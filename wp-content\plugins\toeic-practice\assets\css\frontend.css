/**
 * TOEIC Practice - Frontend Styles
 * 
 * Contents:
 * 1. General Variables & Layout
 * 2. Sidebar Styles
 * 3. Content Area Styles
 * 4. Test Detail Page Styles
 * 5. Loader Styles
 */

@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/7.0.0/css/all.min.css');

:root {
    --wd-primary-color: rgb(232, 78, 72);
    --wd-alternative-color: #fbbc34;
    --wd-link-color: #333333;
    --wd-link-color-hover: #242424;
    --btn-default-bgcolor: #f7f7f7;
    --btn-default-bgcolor-hover: #efefef;
    --btn-accented-bgcolor: rgb(232, 78, 72);
    --btn-accented-bgcolor-hover: rgb(212, 71, 66);
}

* {
    box-sizing: border-box;
}

html {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 16px;
}

body {
    margin: 0;
    padding: 0;
}

a {
    text-decoration: none;
    color: var(--wd-link-color);
}

button {
    cursor: pointer;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Main container layout */
.toeic-container {
    position: relative;
    display: flex;
    height: 100vh;
    margin: 0 auto;
    gap: 30px;
}

/* Sidebar styles */
.toeic-sidebar {
    height: 100%;
    flex: 0 0 250px;
}

.toeic-sidebar__toggle {
    display: none;
    position: absolute;
    top: 20px;
    left: 100%;
    z-index: 1000;
    cursor: pointer;
    width: 24px;
    height: 24px;
    background: black;
    border-radius: 0 50% 50% 0;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 12px;
}

.toeic-sidebar__toggle i {
    transition: 0.1s all ease;
}

.show .toeic-sidebar__toggle i {
    transform: rotate(180deg);
}

.toeic-menu {
    height: 100%;
    background: var(--btn-default-bgcolor);
    border-radius: 5px;
    padding: 20px;
}

.toeic-menu h3 {
    margin-top: 0;
    border-bottom: 1px solid #ddd;
    padding-bottom: 10px;
    color: var(--wd-primary-color);
}

.toeic-menu ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.toeic-menu li {
    margin-bottom: 10px;
}

.toeic-menu a {
    display: block;
    padding: 10px 15px;
    text-decoration: none;
    border-radius: 4px;
    transition: all 0.2s ease;
    color: var(--wd-link-color);
}

.toeic-menu li.active a,
.toeic-menu a:hover {
    background: var(--btn-default-bgcolor-hover);
    color: var(--wd-link-color-hover);
}

/* Content area styles */
.toeic-content {
    flex: 1;
    padding-right: 24px;
}

.toeic-content h1 {
    color: var(--wd-primary-color);
    margin-block: 0;
    margin-bottom: 16px;
}

.toeic-intro {
    margin-bottom: 30px;
}

/* Option cards grid */
.toeic-options {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
}

.toeic-option-card {
    background: #fff;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    padding: 20px;
    transition: transform 0.2s ease;
}

.toeic-option-card:hover {
    transform: translateY(-5px);
}

.toeic-option-card h3 {
    margin-top: 0;
    color: var(--wd-primary-color);
}

/* Button styles */
.toeic-button {
    display: inline-block;
    background: var(--btn-accented-bgcolor);
    color: #fff;
    padding: 8px 15px;
    border-radius: 4px;
    text-decoration: none;
    transition: background 0.2s ease;
    margin-top: 10px;
}

.toeic-button:hover {
    background: var(--btn-accented-bgcolor-hover);
    color: #fff;
}

/* Alternative button style */
.toeic-button-alt {
    background: var(--wd-alternative-color);
}

.toeic-button-alt:hover {
    background: #e9aa25; /* Darker shade of alternative color */
}

/* ==================== */
/* Test Detail Page Styles */
/* ==================== */

/* Test container */
#toeic-test-detail {
    max-width: 900px;
    margin: 0 auto;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 20px;
}

.toeic-test-header {
    text-align: center;
}

/* Timer styles */
.toeic-timer-container {
    margin-bottom: 20px;
    text-align: center;
}

.toeic-timer-progress {
    height: 10px;
    background-color: #e0e0e0;
    border-radius: 5px;
    margin-bottom: 10px;
    overflow: hidden;
}

.toeic-timer-bar {
    height: 100%;
    background-color: #4CAF50;
    width: 100%;
    transition: width 1s linear;
}

.toeic-timer-display {
    font-size: 24px;
    font-weight: bold;
    color: #333;
}

/* Question container */
.toeic-question-container {
    margin-bottom: 30px;
}

.toeic-section-info {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f5f5f5;
    border-radius: 5px;
}

.toeic-section-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
    color: var(--wd-primary-color);
}

.toeic-section-instructions {
    font-style: italic;
}

.toeic-question-number {
    font-weight: bold;
    margin-bottom: 10px;
    color: var(--wd-primary-color);
}

.toeic-question-text {
    margin-bottom: 20px;
    line-height: 1.6;
}

/* Question options */
.toeic-question-options {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.toeic-question-option {
    padding: 12px 15px;
    background-color: #f9f9f9;
    border-radius: 5px;
    border: 1px solid #e0e0e0;
    transition: all 0.2s ease;
}

.toeic-question-option:hover {
    background-color: #f0f0f0;
}

.toeic-question-option input[type="radio"] {
    margin-right: 10px;
}

.toeic-question-option label {
    cursor: pointer;
    display: inline-block;
    width: calc(100% - 30px);
    vertical-align: middle;
}

/* ===== PRONUNCIATION PRACTICE STYLES ===== */

/* Practice Layout */
.pronunciation-practice-content {
    padding: 20px;
}

.practice-header {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #eee;
}

.practice-nav .btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.practice-layout {
    display: flex;
    gap: 30px;
    min-height: 600px;
}

/* Main Practice Area (2/3 width) */
.practice-main {
    flex: 2;
    display: flex;
    flex-direction: column;
    gap: 25px;
}

/* Practice Item Card */
.practice-item-card {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
}

.item-title {
    font-size: 1.4em;
    color: #333;
    margin: 0;
}

.item-meta {
    display: flex;
    gap: 10px;
}

.item-type, .item-difficulty {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.85em;
    font-weight: 500;
}

.item-type {
    background: #e3f2fd;
    color: #1976d2;
}

.item-difficulty {
    background: #f3e5f5;
    color: #7b1fa2;
}

.practice-text {
    font-size: 1.2em;
    line-height: 1.6;
    margin-bottom: 15px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid var(--wd-primary-color);
}

.phonetic-guide {
    font-family: 'Courier New', monospace;
    font-size: 1.1em;
    color: #666;
    padding: 10px;
    background: #fff3cd;
    border-radius: 6px;
    border: 1px solid #ffeaa7;
}

/* Recording Interface */
.recording-interface {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.recording-controls {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.btn-record {
    background: #28a745;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.btn-record:hover:not(:disabled) {
    background: #218838;
    transform: translateY(-1px);
}

.btn-stop {
    background: #dc3545;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.btn-stop:hover:not(:disabled) {
    background: #c82333;
}

.btn-play {
    background: #007bff;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.btn-play:hover:not(:disabled) {
    background: #0056b3;
}

.btn-submit {
    background: var(--wd-primary-color);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.btn-submit:hover:not(:disabled) {
    background: var(--btn-accented-bgcolor-hover);
}

.recording-controls button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

.recording-status {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-text {
    font-weight: 500;
    color: #495057;
}

.recording-timer {
    font-family: 'Courier New', monospace;
    font-size: 1.2em;
    font-weight: bold;
    color: #dc3545;
}

.audio-visualizer {
    text-align: center;
    padding: 10px;
    background: #000;
    border-radius: 8px;
}

#visualizer-canvas {
    border-radius: 4px;
}

/* Rating Results */
.rating-results {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.overall-score {
    display: flex;
    align-items: center;
    gap: 10px;
}

.score-label {
    font-weight: 500;
    color: #495057;
}

.score-value {
    font-size: 1.5em;
    font-weight: bold;
    color: var(--wd-primary-color);
}

.analysis-metrics {
    margin-bottom: 25px;
}

.metric {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.metric-label {
    flex: 0 0 120px;
    font-weight: 500;
    color: #495057;
}

.metric-bar {
    flex: 1;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.metric-fill {
    height: 100%;
    background: linear-gradient(90deg, #28a745, #ffc107, #dc3545);
    width: 0%;
    transition: width 0.8s ease;
}

.metric-value {
    flex: 0 0 50px;
    text-align: right;
    font-weight: bold;
    color: #495057;
}

.word-analysis {
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.feedback-text {
    padding: 15px;
    background: #e7f3ff;
    border-radius: 8px;
    border-left: 4px solid #007bff;
    margin-bottom: 20px;
}

.results-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
}

/* Practice History */
.practice-history {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.practice-history h4 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #495057;
}

/* AI Suggestions Panel (1/3 width) */
.ai-suggestions-panel {
    flex: 1;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    height: fit-content;
    position: sticky;
    top: 20px;
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 20px 15px;
    border-bottom: 1px solid #eee;
}

.panel-header h3 {
    margin: 0;
    color: #495057;
    font-size: 1.1em;
}

.panel-toggle .btn {
    padding: 8px;
    border: none;
    background: #f8f9fa;
    border-radius: 6px;
}

.suggestions-content {
    padding: 20px;
}

/* Quick Actions */
.quick-actions {
    margin-bottom: 25px;
}

.quick-actions h4 {
    margin: 0 0 15px 0;
    font-size: 1em;
    color: #495057;
}

.action-buttons {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
}

.btn-outline {
    background: transparent;
    border: 1px solid #dee2e6;
    color: #495057;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 0.9em;
    transition: all 0.3s ease;
}

.btn-outline:hover {
    background: #f8f9fa;
    border-color: #adb5bd;
}

/* AI Chat */
.ai-chat {
    margin-bottom: 25px;
}

.ai-chat h4 {
    margin: 0 0 15px 0;
    font-size: 1em;
    color: #495057;
}

.chat-messages {
    max-height: 200px;
    overflow-y: auto;
    margin-bottom: 15px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 8px;
}

.chat-message {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.chat-message:last-child {
    margin-bottom: 0;
}

.message-avatar {
    flex: 0 0 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2em;
}

.message-content {
    flex: 1;
    background: #fff;
    padding: 10px 12px;
    border-radius: 8px;
    border: 1px solid #dee2e6;
    font-size: 0.9em;
    line-height: 1.4;
}

.message-content p {
    margin: 0;
}

.chat-input {
    position: relative;
}

.input-group {
    display: flex;
    gap: 8px;
}

.chat-input textarea {
    flex: 1;
    padding: 10px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    resize: vertical;
    font-family: inherit;
    font-size: 0.9em;
}

.chat-input .btn {
    padding: 10px 12px;
    border: none;
    background: var(--wd-primary-color);
    color: white;
    border-radius: 6px;
    align-self: flex-end;
}

/* Suggested Content */

.suggestions-list {
    font-size: 0.9em;
    color: #6c757d;
}

/* Breadcrumb Navigation Styles */
.breadcrumb {
}

.breadcrumb-list {
    display: flex;
    flex-wrap: wrap;
    padding: 0;
    margin: 0;
    list-style: none;
}

.breadcrumb-item {
    font-size: 14px;
    color: #666;
    display: flex;
    align-items: center;
}

.breadcrumb-item a {
    color: var(--wd-link-color);
    text-decoration: none;
    transition: color 0.2s ease;
}

.breadcrumb-item a:hover {
    color: var(--wd-primary-color);
    text-decoration: underline;
}

.breadcrumb-item:not(:last-child)::after {
    content: "›";
    margin: 0 8px;
    color: #999;
    font-size: 16px;
}

.breadcrumb-item.active {
    color: var(--wd-primary-color);
    font-weight: 500;
}

/* Practice Navigation */
.practice-nav { }

.practice-header {
    margin-bottom: 25px;
}

/* Review mode styles */
.toeic-question-option.correct {
    background-color: rgba(76, 175, 80, 0.2);
    border-color: #4CAF50;
}

.toeic-question-option.incorrect {
    background-color: rgba(244, 67, 54, 0.2);
    border-color: #F44336;
}

/* Navigation styles */
.toeic-test-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e0e0e0;
}

.toeic-nav-buttons {
    display: flex;
    gap: 10px;
    font-size: 14px;
}

.toeic-nav-button {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
    transition: background-color 0.2s ease;
    font-size: 14px;
}

#toeic-prev-btn {
    background-color: #f0f0f0;
    color: #333;
}

#toeic-prev-btn:hover {
    background-color: #e0e0e0;
}

#toeic-prev-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

#toeic-next-btn {
    background-color: var(--btn-default-bgcolor);
    color: #333;
}

#toeic-next-btn:hover {
    background-color: var(--btn-default-bgcolor-hover);
}

#toeic-submit-btn {
    background-color: var(--btn-accented-bgcolor);
    color: white;
}

#toeic-submit-btn:hover {
    background-color: var(--btn-accented-bgcolor-hover);
}

/* Question indicators */
.toeic-question-progress {
    display: flex;
    align-items: center;
}

.toeic-question-indicators {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-left: 15px;
}

.toeic-question-indicator {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
}

.toeic-question-indicator.current {
    border: 2px solid var(--wd-primary-color);
}

.toeic-question-indicator.answered {
    background-color: #4CAF50;
    color: white;
}

.toeic-question-indicator.unanswered {
    background-color: #f0f0f0;
    color: #333;
}

/* Results container */
#toeic-results-container {
    display: none;
    text-align: center;
}

.toeic-score {
    margin: 30px auto;
    width: 200px;
    height: 200px;
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
}

.toeic-score.excellent {
    background-color: #4CAF50;
}

.toeic-score.good {
    background-color: #2196F3;
}

.toeic-score.average {
    background-color: #FFC107;
}

.toeic-score.poor {
    background-color: #F44336;
}

.toeic-score-number {
    font-size: 36px;
    font-weight: bold;
}

.toeic-score-percentage {
    font-size: 24px;
}

.toeic-score-breakdown {
    margin: 30px 0;
}

.toeic-score-table {
    width: 100%;
    border-collapse: collapse;
}

.toeic-score-table th,
.toeic-score-table td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid #e0e0e0;
}

.toeic-score-table th {
    background-color: #f5f5f5;
}

.toeic-feedback {
    margin: 30px 0;
    padding: 20px;
    background-color: #f5f5f5;
    border-radius: 5px;
}

#toeic-review-btn {
    background-color: var(--wd-alternative-color);
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
    transition: background-color 0.2s ease;
}

#toeic-review-btn:hover {
    background-color: #e9aa25;
}

/* Pronunciation Practice */

.recording-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 12px;
}

.btn-record {
    border-radius: 9999px;
    padding: 10px 20px;
    background-color: var(--wd-primary-color);
    font-size: 18px;
    color: #fff;
}

.recording-text {
    font-size: 16px;
    color: var(--wd-link-color);
    display: flex;
    align-items: center;
}

.recording-text:before {
    content: '';
    width: 20px;
    height: 20px;
    background-color: var(--wd-primary-color);
    border-radius: 50%;
    display: inline-block;
    margin-right: 8px;
}

.recording-duration {
    display: flex;
    align-items: center;
    gap: 8px;
}

.recording-duration-icon {
    width: 20px;
    height: 20px;
    position: relative;
    display: inline-block;
    margin-right: 8px;
}

.recording-duration-icon::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 10px;
    height: 10px;
    margin: -5px 0 0 -5px;
    border-radius: 50%;
    background-color: var(--wd-primary-color);
    animation: spin 2s linear infinite;
}

@keyframes pulse {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.2); opacity: 0.7; }
    100% { transform: scale(1); opacity: 1; }
}

@keyframes spin {
    0% { 
        box-shadow: 0 0 0 0 rgba(232, 78, 72, 0.7),
                    0 0 0 3px rgba(232, 78, 72, 0.4);
    }
    50% {
        box-shadow: 3px 0 0 0 rgba(232, 78, 72, 0.7),
                    0 3px 0 3px rgba(232, 78, 72, 0.4);
    }
    100% { 
        box-shadow: 0 0 0 0 rgba(232, 78, 72, 0.7),
                    0 0 0 3px rgba(232, 78, 72, 0.4);
    }
}

.recording-duration-text {
    font-size: 16px;
    color: var(--wd-link-color);
}

/* Circular Progress Indicator */
.overall-score {
    display: flex;
    justify-content: center;
    margin: 30px 0;
}

.circular-progress {
    position: relative;
    width: 150px;
    height: 150px;
}

.circular-progress-inner {
    position: relative;
    width: 100%;
    height: 100%;
}

.circular-progress-circle {
    position: relative;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: #f0f0f0;
    overflow: hidden;
}

.circular-progress-mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 50%;
    height: 100%;
    overflow: hidden;
    transform-origin: 100% 50%;
}

.circular-progress-mask.full {
    transform: rotate(180deg);
}

.circular-progress-fill {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--wd-primary-color);
    transform-origin: 100% 50%;
}

.circular-progress-inside {
    position: absolute;
    top: 10px;
    left: 10px;
    right: 10px;
    bottom: 10px;
    background-color: white;
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.circular-progress-percentage {
    font-size: 28px;
    font-weight: bold;
    line-height: 1;
}

.percentage-sign {
    font-size: 16px;
    font-weight: normal;
}

.circular-progress-label {
    font-size: 12px;
    margin-top: 5px;
    color: #666;
}

/* Score color classes for circular progress */
.circular-progress.score-excellent .circular-progress-fill {
    background-color: #4CAF50; /* Green */
}

.circular-progress.score-good .circular-progress-fill {
    background-color: #2196F3; /* Blue */
}

.circular-progress.score-fair .circular-progress-fill {
    background-color: #FFC107; /* Amber */
}

.circular-progress.score-poor .circular-progress-fill {
    background-color: #F44336; /* Red */
}

/* Text score colors */
.score-excellent {
    color: #4CAF50;
}

.score-good {
    color: #2196F3;
}

.score-fair {
    color: #FFC107;
}

.score-poor {
    color: #F44336;
}

/* Metric bar styles */
.metric {
    margin-bottom: 15px;
}

.metric-label {
    display: inline-block;
    width: 120px;
    font-weight: 500;
}

.metric-bar {
    display: inline-block;
    width: calc(100% - 200px);
    height: 12px;
    background-color: #f0f0f0;
    border-radius: 6px;
    overflow: hidden;
    margin: 0 10px;
    vertical-align: middle;
}

.metric-fill {
    height: 100%;
    border-radius: 6px;
    transition: width 0.5s ease-in-out;
}

.metric-fill.score-excellent {
    background-color: #4CAF50;
}

.metric-fill.score-good {
    background-color: #2196F3;
}

.metric-fill.score-fair {
    background-color: #FFC107;
}

.metric-fill.score-poor {
    background-color: #F44336;
}

.metric-value {
    display: inline-block;
    width: 50px;
    text-align: right;
    font-weight: 500;
}

/* Loader */

.toeic-loader-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
}

.toeic-loader-container.show {
    display: flex;
}

.toeic-loader-container.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.toeic-loader {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.toeic-loader.small .toeic-spinner {
    width: 20px;
    height: 20px;
}

.toeic-loader.medium .toeic-spinner {
    width: 40px;
    height: 40px;
}

.toeic-loader.large .toeic-spinner {
    width: 60px;
    height: 60px;
}

.toeic-spinner {
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-left-color: #3498db;
    border-radius: 50%;
    animation: toeic-spin 1s linear infinite;
}

.toeic-loader-text {
    margin-top: 10px;
    font-size: 14px;
    color: #333;
}

@keyframes toeic-spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.toeic-loader-container:not(.fullscreen) .toeic-loader {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

/* AI Assistant */
.suggestion-result {
    margin-bottom: 24px;
}

.suggestion-item {
    border-radius: 8px;
    padding: 12px;
    background: #fff8fd;
    border: 1px solid #e5e7eb;
    margin-bottom: 16px;
}


/* Responsive Tablet */
@media screen and (max-width: 1024px) {
    .toeic-sidebar {
        height: 100%;
        width: min(250px, calc(100% - 32px));
        position: absolute;
        right: 100%;
        transition: 0.3s all ease;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }

    .toeic-sidebar.show {
        transform: translateX(100%);
    }

    .toeic-sidebar__toggle {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .toeic-content {
        padding: 24px;
    }
}


/* Responsive Mobile */
@media (max-width: 768px) {
    .toeic-container {
        flex-direction: column;
    }
    
    .practice-layout {
        flex-direction: column;
    }
    
    .ai-suggestions-panel {
        position: static;
        order: -1;
    }
    
    .recording-controls {
        justify-content: center;
    }
    
    .recording-controls button {
        flex: 1;
        min-width: 120px;
    }
    
    .practice-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .results-actions {
        flex-direction: column;
    }
}