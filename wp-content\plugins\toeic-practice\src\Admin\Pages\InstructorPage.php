<?php
/**
 * Instructors Page
 *
 * @package ToeicPractice\Admin\Pages
 */

namespace ToeicPractice\Admin\Pages;

/**
 * Instructors Page Class
 * 
 * Handles displaying and managing instructors in admin area
 */
class InstructorPage {
    
    /**
     * Instructors per page
     *
     * @var int
     */
    private $per_page = 20;
    
    /**
     * Current page number
     *
     * @var int
     */
    private $current_page = 1;
    
    /**
     * Total number of instructors
     *
     * @var int
     */
    private $total_items = 0;
    
    /**
     * Search query
     *
     * @var string
     */
    private $search = '';
    
    /**
     * Display the instructors page
     */
    public function display() {
        // Process bulk actions
        $this->processBulkActions();
        
        // Process single actions
        $this->processSingleActions();
        
        // Get filters from request
        $this->getFilters();
        
        // Get instructors with pagination and filters
        $instructors = $this->getInstructors();
        
        // Display the page
        $this->renderPage($instructors);
    }
    
    /**
     * Get instructors from database
     */
    private function getInstructors() {
        $args = [
            'role' => 'instructor',
            'number' => $this->per_page,
            'offset' => ($this->current_page - 1) * $this->per_page,
            'orderby' => 'registered',
            'order' => 'DESC'
        ];
        
        if (!empty($this->search)) {
            $args['search'] = '*' . $this->search . '*';
            $args['search_columns'] = ['user_login', 'user_email', 'display_name'];
        }
        
        $user_query = new \WP_User_Query($args);
        $this->total_items = $user_query->get_total();
        
        return $user_query->get_results();
    }
    
    /**
     * Process bulk actions
     */
    private function processBulkActions() {
        if (!isset($_POST['action']) || !in_array($_POST['action'], ['bulk_delete', 'bulk_activate', 'bulk_deactivate'])) {
            return;
        }
        
        // Verify nonce
        if (!isset($_POST['toeic_instructors_nonce']) || !wp_verify_nonce($_POST['toeic_instructors_nonce'], 'bulk_action_instructors')) {
            wp_die(__('Security check failed', 'toeic-practice'));
        }
        
        // Check if any instructors are selected
        if (!isset($_POST['instructors']) || !is_array($_POST['instructors']) || empty($_POST['instructors'])) {
            add_settings_error(
                'toeic_instructors',
                'no_instructors_selected',
                __('No instructors selected.', 'toeic-practice'),
                'error'
            );
            return;
        }
        
        $action = $_POST['action'];
        $processed = 0;
        
        foreach ($_POST['instructors'] as $user_id) {
            $user_id = intval($user_id);
            if ($user_id <= 0) continue;
            
            $user = get_user_by('id', $user_id);
            if (!$user || !in_array('instructor', $user->roles)) continue;
            
            switch ($action) {
                case 'bulk_delete':
                    if (current_user_can('delete_users') && wp_delete_user($user_id)) {
                        $processed++;
                    }
                    break;
                    
                case 'bulk_activate':
                    // Custom activation logic can be added here
                    $processed++;
                    break;
                    
                case 'bulk_deactivate':
                    // Custom deactivation logic can be added here
                    $processed++;
                    break;
            }
        }
        
        // Add success message
        if ($processed > 0) {
            $message = '';
            switch ($action) {
                case 'bulk_delete':
                    $message = sprintf(
                        _n('%d instructor deleted.', '%d instructors deleted.', $processed, 'toeic-practice'),
                        $processed
                    );
                    break;
                case 'bulk_activate':
                    $message = sprintf(
                        _n('%d instructor activated.', '%d instructors activated.', $processed, 'toeic-practice'),
                        $processed
                    );
                    break;
                case 'bulk_deactivate':
                    $message = sprintf(
                        _n('%d instructor deactivated.', '%d instructors deactivated.', $processed, 'toeic-practice'),
                        $processed
                    );
                    break;
            }
            
            add_settings_error('toeic_instructors', 'instructors_processed', $message, 'success');
        }
    }
    
    /**
     * Process single actions
     */
    private function processSingleActions() {
        $action = isset($_GET['action']) ? sanitize_text_field($_GET['action']) : '';
        $user_id = isset($_GET['user_id']) ? intval($_GET['user_id']) : 0;
        
        if (empty($action) || $user_id <= 0) {
            return;
        }
        
        // Verify nonce
        if (!isset($_GET['_wpnonce']) || !wp_verify_nonce($_GET['_wpnonce'], 'instructor_action_' . $user_id)) {
            wp_die(__('Security check failed', 'toeic-practice'));
        }
        
        $user = get_user_by('id', $user_id);
        if (!$user || !in_array('instructor', $user->roles)) {
            wp_die(__('Invalid instructor.', 'toeic-practice'));
        }
        
        switch ($action) {
            case 'delete':
                if (current_user_can('delete_users') && wp_delete_user($user_id)) {
                    add_settings_error(
                        'toeic_instructors',
                        'instructor_deleted',
                        __('Instructor deleted successfully.', 'toeic-practice'),
                        'success'
                    );
                }
                break;
                
            case 'edit':
                // Redirect to WordPress user edit page
                wp_redirect(admin_url('user-edit.php?user_id=' . $user_id));
                exit;
                break;
        }
    }
    
    /**
     * Get filters from request
     */
    private function getFilters() {
        $this->current_page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
        $this->search = isset($_GET['s']) ? sanitize_text_field($_GET['s']) : '';
    }
    
    /**
     * Render the page
     */
    private function renderPage($instructors) {
        ?>
        <div class="wrap">
            <h1 class="wp-heading-inline"><?php _e('Instructors', 'toeic-practice'); ?></h1>
            
            <a href="<?php echo admin_url('user-new.php?role=instructor'); ?>" class="page-title-action">
                <?php _e('Add New Instructor', 'toeic-practice'); ?>
            </a>
            
            <hr class="wp-header-end">
            
            <?php settings_errors('toeic_instructors'); ?>
            
            <!-- Search Form -->
            <form method="get" class="search-form">
                <input type="hidden" name="page" value="toeic-practice-instructors">
                <p class="search-box">
                    <label class="screen-reader-text" for="instructor-search-input"><?php _e('Search Instructors:', 'toeic-practice'); ?></label>
                    <input type="search" id="instructor-search-input" name="s" value="<?php echo esc_attr($this->search); ?>" placeholder="<?php _e('Search instructors...', 'toeic-practice'); ?>">
                    <input type="submit" id="search-submit" class="button" value="<?php _e('Search Instructors', 'toeic-practice'); ?>">
                </p>
            </form>
            
            <!-- Instructors Table -->
            <form method="post">
                <?php wp_nonce_field('bulk_action_instructors', 'toeic_instructors_nonce'); ?>
                
                <div class="tablenav top">
                    <div class="alignleft actions bulkactions">
                        <label for="bulk-action-selector-top" class="screen-reader-text"><?php _e('Select bulk action', 'toeic-practice'); ?></label>
                        <select name="action" id="bulk-action-selector-top">
                            <option value="-1"><?php _e('Bulk Actions', 'toeic-practice'); ?></option>
                            <option value="bulk_delete"><?php _e('Delete', 'toeic-practice'); ?></option>
                        </select>
                        <input type="submit" id="doaction" class="button action" value="<?php _e('Apply', 'toeic-practice'); ?>">
                    </div>
                    
                    <?php $this->renderPagination(); ?>
                </div>
                
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <td id="cb" class="manage-column column-cb check-column">
                                <label class="screen-reader-text" for="cb-select-all-1"><?php _e('Select All', 'toeic-practice'); ?></label>
                                <input id="cb-select-all-1" type="checkbox">
                            </td>
                            <th scope="col" class="manage-column column-username column-primary">
                                <?php _e('Username', 'toeic-practice'); ?>
                            </th>
                            <th scope="col" class="manage-column column-name">
                                <?php _e('Name', 'toeic-practice'); ?>
                            </th>
                            <th scope="col" class="manage-column column-email">
                                <?php _e('Email', 'toeic-practice'); ?>
                            </th>
                            <th scope="col" class="manage-column column-registered">
                                <?php _e('Registered', 'toeic-practice'); ?>
                            </th>
                            <th scope="col" class="manage-column column-posts">
                                <?php _e('Classes Teaching', 'toeic-practice'); ?>
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($instructors)): ?>
                            <tr class="no-items">
                                <td class="colspanchange" colspan="6">
                                    <?php _e('No instructors found.', 'toeic-practice'); ?>
                                </td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($instructors as $instructor): ?>
                                <?php $this->renderInstructorRow($instructor); ?>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
                
                <div class="tablenav bottom">
                    <?php $this->renderPagination(); ?>
                </div>
            </form>
        </div>
        <?php
    }
    
    /**
     * Render instructor row
     */
    private function renderInstructorRow($instructor) {
        $edit_url = admin_url('user-edit.php?user_id=' . $instructor->ID);
        $delete_url = wp_nonce_url(
            admin_url('admin.php?page=toeic-practice-instructors&action=delete&user_id=' . $instructor->ID),
            'instructor_action_' . $instructor->ID
        );
        
        // Get classes count
        global $wpdb;
        $classes_count = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$wpdb->prefix}class WHERE instructor_id = %d",
            $instructor->ID
        ));
        
        ?>
        <tr>
            <th scope="row" class="check-column">
                <input type="checkbox" name="instructors[]" value="<?php echo esc_attr($instructor->ID); ?>">
            </th>
            <td class="username column-username column-primary" data-colname="<?php _e('Username', 'toeic-practice'); ?>">
                <strong>
                    <a href="<?php echo esc_url($edit_url); ?>">
                        <?php echo esc_html($instructor->user_login); ?>
                    </a>
                </strong>
                <div class="row-actions">
                    <span class="edit">
                        <a href="<?php echo esc_url($edit_url); ?>"><?php _e('Edit', 'toeic-practice'); ?></a> |
                    </span>
                    <span class="delete">
                        <a href="<?php echo esc_url($delete_url); ?>" 
                           onclick="return confirm('<?php _e('Are you sure you want to delete this instructor?', 'toeic-practice'); ?>')">
                            <?php _e('Delete', 'toeic-practice'); ?>
                        </a>
                    </span>
                </div>
            </td>
            <td class="name column-name" data-colname="<?php _e('Name', 'toeic-practice'); ?>">
                <?php echo esc_html($instructor->display_name); ?>
            </td>
            <td class="email column-email" data-colname="<?php _e('Email', 'toeic-practice'); ?>">
                <a href="mailto:<?php echo esc_attr($instructor->user_email); ?>">
                    <?php echo esc_html($instructor->user_email); ?>
                </a>
            </td>
            <td class="registered column-registered" data-colname="<?php _e('Registered', 'toeic-practice'); ?>">
                <?php echo date_i18n(get_option('date_format'), strtotime($instructor->user_registered)); ?>
            </td>
            <td class="posts column-posts" data-colname="<?php _e('Classes Teaching', 'toeic-practice'); ?>">
                <?php echo intval($classes_count); ?>
            </td>
        </tr>
        <?php
    }
    
    /**
     * Render pagination
     */
    private function renderPagination() {
        $total_pages = ceil($this->total_items / $this->per_page);
        
        if ($total_pages <= 1) {
            return;
        }
        
        $pagination_args = [
            'base' => add_query_arg('paged', '%#%'),
            'format' => '',
            'prev_text' => __('&laquo;'),
            'next_text' => __('&raquo;'),
            'total' => $total_pages,
            'current' => $this->current_page,
            'type' => 'plain'
        ];
        
        echo '<div class="tablenav-pages">';
        echo paginate_links($pagination_args);
        echo '</div>';
    }
}
