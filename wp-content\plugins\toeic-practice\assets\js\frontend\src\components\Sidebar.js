/**
 * Sidebar Component
 * 
 * Handles the left sidebar navigation that's common across all pages
 * in the TOEIC Practice plugin frontend.
 */
class Sidebar {
    /**
     * Constructor
     * 
     * @param {HTMLElement} element - The sidebar container element
     */
    constructor(element) {
        this.element = element;
        this.menuItems = this.element.querySelectorAll('.toeic-menu-item');
        this.sidebarToggle = this.element.querySelector('.toeic-sidebar__toggle');
    }

    /**
     * Initialize the sidebar
     */
    init() {
        this.setupEventListeners();
        this.highlightCurrentPage();
    }

    /**
     * Set up event listeners for menu items
     */
    setupEventListeners() {
        this.menuItems.forEach(item => {
            item.addEventListener('click', this.handleMenuItemClick.bind(this));
        });

        this.element.addEventListener('click', (e) => {
            e.stopPropagation();
        });
        this.sidebarToggle.addEventListener('click', this.handleSidebarToggle.bind(this));

        // when click on document, hide sidebar
        document.addEventListener('click', this.handleDocumentClick.bind(this));
    }

    /**
     * Handle document click
     */
    handleDocumentClick() {
        this.element.classList.remove('show');
    }

    /**
     * Handle sidebar toggle
     */
    handleSidebarToggle() {
        this.element.classList.toggle('show');
    }

    /**
     * Handle menu item click
     * 
     * @param {Event} event - Click event
     */
    handleMenuItemClick(event) {
        // Remove active class from all menu items
        this.menuItems.forEach(item => {
            item.classList.remove('active');
        });

        // Add active class to clicked menu item
        event.currentTarget.classList.add('active');
    }

    /**
     * Highlight the current page in the menu
     */
    highlightCurrentPage() {
        // Get current page path
        const currentPath = window.location.pathname;
        
        // Find and highlight the corresponding menu item
        this.menuItems.forEach(item => {
            const link = item.querySelector('a');
            if (link && link.getAttribute('href') === currentPath) {
                item.classList.add('active');
            }
        });
    }
}

export default Sidebar;
