[04-Aug-2025 05:08:18 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[04-Aug-2025 05:08:18 UTC] PHP Stack trace:
[04-Aug-2025 05:08:18 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:0
[04-Aug-2025 05:08:18 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:22
[04-Aug-2025 05:08:18 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[04-Aug-2025 05:08:18 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[04-Aug-2025 05:08:18 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[04-Aug-2025 05:08:18 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[04-Aug-2025 05:08:18 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[04-Aug-2025 05:08:18 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[04-Aug-2025 05:08:18 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[04-Aug-2025 05:08:18 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[04-Aug-2025 05:08:18 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[04-Aug-2025 05:08:18 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[04-Aug-2025 05:08:18 UTC] PHP Deprecated:  Optional parameter $search declared before required parameter $table is implicitly treated as a required parameter in D:\wamp64\www\toeictest\wp-content\plugins\search-and-replace\inc\Database\Exporter.php on line 295
[04-Aug-2025 05:08:18 UTC] PHP Stack trace:
[04-Aug-2025 05:08:18 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:0
[04-Aug-2025 05:08:18 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:22
[04-Aug-2025 05:08:18 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[04-Aug-2025 05:08:18 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[04-Aug-2025 05:08:18 UTC] PHP   5. do_action($hook_name = 'plugins_loaded') D:\wamp64\www\toeictest\wp-settings.php:578
[04-Aug-2025 05:08:18 UTC] PHP   6. WP_Hook->do_action($args = [0 => '']) D:\wamp64\www\toeictest\wp-includes\plugin.php:517
[04-Aug-2025 05:08:18 UTC] PHP   7. WP_Hook->apply_filters($value = '', $args = [0 => '']) D:\wamp64\www\toeictest\wp-includes\class-wp-hook.php:348
[04-Aug-2025 05:08:18 UTC] PHP   8. search_replace_load('') D:\wamp64\www\toeictest\wp-includes\class-wp-hook.php:324
[04-Aug-2025 05:08:18 UTC] PHP   9. Composer\Autoload\ClassLoader->loadClass($class = 'Inpsyde\\SearchReplace\\Database\\Exporter') D:\wamp64\www\toeictest\wp-content\plugins\search-and-replace\inpsyde-search-replace.php:97
[04-Aug-2025 05:08:18 UTC] PHP  10. Composer\Autoload\{closure:D:\wamp64\www\toeictest\wp-content\plugins\safe-svg\vendor\composer\ClassLoader.php:575-577}($file = 'D:\\wamp64\\www\\toeictest\\wp-content\\plugins\\search-and-replace\\vendor\\composer/../../inc/Database/Exporter.php') D:\wamp64\www\toeictest\wp-content\plugins\safe-svg\vendor\composer\ClassLoader.php:427
[04-Aug-2025 05:08:18 UTC] PHP Deprecated:  Optional parameter $replace declared before required parameter $table is implicitly treated as a required parameter in D:\wamp64\www\toeictest\wp-content\plugins\search-and-replace\inc\Database\Exporter.php on line 295
[04-Aug-2025 05:08:18 UTC] PHP Stack trace:
[04-Aug-2025 05:08:18 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:0
[04-Aug-2025 05:08:18 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:22
[04-Aug-2025 05:08:18 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[04-Aug-2025 05:08:18 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[04-Aug-2025 05:08:18 UTC] PHP   5. do_action($hook_name = 'plugins_loaded') D:\wamp64\www\toeictest\wp-settings.php:578
[04-Aug-2025 05:08:18 UTC] PHP   6. WP_Hook->do_action($args = [0 => '']) D:\wamp64\www\toeictest\wp-includes\plugin.php:517
[04-Aug-2025 05:08:18 UTC] PHP   7. WP_Hook->apply_filters($value = '', $args = [0 => '']) D:\wamp64\www\toeictest\wp-includes\class-wp-hook.php:348
[04-Aug-2025 05:08:18 UTC] PHP   8. search_replace_load('') D:\wamp64\www\toeictest\wp-includes\class-wp-hook.php:324
[04-Aug-2025 05:08:18 UTC] PHP   9. Composer\Autoload\ClassLoader->loadClass($class = 'Inpsyde\\SearchReplace\\Database\\Exporter') D:\wamp64\www\toeictest\wp-content\plugins\search-and-replace\inpsyde-search-replace.php:97
[04-Aug-2025 05:08:18 UTC] PHP  10. Composer\Autoload\{closure:D:\wamp64\www\toeictest\wp-content\plugins\safe-svg\vendor\composer\ClassLoader.php:575-577}($file = 'D:\\wamp64\\www\\toeictest\\wp-content\\plugins\\search-and-replace\\vendor\\composer/../../inc/Database/Exporter.php') D:\wamp64\www\toeictest\wp-content\plugins\safe-svg\vendor\composer\ClassLoader.php:427
[04-Aug-2025 05:08:19 UTC] PHP Warning:  Undefined array key "item_type" in D:\wamp64\www\toeictest\wp-content\plugins\toeic-practice\src\Ajax\PronunciationHandler.php on line 208
[04-Aug-2025 05:08:19 UTC] PHP Stack trace:
[04-Aug-2025 05:08:19 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:0
[04-Aug-2025 05:08:19 UTC] PHP   2. do_action($hook_name = 'wp_ajax_grade_pronunciation') D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:192
[04-Aug-2025 05:08:19 UTC] PHP   3. WP_Hook->do_action($args = [0 => '']) D:\wamp64\www\toeictest\wp-includes\plugin.php:517
[04-Aug-2025 05:08:19 UTC] PHP   4. WP_Hook->apply_filters($value = '', $args = [0 => '']) D:\wamp64\www\toeictest\wp-includes\class-wp-hook.php:348
[04-Aug-2025 05:08:19 UTC] PHP   5. ToeicPractice\Ajax\PronunciationHandler->handlePronunciationGrading('') D:\wamp64\www\toeictest\wp-includes\class-wp-hook.php:324
[04-Aug-2025 05:08:19 UTC] PHP   6. ToeicPractice\Ajax\PronunciationHandler->getExpectedText($item_data = ['id' => '1', 'word' => 'Dog', 'vi_translate' => 'Con chó', 'audio_link' => 'http://toeictest.local/wp-content/uploads/2025/07/ukcaste011-2.mp3', 'category_id' => '0', 'example_sentence' => '0', 'created_at' => '2025-07-14 15:21:44', 'updated_at' => '2025-07-17 11:48:29']) D:\wamp64\www\toeictest\wp-content\plugins\toeic-practice\src\Ajax\PronunciationHandler.php:69
[04-Aug-2025 05:08:19 UTC] Azure pronunciation assessment cURL error: SSL certificate problem: unable to get local issuer certificate
[04-Aug-2025 05:08:19 UTC] Azure pronunciation assessment failed, falling back to mock implementation
[04-Aug-2025 05:08:44 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[04-Aug-2025 05:08:44 UTC] PHP Stack trace:
[04-Aug-2025 05:08:44 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:0
[04-Aug-2025 05:08:44 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:22
[04-Aug-2025 05:08:44 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[04-Aug-2025 05:08:44 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[04-Aug-2025 05:08:44 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[04-Aug-2025 05:08:44 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[04-Aug-2025 05:08:44 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[04-Aug-2025 05:08:44 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[04-Aug-2025 05:08:44 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[04-Aug-2025 05:08:44 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[04-Aug-2025 05:08:44 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[04-Aug-2025 05:08:44 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[04-Aug-2025 05:08:44 UTC] PHP Deprecated:  Optional parameter $search declared before required parameter $table is implicitly treated as a required parameter in D:\wamp64\www\toeictest\wp-content\plugins\search-and-replace\inc\Database\Exporter.php on line 295
[04-Aug-2025 05:08:44 UTC] PHP Stack trace:
[04-Aug-2025 05:08:44 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:0
[04-Aug-2025 05:08:44 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:22
[04-Aug-2025 05:08:44 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[04-Aug-2025 05:08:44 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[04-Aug-2025 05:08:44 UTC] PHP   5. do_action($hook_name = 'plugins_loaded') D:\wamp64\www\toeictest\wp-settings.php:578
[04-Aug-2025 05:08:44 UTC] PHP   6. WP_Hook->do_action($args = [0 => '']) D:\wamp64\www\toeictest\wp-includes\plugin.php:517
[04-Aug-2025 05:08:44 UTC] PHP   7. WP_Hook->apply_filters($value = '', $args = [0 => '']) D:\wamp64\www\toeictest\wp-includes\class-wp-hook.php:348
[04-Aug-2025 05:08:44 UTC] PHP   8. search_replace_load('') D:\wamp64\www\toeictest\wp-includes\class-wp-hook.php:324
[04-Aug-2025 05:08:44 UTC] PHP   9. Composer\Autoload\ClassLoader->loadClass($class = 'Inpsyde\\SearchReplace\\Database\\Exporter') D:\wamp64\www\toeictest\wp-content\plugins\search-and-replace\inpsyde-search-replace.php:97
[04-Aug-2025 05:08:44 UTC] PHP  10. Composer\Autoload\{closure:D:\wamp64\www\toeictest\wp-content\plugins\safe-svg\vendor\composer\ClassLoader.php:575-577}($file = 'D:\\wamp64\\www\\toeictest\\wp-content\\plugins\\search-and-replace\\vendor\\composer/../../inc/Database/Exporter.php') D:\wamp64\www\toeictest\wp-content\plugins\safe-svg\vendor\composer\ClassLoader.php:427
[04-Aug-2025 05:08:44 UTC] PHP Deprecated:  Optional parameter $replace declared before required parameter $table is implicitly treated as a required parameter in D:\wamp64\www\toeictest\wp-content\plugins\search-and-replace\inc\Database\Exporter.php on line 295
[04-Aug-2025 05:08:44 UTC] PHP Stack trace:
[04-Aug-2025 05:08:44 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:0
[04-Aug-2025 05:08:44 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:22
[04-Aug-2025 05:08:44 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[04-Aug-2025 05:08:44 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[04-Aug-2025 05:08:44 UTC] PHP   5. do_action($hook_name = 'plugins_loaded') D:\wamp64\www\toeictest\wp-settings.php:578
[04-Aug-2025 05:08:44 UTC] PHP   6. WP_Hook->do_action($args = [0 => '']) D:\wamp64\www\toeictest\wp-includes\plugin.php:517
[04-Aug-2025 05:08:44 UTC] PHP   7. WP_Hook->apply_filters($value = '', $args = [0 => '']) D:\wamp64\www\toeictest\wp-includes\class-wp-hook.php:348
[04-Aug-2025 05:08:44 UTC] PHP   8. search_replace_load('') D:\wamp64\www\toeictest\wp-includes\class-wp-hook.php:324
[04-Aug-2025 05:08:44 UTC] PHP   9. Composer\Autoload\ClassLoader->loadClass($class = 'Inpsyde\\SearchReplace\\Database\\Exporter') D:\wamp64\www\toeictest\wp-content\plugins\search-and-replace\inpsyde-search-replace.php:97
[04-Aug-2025 05:08:44 UTC] PHP  10. Composer\Autoload\{closure:D:\wamp64\www\toeictest\wp-content\plugins\safe-svg\vendor\composer\ClassLoader.php:575-577}($file = 'D:\\wamp64\\www\\toeictest\\wp-content\\plugins\\search-and-replace\\vendor\\composer/../../inc/Database/Exporter.php') D:\wamp64\www\toeictest\wp-content\plugins\safe-svg\vendor\composer\ClassLoader.php:427
[04-Aug-2025 05:08:45 UTC] Array
(
    [id] => 1
    [word] => Dog
    [vi_translate] => Con chó
    [audio_link] => http://toeictest.local/wp-content/uploads/2025/07/ukcaste011-2.mp3
    [category_id] => 0
    [example_sentence] => 0
    [created_at] => 2025-07-14 15:21:44
    [updated_at] => 2025-07-17 11:48:29
)

[04-Aug-2025 05:08:45 UTC] PHP Warning:  Undefined array key "item_type" in D:\wamp64\www\toeictest\wp-content\plugins\toeic-practice\src\Ajax\PronunciationHandler.php on line 209
[04-Aug-2025 05:08:45 UTC] PHP Stack trace:
[04-Aug-2025 05:08:45 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:0
[04-Aug-2025 05:08:45 UTC] PHP   2. do_action($hook_name = 'wp_ajax_grade_pronunciation') D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:192
[04-Aug-2025 05:08:45 UTC] PHP   3. WP_Hook->do_action($args = [0 => '']) D:\wamp64\www\toeictest\wp-includes\plugin.php:517
[04-Aug-2025 05:08:45 UTC] PHP   4. WP_Hook->apply_filters($value = '', $args = [0 => '']) D:\wamp64\www\toeictest\wp-includes\class-wp-hook.php:348
[04-Aug-2025 05:08:45 UTC] PHP   5. ToeicPractice\Ajax\PronunciationHandler->handlePronunciationGrading('') D:\wamp64\www\toeictest\wp-includes\class-wp-hook.php:324
[04-Aug-2025 05:08:45 UTC] PHP   6. ToeicPractice\Ajax\PronunciationHandler->getExpectedText($item_data = ['id' => '1', 'word' => 'Dog', 'vi_translate' => 'Con chó', 'audio_link' => 'http://toeictest.local/wp-content/uploads/2025/07/ukcaste011-2.mp3', 'category_id' => '0', 'example_sentence' => '0', 'created_at' => '2025-07-14 15:21:44', 'updated_at' => '2025-07-17 11:48:29']) D:\wamp64\www\toeictest\wp-content\plugins\toeic-practice\src\Ajax\PronunciationHandler.php:69
[04-Aug-2025 05:08:45 UTC] Azure pronunciation assessment cURL error: SSL certificate problem: unable to get local issuer certificate
[04-Aug-2025 05:08:45 UTC] Azure pronunciation assessment failed, falling back to mock implementation
[04-Aug-2025 05:12:38 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[04-Aug-2025 05:12:38 UTC] PHP Stack trace:
[04-Aug-2025 05:12:38 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:0
[04-Aug-2025 05:12:38 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:22
[04-Aug-2025 05:12:38 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[04-Aug-2025 05:12:38 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[04-Aug-2025 05:12:38 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[04-Aug-2025 05:12:38 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[04-Aug-2025 05:12:38 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[04-Aug-2025 05:12:38 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[04-Aug-2025 05:12:38 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[04-Aug-2025 05:12:38 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[04-Aug-2025 05:12:38 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[04-Aug-2025 05:12:38 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[04-Aug-2025 05:12:38 UTC] PHP Deprecated:  Optional parameter $search declared before required parameter $table is implicitly treated as a required parameter in D:\wamp64\www\toeictest\wp-content\plugins\search-and-replace\inc\Database\Exporter.php on line 295
[04-Aug-2025 05:12:38 UTC] PHP Stack trace:
[04-Aug-2025 05:12:38 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:0
[04-Aug-2025 05:12:38 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:22
[04-Aug-2025 05:12:38 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[04-Aug-2025 05:12:38 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[04-Aug-2025 05:12:38 UTC] PHP   5. do_action($hook_name = 'plugins_loaded') D:\wamp64\www\toeictest\wp-settings.php:578
[04-Aug-2025 05:12:38 UTC] PHP   6. WP_Hook->do_action($args = [0 => '']) D:\wamp64\www\toeictest\wp-includes\plugin.php:517
[04-Aug-2025 05:12:38 UTC] PHP   7. WP_Hook->apply_filters($value = '', $args = [0 => '']) D:\wamp64\www\toeictest\wp-includes\class-wp-hook.php:348
[04-Aug-2025 05:12:38 UTC] PHP   8. search_replace_load('') D:\wamp64\www\toeictest\wp-includes\class-wp-hook.php:324
[04-Aug-2025 05:12:38 UTC] PHP   9. Composer\Autoload\ClassLoader->loadClass($class = 'Inpsyde\\SearchReplace\\Database\\Exporter') D:\wamp64\www\toeictest\wp-content\plugins\search-and-replace\inpsyde-search-replace.php:97
[04-Aug-2025 05:12:38 UTC] PHP  10. Composer\Autoload\{closure:D:\wamp64\www\toeictest\wp-content\plugins\safe-svg\vendor\composer\ClassLoader.php:575-577}($file = 'D:\\wamp64\\www\\toeictest\\wp-content\\plugins\\search-and-replace\\vendor\\composer/../../inc/Database/Exporter.php') D:\wamp64\www\toeictest\wp-content\plugins\safe-svg\vendor\composer\ClassLoader.php:427
[04-Aug-2025 05:12:38 UTC] PHP Deprecated:  Optional parameter $replace declared before required parameter $table is implicitly treated as a required parameter in D:\wamp64\www\toeictest\wp-content\plugins\search-and-replace\inc\Database\Exporter.php on line 295
[04-Aug-2025 05:12:38 UTC] PHP Stack trace:
[04-Aug-2025 05:12:38 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:0
[04-Aug-2025 05:12:38 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:22
[04-Aug-2025 05:12:38 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[04-Aug-2025 05:12:38 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[04-Aug-2025 05:12:38 UTC] PHP   5. do_action($hook_name = 'plugins_loaded') D:\wamp64\www\toeictest\wp-settings.php:578
[04-Aug-2025 05:12:38 UTC] PHP   6. WP_Hook->do_action($args = [0 => '']) D:\wamp64\www\toeictest\wp-includes\plugin.php:517
[04-Aug-2025 05:12:38 UTC] PHP   7. WP_Hook->apply_filters($value = '', $args = [0 => '']) D:\wamp64\www\toeictest\wp-includes\class-wp-hook.php:348
[04-Aug-2025 05:12:38 UTC] PHP   8. search_replace_load('') D:\wamp64\www\toeictest\wp-includes\class-wp-hook.php:324
[04-Aug-2025 05:12:38 UTC] PHP   9. Composer\Autoload\ClassLoader->loadClass($class = 'Inpsyde\\SearchReplace\\Database\\Exporter') D:\wamp64\www\toeictest\wp-content\plugins\search-and-replace\inpsyde-search-replace.php:97
[04-Aug-2025 05:12:38 UTC] PHP  10. Composer\Autoload\{closure:D:\wamp64\www\toeictest\wp-content\plugins\safe-svg\vendor\composer\ClassLoader.php:575-577}($file = 'D:\\wamp64\\www\\toeictest\\wp-content\\plugins\\search-and-replace\\vendor\\composer/../../inc/Database/Exporter.php') D:\wamp64\www\toeictest\wp-content\plugins\safe-svg\vendor\composer\ClassLoader.php:427
[04-Aug-2025 05:12:38 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[04-Aug-2025 05:12:38 UTC] PHP Stack trace:
[04-Aug-2025 05:12:38 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-cron.php:0
[04-Aug-2025 05:12:38 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-cron.php:46
[04-Aug-2025 05:12:38 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[04-Aug-2025 05:12:38 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[04-Aug-2025 05:12:38 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[04-Aug-2025 05:12:38 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[04-Aug-2025 05:12:38 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[04-Aug-2025 05:12:38 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[04-Aug-2025 05:12:38 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[04-Aug-2025 05:12:38 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[04-Aug-2025 05:12:38 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[04-Aug-2025 05:12:38 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[04-Aug-2025 05:12:39 UTC] Azure pronunciation assessment cURL error: SSL certificate problem: unable to get local issuer certificate
[04-Aug-2025 05:12:39 UTC] Azure pronunciation assessment failed, falling back to mock implementation
[04-Aug-2025 05:13:28 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[04-Aug-2025 05:13:28 UTC] PHP Stack trace:
[04-Aug-2025 05:13:28 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:0
[04-Aug-2025 05:13:28 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:22
[04-Aug-2025 05:13:28 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[04-Aug-2025 05:13:28 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[04-Aug-2025 05:13:28 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[04-Aug-2025 05:13:28 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[04-Aug-2025 05:13:28 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[04-Aug-2025 05:13:28 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[04-Aug-2025 05:13:28 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[04-Aug-2025 05:13:28 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1379
[04-Aug-2025 05:13:28 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[04-Aug-2025 05:13:28 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[04-Aug-2025 05:13:28 UTC] PHP Deprecated:  Optional parameter $search declared before required parameter $table is implicitly treated as a required parameter in D:\wamp64\www\toeictest\wp-content\plugins\search-and-replace\inc\Database\Exporter.php on line 295
[04-Aug-2025 05:13:28 UTC] PHP Stack trace:
[04-Aug-2025 05:13:28 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:0
[04-Aug-2025 05:13:28 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:22
[04-Aug-2025 05:13:28 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[04-Aug-2025 05:13:28 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[04-Aug-2025 05:13:28 UTC] PHP   5. do_action($hook_name = 'plugins_loaded') D:\wamp64\www\toeictest\wp-settings.php:578
[04-Aug-2025 05:13:28 UTC] PHP   6. WP_Hook->do_action($args = [0 => '']) D:\wamp64\www\toeictest\wp-includes\plugin.php:517
[04-Aug-2025 05:13:28 UTC] PHP   7. WP_Hook->apply_filters($value = '', $args = [0 => '']) D:\wamp64\www\toeictest\wp-includes\class-wp-hook.php:348
[04-Aug-2025 05:13:28 UTC] PHP   8. search_replace_load('') D:\wamp64\www\toeictest\wp-includes\class-wp-hook.php:324
[04-Aug-2025 05:13:28 UTC] PHP   9. Composer\Autoload\ClassLoader->loadClass($class = 'Inpsyde\\SearchReplace\\Database\\Exporter') D:\wamp64\www\toeictest\wp-content\plugins\search-and-replace\inpsyde-search-replace.php:97
[04-Aug-2025 05:13:28 UTC] PHP  10. Composer\Autoload\{closure:D:\wamp64\www\toeictest\wp-content\plugins\safe-svg\vendor\composer\ClassLoader.php:575-577}($file = 'D:\\wamp64\\www\\toeictest\\wp-content\\plugins\\search-and-replace\\vendor\\composer/../../inc/Database/Exporter.php') D:\wamp64\www\toeictest\wp-content\plugins\safe-svg\vendor\composer\ClassLoader.php:427
[04-Aug-2025 05:13:28 UTC] PHP Deprecated:  Optional parameter $replace declared before required parameter $table is implicitly treated as a required parameter in D:\wamp64\www\toeictest\wp-content\plugins\search-and-replace\inc\Database\Exporter.php on line 295
[04-Aug-2025 05:13:28 UTC] PHP Stack trace:
[04-Aug-2025 05:13:28 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:0
[04-Aug-2025 05:13:28 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:22
[04-Aug-2025 05:13:28 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[04-Aug-2025 05:13:28 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[04-Aug-2025 05:13:28 UTC] PHP   5. do_action($hook_name = 'plugins_loaded') D:\wamp64\www\toeictest\wp-settings.php:578
[04-Aug-2025 05:13:28 UTC] PHP   6. WP_Hook->do_action($args = [0 => '']) D:\wamp64\www\toeictest\wp-includes\plugin.php:517
[04-Aug-2025 05:13:28 UTC] PHP   7. WP_Hook->apply_filters($value = '', $args = [0 => '']) D:\wamp64\www\toeictest\wp-includes\class-wp-hook.php:348
[04-Aug-2025 05:13:28 UTC] PHP   8. search_replace_load('') D:\wamp64\www\toeictest\wp-includes\class-wp-hook.php:324
[04-Aug-2025 05:13:28 UTC] PHP   9. Composer\Autoload\ClassLoader->loadClass($class = 'Inpsyde\\SearchReplace\\Database\\Exporter') D:\wamp64\www\toeictest\wp-content\plugins\search-and-replace\inpsyde-search-replace.php:97
[04-Aug-2025 05:13:28 UTC] PHP  10. Composer\Autoload\{closure:D:\wamp64\www\toeictest\wp-content\plugins\safe-svg\vendor\composer\ClassLoader.php:575-577}($file = 'D:\\wamp64\\www\\toeictest\\wp-content\\plugins\\search-and-replace\\vendor\\composer/../../inc/Database/Exporter.php') D:\wamp64\www\toeictest\wp-content\plugins\safe-svg\vendor\composer\ClassLoader.php:427
[04-Aug-2025 05:13:29 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[04-Aug-2025 05:13:29 UTC] PHP Stack trace:
[04-Aug-2025 05:13:29 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-cron.php:0
[04-Aug-2025 05:13:29 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-cron.php:46
[04-Aug-2025 05:13:29 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[04-Aug-2025 05:13:29 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[04-Aug-2025 05:13:29 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[04-Aug-2025 05:13:29 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[04-Aug-2025 05:13:29 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[04-Aug-2025 05:13:29 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[04-Aug-2025 05:13:29 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[04-Aug-2025 05:13:29 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1379
[04-Aug-2025 05:13:29 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[04-Aug-2025 05:13:29 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[04-Aug-2025 05:13:31 UTC] =======================================
[04-Aug-2025 05:13:31 UTC] Pronunciation config: 
[04-Aug-2025 05:13:31 UTC] Array
(
    [ReferenceText] => 
    [GradingSystem] => HundredMark
    [Granularity] => Word
    [Dimension] => Comprehensive
    [EnableMiscue] => 1
)

[04-Aug-2025 05:13:31 UTC] =======================================
[04-Aug-2025 05:13:31 UTC] Azure pronunciation assessment result: 
[04-Aug-2025 05:13:31 UTC] Array
(
    [RecognitionStatus] => Success
    [Offset] => 12700000
    [Duration] => 3500000
    [DisplayText] => No.
    [SNR] => 18.427652
    [NBest] => Array
        (
            [0] => Array
                (
                    [Confidence] => 0.179894
                    [Lexical] => no
                    [ITN] => no
                    [MaskedITN] => no
                    [Display] => No.
                    [AccuracyScore] => 100
                    [FluencyScore] => 100
                    [CompletenessScore] => 100
                    [PronScore] => 100
                    [Words] => Array
                        (
                            [0] => Array
                                (
                                    [Word] => no
                                    [Offset] => 12700000
                                    [Duration] => 3500000
                                    [Confidence] => 0
                                    [AccuracyScore] => 100
                                    [ErrorType] => None
                                )

                        )

                )

        )

)

[04-Aug-2025 05:13:51 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[04-Aug-2025 05:13:51 UTC] PHP Stack trace:
[04-Aug-2025 05:13:51 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:0
[04-Aug-2025 05:13:51 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:22
[04-Aug-2025 05:13:51 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[04-Aug-2025 05:13:51 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[04-Aug-2025 05:13:51 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[04-Aug-2025 05:13:51 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[04-Aug-2025 05:13:51 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[04-Aug-2025 05:13:51 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[04-Aug-2025 05:13:51 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[04-Aug-2025 05:13:51 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1379
[04-Aug-2025 05:13:51 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[04-Aug-2025 05:13:51 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[04-Aug-2025 05:14:52 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[04-Aug-2025 05:14:52 UTC] PHP Stack trace:
[04-Aug-2025 05:14:52 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:0
[04-Aug-2025 05:14:52 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:22
[04-Aug-2025 05:14:52 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[04-Aug-2025 05:14:52 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[04-Aug-2025 05:14:52 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[04-Aug-2025 05:14:52 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[04-Aug-2025 05:14:52 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[04-Aug-2025 05:14:52 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[04-Aug-2025 05:14:52 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[04-Aug-2025 05:14:52 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1379
[04-Aug-2025 05:14:52 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[04-Aug-2025 05:14:52 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[04-Aug-2025 05:14:52 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[04-Aug-2025 05:14:52 UTC] PHP Stack trace:
[04-Aug-2025 05:14:52 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-cron.php:0
[04-Aug-2025 05:14:52 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-cron.php:46
[04-Aug-2025 05:14:52 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[04-Aug-2025 05:14:52 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[04-Aug-2025 05:14:52 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[04-Aug-2025 05:14:52 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[04-Aug-2025 05:14:52 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[04-Aug-2025 05:14:52 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[04-Aug-2025 05:14:52 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[04-Aug-2025 05:14:52 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1379
[04-Aug-2025 05:14:52 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[04-Aug-2025 05:14:52 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[04-Aug-2025 05:16:53 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[04-Aug-2025 05:16:53 UTC] PHP Stack trace:
[04-Aug-2025 05:16:53 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:0
[04-Aug-2025 05:16:53 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:22
[04-Aug-2025 05:16:53 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[04-Aug-2025 05:16:53 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[04-Aug-2025 05:16:53 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[04-Aug-2025 05:16:53 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[04-Aug-2025 05:16:53 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[04-Aug-2025 05:16:53 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[04-Aug-2025 05:16:53 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[04-Aug-2025 05:16:53 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1379
[04-Aug-2025 05:16:53 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[04-Aug-2025 05:16:53 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[04-Aug-2025 05:16:53 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[04-Aug-2025 05:16:53 UTC] PHP Stack trace:
[04-Aug-2025 05:16:53 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-cron.php:0
[04-Aug-2025 05:16:53 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-cron.php:46
[04-Aug-2025 05:16:53 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[04-Aug-2025 05:16:53 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[04-Aug-2025 05:16:53 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[04-Aug-2025 05:16:53 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[04-Aug-2025 05:16:53 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[04-Aug-2025 05:16:53 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[04-Aug-2025 05:16:53 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[04-Aug-2025 05:16:53 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1379
[04-Aug-2025 05:16:53 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[04-Aug-2025 05:16:53 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:21:44 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:21:44 UTC] PHP Stack trace:
[10-Aug-2025 05:21:44 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 05:21:44 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 05:21:44 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 05:21:44 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:21:44 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:21:44 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:21:44 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:21:44 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:21:44 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:21:44 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:21:44 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:21:44 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:21:44 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:21:53 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:21:53 UTC] PHP Stack trace:
[10-Aug-2025 05:21:53 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-cron.php:0
[10-Aug-2025 05:21:53 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-cron.php:46
[10-Aug-2025 05:21:53 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:21:53 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:21:53 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:21:53 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:21:53 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:21:53 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:21:53 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:21:53 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:21:53 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:21:53 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:21:54 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:21:54 UTC] PHP Stack trace:
[10-Aug-2025 05:21:54 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 05:21:54 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 05:21:54 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 05:21:54 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:21:54 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:21:54 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:21:54 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:21:54 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:21:54 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:21:54 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:21:54 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:21:54 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:21:54 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:21:55 UTC] PHP Deprecated:  Creation of dynamic property Puc_v4p11_Plugin_Info::$message is deprecated in D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\inc\library\plugin-update-checker\Puc\v4p11\Metadata.php on line 47
[10-Aug-2025 05:21:55 UTC] PHP Stack trace:
[10-Aug-2025 05:21:55 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-cron.php:0
[10-Aug-2025 05:21:55 UTC] PHP   2. do_action_ref_array($hook_name = 'puc_cron_check_updates-custom-admin-interface-pro', $args = []) D:\wamp64\www\toeictest\wp-cron.php:191
[10-Aug-2025 05:21:55 UTC] PHP   3. WP_Hook->do_action($args = []) D:\wamp64\www\toeictest\wp-includes\plugin.php:565
[10-Aug-2025 05:21:55 UTC] PHP   4. WP_Hook->apply_filters($value = '', $args = []) D:\wamp64\www\toeictest\wp-includes\class-wp-hook.php:348
[10-Aug-2025 05:21:55 UTC] PHP   5. Puc_v4p11_Scheduler->maybeCheckForUpdates() D:\wamp64\www\toeictest\wp-includes\class-wp-hook.php:324
[10-Aug-2025 05:21:55 UTC] PHP   6. Puc_v4p11_UpdateChecker->checkForUpdates() D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\inc\library\plugin-update-checker\Puc\v4p11\Scheduler.php:180
[10-Aug-2025 05:21:55 UTC] PHP   7. Puc_v4p11_Plugin_UpdateChecker->requestUpdate() D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\inc\library\plugin-update-checker\Puc\v4p11\UpdateChecker.php:268
[10-Aug-2025 05:21:55 UTC] PHP   8. Puc_v4p11_Plugin_UpdateChecker->requestInfo($queryArgs = ['checking_for_updates' => '1']) D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\inc\library\plugin-update-checker\Puc\v4p11\Plugin\UpdateChecker.php:152
[10-Aug-2025 05:21:55 UTC] PHP   9. Puc_v4p11_UpdateChecker->requestMetadata($metaClass = 'Puc_v4p11_Plugin_Info', $filterRoot = 'request_info', $queryArgs = ['checking_for_updates' => '1']) D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\inc\library\plugin-update-checker\Puc\v4p11\Plugin\UpdateChecker.php:130
[10-Aug-2025 05:21:55 UTC] PHP  10. Puc_v4p11_Plugin_Info::fromJson($json = '{\n    "name": "Custom Admin Interface Pro",\n    "version": "2.1",\n    "homepage": "https:\\/\\/www.northernbeacheswebsites.com.au",\n    "author": "Martin Gibson",\n    "requires": "3.0.1",\n    "tested": "6.5.4",\n    "sections": {\n        "description": "<p>Custom Admin Interface Pro<\\/p>\\n",\n        "installation": "<p>There are a couple of methods for installing and setting up this plugin.<\\/p>\\n\\n<h4>Upload Manually<\\/h4>\\n\\n<ol>\\n<li>Download and unzip the plugin<\\/li>\\n<li>Upload the \'custom-admin-interfac'...) D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\inc\library\plugin-update-checker\Puc\v4p11\UpdateChecker.php:681
[10-Aug-2025 05:21:55 UTC] PHP  11. Puc_v4p11_Metadata::createFromJson($json = '{\n    "name": "Custom Admin Interface Pro",\n    "version": "2.1",\n    "homepage": "https:\\/\\/www.northernbeacheswebsites.com.au",\n    "author": "Martin Gibson",\n    "requires": "3.0.1",\n    "tested": "6.5.4",\n    "sections": {\n        "description": "<p>Custom Admin Interface Pro<\\/p>\\n",\n        "installation": "<p>There are a couple of methods for installing and setting up this plugin.<\\/p>\\n\\n<h4>Upload Manually<\\/h4>\\n\\n<ol>\\n<li>Download and unzip the plugin<\\/li>\\n<li>Upload the \'custom-admin-interfac'..., $target = class Puc_v4p11_Plugin_Info { public $name = 'Custom Admin Interface Pro'; public $slug = 'custom-admin-interface-pro'; public $version = '2.1'; public $homepage = 'https://www.northernbeacheswebsites.com.au'; public $sections = class stdClass { public $description = '<p>Custom Admin Interface Pro</p>\n'; public $installation = '<p>There are a couple of methods for installing and setting up this plugin.</p>\n\n<h4>Upload Manually</h4>\n\n<ol>\n<li>Download and unzip the plugin</li>\n<li>Upload the \'custom-admin-interface-pro\' folder into the \'/wp-content/plugins/\' directory</li>\n<li>Go to the Plugins admin page and activate the plugin</li>\n</ol>\n'; public $frequently_asked_questions = '\n'; public $screenshots = '\n'; public $changelog = '<h4>2.1</h4>\n\n<ul>\n<li>Minor bug fix</li>\n</ul>\n\n<h4>2.0</h4>\n\n<ul>\n<li>Implementation of new custom admin page module</li>\n</ul>\n\n<h4>1.61</h4>\n\n<ul>\n<li>Fix for 1.60</li>\n</ul>\n\n<h4>1.59</h4>\n\n<ul>\n<li>Small PHP bug fix</li>\n</ul>\n\n<h4>1.58</h4>\n\n<ul>\n<li>Fix to spacing of body classes</li>\n</ul>\n\n<h4>1.57</h4>\n\n<ul>\n<li>Updates to the hide metabox functionality for compatibility with WordPress 6.3 - Please only update this plugin to this version if you are using version 6.3 of WordPress or above </li>\n</'...; public $upgrade_notice = '<h4>2.1</h4>\n\n<ul>\n<li>Minor bug fix</li>\n</ul>\n\n<h4>2.0</h4>\n\n<ul>\n<li>Implementation of new custom admin page module</li>\n</ul>\n\n<h4>1.61</h4>\n\n<ul>\n<li>Fix for 1.60</li>\n</ul>\n\n<h4>1.59</h4>\n\n<ul>\n<li>Small PHP bug fix</li>\n</ul>\n\n<h4>1.58</h4>\n\n<ul>\n<li>Fix to spacing of body classes</li>\n</ul>\n\n<h4>1.57</h4>\n\n<ul>\n<li>Updates to the hide metabox functionality for compatibility with WordPress 6.3 - Please only update this plugin to this version if you are using version 6.3 of WordPress or above </li>\n</'... }; public $download_url = NULL; public $banners = NULL; public $icons = []; public $translations = []; public $author = 'Martin Gibson'; public $author_homepage = NULL; public $requires = '3.0.1'; public $tested = '6.5.4'; public $requires_php = NULL; public $upgrade_notice = NULL; public $rating = NULL; public $num_ratings = NULL; public $downloaded = NULL; public $active_installs = NULL; public $last_updated = '2024-08-06 23:44:52'; public $id = 0; public $filename = NULL }) D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\inc\library\plugin-update-checker\Puc\v4p11\Plugin\Info.php:53
[10-Aug-2025 05:21:55 UTC] PHP Deprecated:  Creation of dynamic property Puc_v4p11_Plugin_Info::$request_time_elapsed is deprecated in D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\inc\library\plugin-update-checker\Puc\v4p11\Metadata.php on line 47
[10-Aug-2025 05:21:55 UTC] PHP Stack trace:
[10-Aug-2025 05:21:55 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-cron.php:0
[10-Aug-2025 05:21:55 UTC] PHP   2. do_action_ref_array($hook_name = 'puc_cron_check_updates-custom-admin-interface-pro', $args = []) D:\wamp64\www\toeictest\wp-cron.php:191
[10-Aug-2025 05:21:55 UTC] PHP   3. WP_Hook->do_action($args = []) D:\wamp64\www\toeictest\wp-includes\plugin.php:565
[10-Aug-2025 05:21:55 UTC] PHP   4. WP_Hook->apply_filters($value = '', $args = []) D:\wamp64\www\toeictest\wp-includes\class-wp-hook.php:348
[10-Aug-2025 05:21:55 UTC] PHP   5. Puc_v4p11_Scheduler->maybeCheckForUpdates() D:\wamp64\www\toeictest\wp-includes\class-wp-hook.php:324
[10-Aug-2025 05:21:55 UTC] PHP   6. Puc_v4p11_UpdateChecker->checkForUpdates() D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\inc\library\plugin-update-checker\Puc\v4p11\Scheduler.php:180
[10-Aug-2025 05:21:55 UTC] PHP   7. Puc_v4p11_Plugin_UpdateChecker->requestUpdate() D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\inc\library\plugin-update-checker\Puc\v4p11\UpdateChecker.php:268
[10-Aug-2025 05:21:55 UTC] PHP   8. Puc_v4p11_Plugin_UpdateChecker->requestInfo($queryArgs = ['checking_for_updates' => '1']) D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\inc\library\plugin-update-checker\Puc\v4p11\Plugin\UpdateChecker.php:152
[10-Aug-2025 05:21:55 UTC] PHP   9. Puc_v4p11_UpdateChecker->requestMetadata($metaClass = 'Puc_v4p11_Plugin_Info', $filterRoot = 'request_info', $queryArgs = ['checking_for_updates' => '1']) D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\inc\library\plugin-update-checker\Puc\v4p11\Plugin\UpdateChecker.php:130
[10-Aug-2025 05:21:55 UTC] PHP  10. Puc_v4p11_Plugin_Info::fromJson($json = '{\n    "name": "Custom Admin Interface Pro",\n    "version": "2.1",\n    "homepage": "https:\\/\\/www.northernbeacheswebsites.com.au",\n    "author": "Martin Gibson",\n    "requires": "3.0.1",\n    "tested": "6.5.4",\n    "sections": {\n        "description": "<p>Custom Admin Interface Pro<\\/p>\\n",\n        "installation": "<p>There are a couple of methods for installing and setting up this plugin.<\\/p>\\n\\n<h4>Upload Manually<\\/h4>\\n\\n<ol>\\n<li>Download and unzip the plugin<\\/li>\\n<li>Upload the \'custom-admin-interfac'...) D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\inc\library\plugin-update-checker\Puc\v4p11\UpdateChecker.php:681
[10-Aug-2025 05:21:55 UTC] PHP  11. Puc_v4p11_Metadata::createFromJson($json = '{\n    "name": "Custom Admin Interface Pro",\n    "version": "2.1",\n    "homepage": "https:\\/\\/www.northernbeacheswebsites.com.au",\n    "author": "Martin Gibson",\n    "requires": "3.0.1",\n    "tested": "6.5.4",\n    "sections": {\n        "description": "<p>Custom Admin Interface Pro<\\/p>\\n",\n        "installation": "<p>There are a couple of methods for installing and setting up this plugin.<\\/p>\\n\\n<h4>Upload Manually<\\/h4>\\n\\n<ol>\\n<li>Download and unzip the plugin<\\/li>\\n<li>Upload the \'custom-admin-interfac'..., $target = class Puc_v4p11_Plugin_Info { public $name = 'Custom Admin Interface Pro'; public $slug = 'custom-admin-interface-pro'; public $version = '2.1'; public $homepage = 'https://www.northernbeacheswebsites.com.au'; public $sections = class stdClass { public $description = '<p>Custom Admin Interface Pro</p>\n'; public $installation = '<p>There are a couple of methods for installing and setting up this plugin.</p>\n\n<h4>Upload Manually</h4>\n\n<ol>\n<li>Download and unzip the plugin</li>\n<li>Upload the \'custom-admin-interface-pro\' folder into the \'/wp-content/plugins/\' directory</li>\n<li>Go to the Plugins admin page and activate the plugin</li>\n</ol>\n'; public $frequently_asked_questions = '\n'; public $screenshots = '\n'; public $changelog = '<h4>2.1</h4>\n\n<ul>\n<li>Minor bug fix</li>\n</ul>\n\n<h4>2.0</h4>\n\n<ul>\n<li>Implementation of new custom admin page module</li>\n</ul>\n\n<h4>1.61</h4>\n\n<ul>\n<li>Fix for 1.60</li>\n</ul>\n\n<h4>1.59</h4>\n\n<ul>\n<li>Small PHP bug fix</li>\n</ul>\n\n<h4>1.58</h4>\n\n<ul>\n<li>Fix to spacing of body classes</li>\n</ul>\n\n<h4>1.57</h4>\n\n<ul>\n<li>Updates to the hide metabox functionality for compatibility with WordPress 6.3 - Please only update this plugin to this version if you are using version 6.3 of WordPress or above </li>\n</'...; public $upgrade_notice = '<h4>2.1</h4>\n\n<ul>\n<li>Minor bug fix</li>\n</ul>\n\n<h4>2.0</h4>\n\n<ul>\n<li>Implementation of new custom admin page module</li>\n</ul>\n\n<h4>1.61</h4>\n\n<ul>\n<li>Fix for 1.60</li>\n</ul>\n\n<h4>1.59</h4>\n\n<ul>\n<li>Small PHP bug fix</li>\n</ul>\n\n<h4>1.58</h4>\n\n<ul>\n<li>Fix to spacing of body classes</li>\n</ul>\n\n<h4>1.57</h4>\n\n<ul>\n<li>Updates to the hide metabox functionality for compatibility with WordPress 6.3 - Please only update this plugin to this version if you are using version 6.3 of WordPress or above </li>\n</'... }; public $download_url = NULL; public $banners = NULL; public $icons = []; public $translations = []; public $author = 'Martin Gibson'; public $author_homepage = NULL; public $requires = '3.0.1'; public $tested = '6.5.4'; public $requires_php = NULL; public $upgrade_notice = NULL; public $rating = NULL; public $num_ratings = NULL; public $downloaded = NULL; public $active_installs = NULL; public $last_updated = '2024-08-06 23:44:52'; public $id = 0; public $filename = NULL; public $message = 'No' }) D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\inc\library\plugin-update-checker\Puc\v4p11\Plugin\Info.php:53
[10-Aug-2025 05:21:57 UTC] Automatic updates starting...
[10-Aug-2025 05:22:06 UTC]   Automatic plugin updates starting...
[10-Aug-2025 05:22:06 UTC]   Automatic plugin updates complete.
[10-Aug-2025 05:22:06 UTC]   Automatic theme updates starting...
[10-Aug-2025 05:22:06 UTC]   Automatic theme updates complete.
[10-Aug-2025 05:22:06 UTC] Automatic updates complete.
[10-Aug-2025 05:22:14 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:22:14 UTC] PHP Stack trace:
[10-Aug-2025 05:22:14 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:0
[10-Aug-2025 05:22:14 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:22
[10-Aug-2025 05:22:14 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:22:14 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:22:14 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:22:14 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:22:14 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:22:14 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:22:14 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:22:14 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:22:14 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:22:14 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:22:20 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:22:20 UTC] PHP Stack trace:
[10-Aug-2025 05:22:20 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 05:22:20 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 05:22:20 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 05:22:20 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:22:20 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:22:20 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:22:20 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:22:20 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:22:20 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:22:20 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:22:20 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:22:20 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:22:20 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:22:21 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:22:21 UTC] PHP Stack trace:
[10-Aug-2025 05:22:21 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 05:22:21 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 05:22:21 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 05:22:21 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:22:21 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:22:21 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:22:21 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:22:21 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:22:21 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:22:21 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:22:21 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:22:21 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:22:21 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:22:21 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:22:21 UTC] PHP Stack trace:
[10-Aug-2025 05:22:21 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 05:22:21 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 05:22:21 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 05:22:21 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:22:21 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:22:21 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:22:21 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:22:21 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:22:21 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:22:21 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:22:21 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:22:21 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:22:21 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:31:35 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:31:35 UTC] PHP Stack trace:
[10-Aug-2025 05:31:35 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 05:31:35 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 05:31:35 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 05:31:35 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:31:35 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:31:35 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:31:35 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:31:35 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:31:35 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:31:35 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:31:35 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:31:35 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:31:35 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:31:35 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:31:35 UTC] PHP Stack trace:
[10-Aug-2025 05:31:35 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-cron.php:0
[10-Aug-2025 05:31:35 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-cron.php:46
[10-Aug-2025 05:31:35 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:31:35 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:31:35 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:31:35 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:31:35 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:31:35 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:31:35 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:31:35 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:31:35 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:31:35 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:31:37 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:31:37 UTC] PHP Stack trace:
[10-Aug-2025 05:31:37 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 05:31:37 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 05:31:37 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 05:31:37 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:31:37 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:31:37 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:31:37 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:31:37 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:31:37 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:31:37 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:31:37 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:31:37 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:31:37 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:31:40 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:31:40 UTC] PHP Stack trace:
[10-Aug-2025 05:31:40 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 05:31:40 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 05:31:40 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 05:31:40 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:31:40 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:31:40 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:31:40 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:31:40 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:31:40 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:31:40 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:31:40 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:31:40 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:31:40 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:31:41 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:31:41 UTC] PHP Stack trace:
[10-Aug-2025 05:31:41 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 05:31:41 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 05:31:41 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 05:31:41 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:31:41 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:31:41 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:31:41 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:31:41 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:31:41 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:31:41 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:31:41 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:31:41 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:31:41 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:31:42 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:31:42 UTC] PHP Stack trace:
[10-Aug-2025 05:31:42 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 05:31:42 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 05:31:42 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 05:31:42 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:31:42 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:31:42 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:31:42 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:31:42 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:31:42 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:31:42 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:31:42 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:31:42 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:31:42 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:33:06 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:33:06 UTC] PHP Stack trace:
[10-Aug-2025 05:33:06 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 05:33:06 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 05:33:06 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 05:33:06 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:33:06 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:33:06 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:33:06 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:33:06 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:33:06 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:33:06 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:33:06 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:33:06 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:33:06 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:33:06 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:33:06 UTC] PHP Stack trace:
[10-Aug-2025 05:33:06 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-cron.php:0
[10-Aug-2025 05:33:06 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-cron.php:46
[10-Aug-2025 05:33:06 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:33:06 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:33:06 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:33:06 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:33:06 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:33:06 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:33:06 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:33:06 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:33:06 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:33:06 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:33:07 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:33:07 UTC] PHP Stack trace:
[10-Aug-2025 05:33:07 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 05:33:07 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 05:33:07 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 05:33:07 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:33:07 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:33:07 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:33:07 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:33:07 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:33:07 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:33:07 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:33:07 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:33:07 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:33:07 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:33:18 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:33:18 UTC] PHP Stack trace:
[10-Aug-2025 05:33:18 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 05:33:18 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 05:33:18 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 05:33:18 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:33:18 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:33:18 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:33:18 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:33:18 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:33:18 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:33:18 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:33:18 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:33:18 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:33:18 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:33:19 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:33:19 UTC] PHP Stack trace:
[10-Aug-2025 05:33:19 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 05:33:19 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 05:33:19 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 05:33:19 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:33:19 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:33:19 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:33:19 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:33:19 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:33:19 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:33:19 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:33:19 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:33:19 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:33:19 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:36:25 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:36:25 UTC] PHP Stack trace:
[10-Aug-2025 05:36:25 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 05:36:25 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 05:36:25 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 05:36:25 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:36:25 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:36:25 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:36:25 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:36:25 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:36:25 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:36:25 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:36:25 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:36:25 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:36:25 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:36:25 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:36:25 UTC] PHP Stack trace:
[10-Aug-2025 05:36:25 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-cron.php:0
[10-Aug-2025 05:36:25 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-cron.php:46
[10-Aug-2025 05:36:25 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:36:25 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:36:25 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:36:25 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:36:25 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:36:25 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:36:25 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:36:25 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:36:25 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:36:25 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:36:26 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:36:26 UTC] PHP Stack trace:
[10-Aug-2025 05:36:26 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 05:36:26 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 05:36:26 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 05:36:26 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:36:26 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:36:26 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:36:26 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:36:26 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:36:26 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:36:26 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:36:26 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:36:26 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:36:26 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:36:40 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:36:40 UTC] PHP Stack trace:
[10-Aug-2025 05:36:40 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 05:36:40 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 05:36:40 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 05:36:40 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:36:40 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:36:40 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:36:40 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:36:40 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:36:40 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:36:40 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:36:40 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:36:40 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:36:40 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:36:41 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:36:41 UTC] PHP Stack trace:
[10-Aug-2025 05:36:41 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 05:36:41 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 05:36:41 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 05:36:41 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:36:41 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:36:41 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:36:41 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:36:41 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:36:41 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:36:41 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:36:41 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:36:41 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:36:41 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:39:20 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:39:20 UTC] PHP Stack trace:
[10-Aug-2025 05:39:20 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 05:39:20 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 05:39:20 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 05:39:20 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:39:20 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:39:20 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:39:20 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:39:20 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:39:20 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:39:20 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:39:20 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:39:20 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:39:20 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:39:20 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:39:20 UTC] PHP Stack trace:
[10-Aug-2025 05:39:20 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-cron.php:0
[10-Aug-2025 05:39:20 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-cron.php:46
[10-Aug-2025 05:39:20 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:39:20 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:39:20 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:39:20 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:39:20 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:39:20 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:39:20 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:39:20 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:39:20 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:39:20 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:39:21 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:39:21 UTC] PHP Stack trace:
[10-Aug-2025 05:39:21 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 05:39:21 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 05:39:21 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 05:39:21 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:39:21 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:39:21 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:39:21 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:39:21 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:39:21 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:39:21 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:39:21 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:39:21 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:39:21 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:40:53 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:40:53 UTC] PHP Stack trace:
[10-Aug-2025 05:40:53 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 05:40:53 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 05:40:53 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 05:40:53 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:40:53 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:40:53 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:40:53 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:40:53 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:40:53 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:40:53 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:40:53 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:40:53 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:40:53 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:40:53 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:40:53 UTC] PHP Stack trace:
[10-Aug-2025 05:40:53 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-cron.php:0
[10-Aug-2025 05:40:53 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-cron.php:46
[10-Aug-2025 05:40:53 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:40:53 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:40:53 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:40:53 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:40:53 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:40:53 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:40:53 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:40:53 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:40:53 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:40:53 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:40:54 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:40:54 UTC] PHP Stack trace:
[10-Aug-2025 05:40:54 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 05:40:54 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 05:40:54 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 05:40:54 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:40:54 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:40:54 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:40:54 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:40:54 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:40:54 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:40:54 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:40:54 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:40:54 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:40:54 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:43:54 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:43:54 UTC] PHP Stack trace:
[10-Aug-2025 05:43:54 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 05:43:54 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 05:43:54 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 05:43:54 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:43:54 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:43:54 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:43:54 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:43:54 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:43:54 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:43:54 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:43:54 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:43:54 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:43:54 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:43:54 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:43:54 UTC] PHP Stack trace:
[10-Aug-2025 05:43:54 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-cron.php:0
[10-Aug-2025 05:43:54 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-cron.php:46
[10-Aug-2025 05:43:54 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:43:54 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:43:54 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:43:54 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:43:54 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:43:54 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:43:54 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:43:54 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:43:54 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:43:54 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:43:55 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:43:55 UTC] PHP Stack trace:
[10-Aug-2025 05:43:55 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 05:43:55 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 05:43:55 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 05:43:55 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:43:55 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:43:55 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:43:55 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:43:55 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:43:55 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:43:55 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:43:55 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:43:55 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:43:55 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:44:50 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:44:50 UTC] PHP Stack trace:
[10-Aug-2025 05:44:50 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 05:44:50 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 05:44:50 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 05:44:50 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:44:50 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:44:51 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:44:51 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:44:51 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:44:51 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:44:51 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:44:51 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:44:51 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:44:51 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:44:51 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:44:51 UTC] PHP Stack trace:
[10-Aug-2025 05:44:51 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-cron.php:0
[10-Aug-2025 05:44:51 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-cron.php:46
[10-Aug-2025 05:44:51 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:44:51 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:44:51 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:44:51 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:44:51 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:44:51 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:44:51 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:44:51 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:44:51 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:44:51 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:44:52 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:44:52 UTC] PHP Stack trace:
[10-Aug-2025 05:44:52 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 05:44:52 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 05:44:52 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 05:44:52 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:44:52 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:44:52 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:44:52 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:44:52 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:44:52 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:44:52 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:44:52 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:44:52 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:44:52 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:45:18 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:45:18 UTC] PHP Stack trace:
[10-Aug-2025 05:45:18 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 05:45:18 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 05:45:18 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 05:45:18 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:45:18 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:45:18 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:45:18 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:45:18 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:45:18 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:45:18 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:45:18 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:45:18 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:45:18 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:45:18 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:45:18 UTC] PHP Stack trace:
[10-Aug-2025 05:45:18 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 05:45:18 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 05:45:18 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 05:45:18 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:45:18 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:45:18 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:45:18 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:45:18 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:45:18 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:45:18 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:45:18 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:45:18 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:45:18 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:45:18 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:45:18 UTC] PHP Stack trace:
[10-Aug-2025 05:45:18 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-cron.php:0
[10-Aug-2025 05:45:18 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-cron.php:46
[10-Aug-2025 05:45:18 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:45:18 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:45:18 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:45:18 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:45:18 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:45:18 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:45:18 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:45:18 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:45:18 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:45:18 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:45:19 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:45:19 UTC] PHP Stack trace:
[10-Aug-2025 05:45:19 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 05:45:19 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 05:45:19 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 05:45:19 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:45:19 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:45:20 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:45:20 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:45:20 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:45:20 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:45:20 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:45:20 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:45:20 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:45:20 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:45:21 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:45:21 UTC] PHP Stack trace:
[10-Aug-2025 05:45:21 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 05:45:21 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 05:45:21 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 05:45:21 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:45:21 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:45:21 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:45:21 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:45:21 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:45:21 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:45:21 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:45:21 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:45:21 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:45:21 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:45:21 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:45:21 UTC] PHP Stack trace:
[10-Aug-2025 05:45:21 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 05:45:21 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 05:45:21 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 05:45:21 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:45:21 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:45:21 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:45:21 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:45:21 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:45:21 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:45:21 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:45:21 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:45:21 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:45:21 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:45:34 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:45:34 UTC] PHP Stack trace:
[10-Aug-2025 05:45:34 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 05:45:34 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 05:45:34 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 05:45:34 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:45:34 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:45:34 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:45:34 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:45:34 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:45:34 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:45:34 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:45:34 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:45:34 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:45:34 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:45:35 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:45:35 UTC] PHP Stack trace:
[10-Aug-2025 05:45:35 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 05:45:35 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 05:45:35 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 05:45:35 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:45:35 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:45:35 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:45:35 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:45:35 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:45:35 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:45:35 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:45:35 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:45:35 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:45:35 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:45:39 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:45:39 UTC] PHP Stack trace:
[10-Aug-2025 05:45:39 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 05:45:39 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 05:45:39 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 05:45:39 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:45:39 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:45:39 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:45:39 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:45:39 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:45:39 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:45:39 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:45:39 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:45:39 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:45:39 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:45:39 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:45:39 UTC] PHP Stack trace:
[10-Aug-2025 05:45:39 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 05:45:39 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 05:45:39 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 05:45:39 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:45:39 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:45:39 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:45:39 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:45:39 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:45:39 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:45:39 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:45:39 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:45:39 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:45:39 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:45:40 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:45:40 UTC] PHP Stack trace:
[10-Aug-2025 05:45:40 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 05:45:40 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 05:45:40 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 05:45:40 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:45:40 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:45:40 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:45:40 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:45:40 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:45:40 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:45:40 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:45:40 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:45:40 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:45:40 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:45:41 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:45:41 UTC] PHP Stack trace:
[10-Aug-2025 05:45:41 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 05:45:41 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 05:45:41 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 05:45:41 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:45:41 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:45:41 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:45:41 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:45:41 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:45:41 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:45:41 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:45:41 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:45:41 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:45:41 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:46:14 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:46:14 UTC] PHP Stack trace:
[10-Aug-2025 05:46:14 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 05:46:14 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 05:46:14 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 05:46:14 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:46:14 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:46:14 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:46:14 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:46:14 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:46:14 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:46:14 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:46:14 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:46:14 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:46:14 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:46:15 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:46:15 UTC] PHP Stack trace:
[10-Aug-2025 05:46:15 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-cron.php:0
[10-Aug-2025 05:46:15 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-cron.php:46
[10-Aug-2025 05:46:15 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:46:15 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:46:15 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:46:15 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:46:15 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:46:15 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:46:15 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:46:15 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:46:15 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:46:15 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:46:16 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:46:16 UTC] PHP Stack trace:
[10-Aug-2025 05:46:16 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 05:46:16 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 05:46:16 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 05:46:16 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:46:16 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:46:16 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:46:16 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:46:16 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:46:16 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:46:16 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:46:16 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:46:16 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:46:16 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:46:27 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:46:27 UTC] PHP Stack trace:
[10-Aug-2025 05:46:27 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 05:46:27 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 05:46:27 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 05:46:27 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:46:27 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:46:27 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:46:27 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:46:27 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:46:27 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:46:27 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:46:27 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:46:27 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:46:27 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:46:28 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:46:28 UTC] PHP Stack trace:
[10-Aug-2025 05:46:28 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 05:46:28 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 05:46:28 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 05:46:28 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:46:28 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:46:28 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:46:28 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:46:28 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:46:28 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:46:28 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:46:28 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:46:28 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:46:28 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:46:35 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:46:35 UTC] PHP Stack trace:
[10-Aug-2025 05:46:35 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 05:46:35 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 05:46:35 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 05:46:35 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:46:35 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:46:35 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:46:35 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:46:35 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:46:35 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:46:35 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:46:35 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:46:35 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:46:35 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:46:36 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:46:36 UTC] PHP Stack trace:
[10-Aug-2025 05:46:36 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 05:46:36 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 05:46:36 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 05:46:36 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:46:36 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:46:36 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:46:36 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:46:36 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:46:36 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:46:36 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:46:36 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:46:36 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:46:36 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:47:31 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:47:31 UTC] PHP Stack trace:
[10-Aug-2025 05:47:31 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 05:47:31 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 05:47:31 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 05:47:31 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:47:31 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:47:31 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:47:31 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:47:31 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:47:31 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:47:31 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:47:31 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:47:31 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:47:31 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:47:31 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:47:31 UTC] PHP Stack trace:
[10-Aug-2025 05:47:31 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-cron.php:0
[10-Aug-2025 05:47:31 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-cron.php:46
[10-Aug-2025 05:47:31 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:47:31 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:47:31 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:47:31 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:47:31 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:47:31 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:47:31 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:47:31 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:47:31 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:47:31 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:47:32 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:47:32 UTC] PHP Stack trace:
[10-Aug-2025 05:47:32 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 05:47:32 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 05:47:32 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 05:47:32 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:47:32 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:47:32 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:47:32 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:47:32 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:47:32 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:47:32 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:47:32 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:47:32 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:47:32 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:47:45 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:47:45 UTC] PHP Stack trace:
[10-Aug-2025 05:47:45 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 05:47:45 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 05:47:45 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 05:47:45 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:47:45 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:47:45 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:47:45 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:47:45 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:47:45 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:47:45 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:47:45 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:47:45 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:47:45 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:47:45 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:47:45 UTC] PHP Stack trace:
[10-Aug-2025 05:47:45 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 05:47:45 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 05:47:45 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 05:47:45 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:47:45 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:47:45 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:47:45 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:47:45 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:47:45 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:47:45 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:47:45 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:47:45 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:47:45 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:48:22 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:48:22 UTC] PHP Stack trace:
[10-Aug-2025 05:48:22 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 05:48:22 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 05:48:22 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 05:48:22 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:48:22 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:48:22 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:48:22 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:48:22 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:48:22 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:48:22 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:48:22 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:48:22 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:48:22 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:48:22 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:48:22 UTC] PHP Stack trace:
[10-Aug-2025 05:48:22 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-cron.php:0
[10-Aug-2025 05:48:22 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-cron.php:46
[10-Aug-2025 05:48:22 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:48:22 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:48:22 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:48:22 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:48:22 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:48:22 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:48:22 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:48:22 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:48:22 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:48:22 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:48:23 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:48:23 UTC] PHP Stack trace:
[10-Aug-2025 05:48:23 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 05:48:23 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 05:48:23 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 05:48:23 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:48:23 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:48:23 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:48:23 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:48:23 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:48:23 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:48:23 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:48:23 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:48:23 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:48:23 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:49:15 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:49:15 UTC] PHP Stack trace:
[10-Aug-2025 05:49:15 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 05:49:15 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 05:49:15 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 05:49:15 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:49:15 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:49:15 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:49:15 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:49:15 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:49:15 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:49:15 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:49:15 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:49:15 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:49:15 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:49:16 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:49:16 UTC] PHP Stack trace:
[10-Aug-2025 05:49:16 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-cron.php:0
[10-Aug-2025 05:49:16 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-cron.php:46
[10-Aug-2025 05:49:16 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:49:16 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:49:16 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:49:16 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:49:16 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:49:16 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:49:16 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:49:16 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:49:16 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:49:16 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:49:16 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:49:16 UTC] PHP Stack trace:
[10-Aug-2025 05:49:16 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 05:49:16 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 05:49:16 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 05:49:16 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:49:16 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:49:16 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:49:16 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:49:16 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:49:16 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:49:16 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:49:16 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:49:16 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:49:16 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:49:57 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:49:57 UTC] PHP Stack trace:
[10-Aug-2025 05:49:57 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 05:49:57 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 05:49:57 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 05:49:57 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:49:57 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:49:57 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:49:57 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:49:57 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:49:57 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:49:57 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:49:57 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:49:57 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:49:57 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:49:58 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:49:58 UTC] PHP Stack trace:
[10-Aug-2025 05:49:58 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 05:49:58 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 05:49:58 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 05:49:58 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:49:58 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:49:58 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:49:58 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:49:58 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:49:58 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:49:58 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:49:58 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:49:58 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:49:58 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:52:51 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:52:51 UTC] PHP Stack trace:
[10-Aug-2025 05:52:51 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 05:52:51 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 05:52:51 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 05:52:51 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:52:51 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:52:51 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:52:51 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:52:51 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:52:51 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:52:51 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:52:51 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:52:51 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:52:51 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:52:51 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:52:51 UTC] PHP Stack trace:
[10-Aug-2025 05:52:51 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-cron.php:0
[10-Aug-2025 05:52:51 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-cron.php:46
[10-Aug-2025 05:52:51 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:52:51 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:52:51 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:52:51 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:52:51 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:52:51 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:52:51 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:52:51 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:52:51 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:52:51 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:52:52 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:52:52 UTC] PHP Stack trace:
[10-Aug-2025 05:52:52 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 05:52:52 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 05:52:52 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 05:52:52 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:52:52 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:52:52 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:52:52 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:52:52 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:52:52 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:52:52 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:52:52 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:52:52 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:52:52 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:54:09 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:54:09 UTC] PHP Stack trace:
[10-Aug-2025 05:54:09 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 05:54:09 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 05:54:09 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 05:54:09 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:54:09 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:54:09 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:54:09 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:54:09 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:54:09 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:54:09 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:54:09 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:54:09 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:54:09 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:54:10 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:54:10 UTC] PHP Stack trace:
[10-Aug-2025 05:54:10 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-cron.php:0
[10-Aug-2025 05:54:10 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-cron.php:46
[10-Aug-2025 05:54:10 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:54:10 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:54:10 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:54:10 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:54:10 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:54:10 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:54:10 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:54:10 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:54:10 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:54:10 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:54:11 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:54:11 UTC] PHP Stack trace:
[10-Aug-2025 05:54:11 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 05:54:11 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 05:54:11 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 05:54:11 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:54:11 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:54:11 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:54:11 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:54:11 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:54:11 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:54:11 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:54:11 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:54:11 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:54:11 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 05:54:45 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 05:54:45 UTC] PHP Stack trace:
[10-Aug-2025 05:54:45 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 05:54:45 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 05:54:45 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 05:54:45 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 05:54:45 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 05:54:45 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 05:54:45 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 05:54:45 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 05:54:45 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 05:54:45 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 05:54:45 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 05:54:45 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 05:54:45 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 06:19:06 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 06:19:06 UTC] PHP Stack trace:
[10-Aug-2025 06:19:06 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 06:19:06 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 06:19:06 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 06:19:06 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 06:19:06 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 06:19:06 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 06:19:06 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 06:19:06 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 06:19:06 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 06:19:06 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 06:19:06 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 06:19:06 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 06:19:06 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 06:19:06 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 06:19:06 UTC] PHP Stack trace:
[10-Aug-2025 06:19:06 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-cron.php:0
[10-Aug-2025 06:19:06 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-cron.php:46
[10-Aug-2025 06:19:06 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 06:19:06 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 06:19:06 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 06:19:06 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 06:19:06 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 06:19:06 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 06:19:06 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 06:19:06 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 06:19:06 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 06:19:06 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 06:19:06 UTC] PHP Deprecated:  explode(): Passing null to parameter #2 ($string) of type string is deprecated in D:\wamp64\www\toeictest\wp-includes\general-template.php on line 1439
[10-Aug-2025 06:19:06 UTC] PHP Stack trace:
[10-Aug-2025 06:19:06 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 06:19:06 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 06:19:06 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:19
[10-Aug-2025 06:19:06 UTC] PHP   4. do_action($hook_name = 'template_redirect') D:\wamp64\www\toeictest\wp-includes\template-loader.php:13
[10-Aug-2025 06:19:06 UTC] PHP   5. WP_Hook->do_action($args = [0 => '']) D:\wamp64\www\toeictest\wp-includes\plugin.php:517
[10-Aug-2025 06:19:06 UTC] PHP   6. WP_Hook->apply_filters($value = '', $args = [0 => '']) D:\wamp64\www\toeictest\wp-includes\class-wp-hook.php:348
[10-Aug-2025 06:19:06 UTC] PHP   7. ToeicPractice\Frontend\Utils\Router->handleRoutes('') D:\wamp64\www\toeictest\wp-includes\class-wp-hook.php:324
[10-Aug-2025 06:19:06 UTC] PHP   8. ToeicPractice\Frontend\Utils\Router->loadRouteTemplate($template = 'toeic', $data = []) D:\wamp64\www\toeictest\wp-content\plugins\toeic-practice\src\Frontend\Utils\Router.php:117
[10-Aug-2025 06:19:06 UTC] PHP   9. ToeicPractice\Frontend\Utils\TemplateLoader->load($template = 'toeic', $vars = [], $return = *uninitialized*) D:\wamp64\www\toeictest\wp-content\plugins\toeic-practice\src\Frontend\Utils\Router.php:265
[10-Aug-2025 06:19:06 UTC] PHP  10. include() D:\wamp64\www\toeictest\wp-content\plugins\toeic-practice\src\Frontend\Utils\TemplateLoader.php:65
[10-Aug-2025 06:19:06 UTC] PHP  11. include_once() D:\wamp64\www\toeictest\wp-content\plugins\toeic-practice\templates\toeic.php:15
[10-Aug-2025 06:19:06 UTC] PHP  12. wp_title($sep = '|', $display = TRUE, $seplocation = 'right') D:\wamp64\www\toeictest\wp-content\plugins\toeic-practice\templates\parts\header.php:7
[10-Aug-2025 06:19:06 UTC] PHP  13. explode($separator = '%WP_TITLE_SEP%', $string = NULL) D:\wamp64\www\toeictest\wp-includes\general-template.php:1439
[10-Aug-2025 06:19:07 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 06:19:07 UTC] PHP Stack trace:
[10-Aug-2025 06:19:07 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 06:19:07 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 06:19:07 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 06:19:07 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 06:19:07 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 06:19:07 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 06:19:07 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 06:19:07 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 06:19:07 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 06:19:07 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 06:19:07 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 06:19:07 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 06:19:07 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 06:20:07 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 06:20:07 UTC] PHP Stack trace:
[10-Aug-2025 06:20:07 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 06:20:07 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 06:20:07 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 06:20:07 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 06:20:07 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 06:20:07 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 06:20:07 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 06:20:07 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 06:20:07 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 06:20:07 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 06:20:07 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 06:20:07 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 06:20:07 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 06:20:08 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 06:20:08 UTC] PHP Stack trace:
[10-Aug-2025 06:20:08 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-cron.php:0
[10-Aug-2025 06:20:08 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-cron.php:46
[10-Aug-2025 06:20:08 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 06:20:08 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 06:20:08 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 06:20:08 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 06:20:08 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 06:20:08 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 06:20:08 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 06:20:08 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 06:20:08 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 06:20:08 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 06:20:08 UTC] PHP Deprecated:  explode(): Passing null to parameter #2 ($string) of type string is deprecated in D:\wamp64\www\toeictest\wp-includes\general-template.php on line 1439
[10-Aug-2025 06:20:08 UTC] PHP Stack trace:
[10-Aug-2025 06:20:08 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 06:20:08 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 06:20:08 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:19
[10-Aug-2025 06:20:08 UTC] PHP   4. do_action($hook_name = 'template_redirect') D:\wamp64\www\toeictest\wp-includes\template-loader.php:13
[10-Aug-2025 06:20:08 UTC] PHP   5. WP_Hook->do_action($args = [0 => '']) D:\wamp64\www\toeictest\wp-includes\plugin.php:517
[10-Aug-2025 06:20:08 UTC] PHP   6. WP_Hook->apply_filters($value = '', $args = [0 => '']) D:\wamp64\www\toeictest\wp-includes\class-wp-hook.php:348
[10-Aug-2025 06:20:08 UTC] PHP   7. ToeicPractice\Frontend\Utils\Router->handleRoutes('') D:\wamp64\www\toeictest\wp-includes\class-wp-hook.php:324
[10-Aug-2025 06:20:08 UTC] PHP   8. ToeicPractice\Frontend\Utils\Router->loadRouteTemplate($template = 'toeic', $data = []) D:\wamp64\www\toeictest\wp-content\plugins\toeic-practice\src\Frontend\Utils\Router.php:117
[10-Aug-2025 06:20:08 UTC] PHP   9. ToeicPractice\Frontend\Utils\TemplateLoader->load($template = 'toeic', $vars = [], $return = *uninitialized*) D:\wamp64\www\toeictest\wp-content\plugins\toeic-practice\src\Frontend\Utils\Router.php:265
[10-Aug-2025 06:20:08 UTC] PHP  10. include() D:\wamp64\www\toeictest\wp-content\plugins\toeic-practice\src\Frontend\Utils\TemplateLoader.php:65
[10-Aug-2025 06:20:08 UTC] PHP  11. include_once() D:\wamp64\www\toeictest\wp-content\plugins\toeic-practice\templates\toeic.php:15
[10-Aug-2025 06:20:08 UTC] PHP  12. wp_title($sep = '|', $display = TRUE, $seplocation = 'right') D:\wamp64\www\toeictest\wp-content\plugins\toeic-practice\templates\parts\header.php:7
[10-Aug-2025 06:20:08 UTC] PHP  13. explode($separator = '%WP_TITLE_SEP%', $string = NULL) D:\wamp64\www\toeictest\wp-includes\general-template.php:1439
[10-Aug-2025 06:20:09 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 06:20:09 UTC] PHP Stack trace:
[10-Aug-2025 06:20:09 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 06:20:09 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 06:20:09 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 06:20:09 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 06:20:09 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 06:20:09 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 06:20:09 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 06:20:09 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 06:20:09 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 06:20:09 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 06:20:09 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 06:20:09 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 06:20:09 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 06:20:14 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 06:20:14 UTC] PHP Stack trace:
[10-Aug-2025 06:20:14 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 06:20:14 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 06:20:14 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 06:20:14 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 06:20:14 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 06:20:14 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 06:20:14 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 06:20:14 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 06:20:14 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 06:20:14 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 06:20:14 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 06:20:14 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 06:20:14 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 06:20:15 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 06:20:15 UTC] PHP Stack trace:
[10-Aug-2025 06:20:15 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 06:20:15 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 06:20:15 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 06:20:15 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 06:20:15 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 06:20:15 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 06:20:15 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 06:20:15 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 06:20:15 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 06:20:15 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 06:20:15 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 06:20:15 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 06:20:15 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 06:20:15 UTC] PHP Deprecated:  explode(): Passing null to parameter #2 ($string) of type string is deprecated in D:\wamp64\www\toeictest\wp-includes\general-template.php on line 1439
[10-Aug-2025 06:20:15 UTC] PHP Stack trace:
[10-Aug-2025 06:20:15 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 06:20:15 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 06:20:15 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:19
[10-Aug-2025 06:20:15 UTC] PHP   4. do_action($hook_name = 'template_redirect') D:\wamp64\www\toeictest\wp-includes\template-loader.php:13
[10-Aug-2025 06:20:15 UTC] PHP   5. WP_Hook->do_action($args = [0 => '']) D:\wamp64\www\toeictest\wp-includes\plugin.php:517
[10-Aug-2025 06:20:15 UTC] PHP   6. WP_Hook->apply_filters($value = '', $args = [0 => '']) D:\wamp64\www\toeictest\wp-includes\class-wp-hook.php:348
[10-Aug-2025 06:20:15 UTC] PHP   7. ToeicPractice\Frontend\Utils\Router->handleRoutes('') D:\wamp64\www\toeictest\wp-includes\class-wp-hook.php:324
[10-Aug-2025 06:20:15 UTC] PHP   8. ToeicPractice\Frontend\Utils\Router->loadRouteTemplate($template = 'toeic-pronunciation-topic', $data = []) D:\wamp64\www\toeictest\wp-content\plugins\toeic-practice\src\Frontend\Utils\Router.php:117
[10-Aug-2025 06:20:15 UTC] PHP   9. ToeicPractice\Frontend\Utils\TemplateLoader->load($template = 'toeic-pronunciation-topic', $vars = [], $return = *uninitialized*) D:\wamp64\www\toeictest\wp-content\plugins\toeic-practice\src\Frontend\Utils\Router.php:265
[10-Aug-2025 06:20:15 UTC] PHP  10. include() D:\wamp64\www\toeictest\wp-content\plugins\toeic-practice\src\Frontend\Utils\TemplateLoader.php:65
[10-Aug-2025 06:20:15 UTC] PHP  11. include_once() D:\wamp64\www\toeictest\wp-content\plugins\toeic-practice\templates\toeic-pronunciation-topic.php:60
[10-Aug-2025 06:20:15 UTC] PHP  12. wp_title($sep = '|', $display = TRUE, $seplocation = 'right') D:\wamp64\www\toeictest\wp-content\plugins\toeic-practice\templates\parts\header.php:7
[10-Aug-2025 06:20:15 UTC] PHP  13. explode($separator = '%WP_TITLE_SEP%', $string = NULL) D:\wamp64\www\toeictest\wp-includes\general-template.php:1439
[10-Aug-2025 06:20:21 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 06:20:21 UTC] PHP Stack trace:
[10-Aug-2025 06:20:21 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 06:20:21 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 06:20:21 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 06:20:21 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 06:20:21 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 06:20:21 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 06:20:21 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 06:20:21 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 06:20:21 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 06:20:21 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 06:20:21 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 06:20:21 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 06:20:21 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 06:20:22 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 06:20:22 UTC] PHP Stack trace:
[10-Aug-2025 06:20:22 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 06:20:22 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 06:20:22 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 06:20:22 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 06:20:22 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 06:20:22 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 06:20:22 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 06:20:22 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 06:20:22 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 06:20:22 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 06:20:22 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 06:20:22 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 06:20:22 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 06:20:22 UTC] PHP Deprecated:  explode(): Passing null to parameter #2 ($string) of type string is deprecated in D:\wamp64\www\toeictest\wp-includes\general-template.php on line 1439
[10-Aug-2025 06:20:22 UTC] PHP Stack trace:
[10-Aug-2025 06:20:22 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 06:20:22 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 06:20:22 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:19
[10-Aug-2025 06:20:22 UTC] PHP   4. do_action($hook_name = 'template_redirect') D:\wamp64\www\toeictest\wp-includes\template-loader.php:13
[10-Aug-2025 06:20:22 UTC] PHP   5. WP_Hook->do_action($args = [0 => '']) D:\wamp64\www\toeictest\wp-includes\plugin.php:517
[10-Aug-2025 06:20:22 UTC] PHP   6. WP_Hook->apply_filters($value = '', $args = [0 => '']) D:\wamp64\www\toeictest\wp-includes\class-wp-hook.php:348
[10-Aug-2025 06:20:22 UTC] PHP   7. ToeicPractice\Frontend\Utils\Router->handleRoutes('') D:\wamp64\www\toeictest\wp-includes\class-wp-hook.php:324
[10-Aug-2025 06:20:22 UTC] PHP   8. ToeicPractice\Frontend\Utils\Router->loadRouteTemplate($template = 'toeic-pronunciation-practice', $data = ['topic_id' => '1', 'item_id' => '33']) D:\wamp64\www\toeictest\wp-content\plugins\toeic-practice\src\Frontend\Utils\Router.php:117
[10-Aug-2025 06:20:22 UTC] PHP   9. ToeicPractice\Frontend\Utils\TemplateLoader->load($template = 'toeic-pronunciation-practice', $vars = ['topic_id' => '1', 'item_id' => '33'], $return = *uninitialized*) D:\wamp64\www\toeictest\wp-content\plugins\toeic-practice\src\Frontend\Utils\Router.php:265
[10-Aug-2025 06:20:22 UTC] PHP  10. include() D:\wamp64\www\toeictest\wp-content\plugins\toeic-practice\src\Frontend\Utils\TemplateLoader.php:65
[10-Aug-2025 06:20:22 UTC] PHP  11. include_once() D:\wamp64\www\toeictest\wp-content\plugins\toeic-practice\templates\toeic-pronunciation-practice.php:32
[10-Aug-2025 06:20:22 UTC] PHP  12. wp_title($sep = '|', $display = TRUE, $seplocation = 'right') D:\wamp64\www\toeictest\wp-content\plugins\toeic-practice\templates\parts\header.php:7
[10-Aug-2025 06:20:22 UTC] PHP  13. explode($separator = '%WP_TITLE_SEP%', $string = NULL) D:\wamp64\www\toeictest\wp-includes\general-template.php:1439
[10-Aug-2025 06:20:38 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 06:20:38 UTC] PHP Stack trace:
[10-Aug-2025 06:20:38 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:0
[10-Aug-2025 06:20:38 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-admin\admin-ajax.php:22
[10-Aug-2025 06:20:38 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 06:20:38 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 06:20:38 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 06:20:38 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 06:20:38 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 06:20:38 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 06:20:38 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 06:20:38 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 06:20:38 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 06:20:38 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 06:40:43 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 06:40:43 UTC] PHP Stack trace:
[10-Aug-2025 06:40:43 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 06:40:43 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 06:40:43 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:13
[10-Aug-2025 06:40:43 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 06:40:43 UTC] PHP   5. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 06:40:43 UTC] PHP   6. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 06:40:43 UTC] PHP   7. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 06:40:43 UTC] PHP   8. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 06:40:43 UTC] PHP   9. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 06:40:43 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 06:40:43 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 06:40:43 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 06:40:43 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 06:40:44 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in D:\wamp64\www\toeictest\wp-includes\functions.php on line 6121
[10-Aug-2025 06:40:44 UTC] PHP Stack trace:
[10-Aug-2025 06:40:44 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\wp-cron.php:0
[10-Aug-2025 06:40:44 UTC] PHP   2. require_once() D:\wamp64\www\toeictest\wp-cron.php:46
[10-Aug-2025 06:40:44 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-load.php:50
[10-Aug-2025 06:40:44 UTC] PHP   4. require_once() D:\wamp64\www\toeictest\wp-config.php:104
[10-Aug-2025 06:40:44 UTC] PHP   5. include_once() D:\wamp64\www\toeictest\wp-settings.php:545
[10-Aug-2025 06:40:44 UTC] PHP   6. __($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-content\plugins\custom-admin-interface-pro\custom-admin-interface-pro.php:38
[10-Aug-2025 06:40:44 UTC] PHP   7. translate($text = 'Admin Menu', $domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:307
[10-Aug-2025 06:40:44 UTC] PHP   8. get_translations_for_domain($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:195
[10-Aug-2025 06:40:44 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'custom-admin-interface-pro') D:\wamp64\www\toeictest\wp-includes\l10n.php:1409
[10-Aug-2025 06:40:44 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') D:\wamp64\www\toeictest\wp-includes\l10n.php:1371
[10-Aug-2025 06:40:44 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = *uninitialized*) D:\wamp64\www\toeictest\wp-includes\functions.php:6061
[10-Aug-2025 06:40:44 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>custom-admin-interface-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in'..., $error_level = 1024) D:\wamp64\www\toeictest\wp-includes\functions.php:6121
[10-Aug-2025 06:40:44 UTC] PHP Deprecated:  explode(): Passing null to parameter #2 ($string) of type string is deprecated in D:\wamp64\www\toeictest\wp-includes\general-template.php on line 1439
[10-Aug-2025 06:40:44 UTC] PHP Stack trace:
[10-Aug-2025 06:40:44 UTC] PHP   1. {main}() D:\wamp64\www\toeictest\index.php:0
[10-Aug-2025 06:40:44 UTC] PHP   2. require() D:\wamp64\www\toeictest\index.php:17
[10-Aug-2025 06:40:44 UTC] PHP   3. require_once() D:\wamp64\www\toeictest\wp-blog-header.php:19
[10-Aug-2025 06:40:44 UTC] PHP   4. do_action($hook_name = 'template_redirect') D:\wamp64\www\toeictest\wp-includes\template-loader.php:13
[10-Aug-2025 06:40:44 UTC] PHP   5. WP_Hook->do_action($args = [0 => '']) D:\wamp64\www\toeictest\wp-includes\plugin.php:517
[10-Aug-2025 06:40:44 UTC] PHP   6. WP_Hook->apply_filters($value = '', $args = [0 => '']) D:\wamp64\www\toeictest\wp-includes\class-wp-hook.php:348
[10-Aug-2025 06:40:44 UTC] PHP   7. ToeicPractice\Frontend\Utils\Router->handleRoutes('') D:\wamp64\www\toeictest\wp-includes\class-wp-hook.php:324
[10-Aug-2025 06:40:44 UTC] PHP   8. ToeicPractice\Frontend\Utils\Router->loadRouteTemplate($template = 'toeic', $data = []) D:\wamp64\www\toeictest\wp-content\plugins\toeic-practice\src\Frontend\Utils\Router.php:117
[10-Aug-2025 06:40:44 UTC] PHP   9. ToeicPractice\Frontend\Utils\TemplateLoader->load($template = 'toeic', $vars = [], $return = *uninitialized*) D:\wamp64\www\toeictest\wp-content\plugins\toeic-practice\src\Frontend\Utils\Router.php:265
[10-Aug-2025 06:40:44 UTC] PHP  10. include() D:\wamp64\www\toeictest\wp-content\plugins\toeic-practice\src\Frontend\Utils\TemplateLoader.php:65
[10-Aug-2025 06:40:44 UTC] PHP  11. include_once() D:\wamp64\www\toeictest\wp-content\plugins\toeic-practice\templates\toeic.php:15
[10-Aug-2025 06:40:44 UTC] PHP  12. wp_title($sep = '|', $display = TRUE, $seplocation = 'right') D:\wamp64\www\toeictest\wp-content\plugins\toeic-practice\templates\parts\header.php:7
[10-Aug-2025 06:40:44 UTC] PHP  13. explode($separator = '%WP_TITLE_SEP%', $string = NULL) D:\wamp64\www\toeictest\wp-includes\general-template.php:1439
