# TOEIC Practice Plugin - User Roles

This document describes the custom user roles added by the TOEIC Practice plugin.

## Overview

The plugin adds two new WordPress user roles:
- **Student**: For learners taking TOEIC tests and enrolling in classes
- **Instructor**: For teachers managing classes and creating content

## Student Role

### Basic WordPress Capabilities
- `read`: Can access the WordPress dashboard
- Cannot create, edit, or delete posts
- Cannot upload files to media library

### Custom TOEIC Capabilities
- `take_toeic_tests`: Can take TOEIC practice tests
- `view_test_results`: Can view their own test results
- `enroll_in_classes`: Can enroll in available classes
- `view_class_materials`: Can access class materials and content
- `submit_assignments`: Can submit assignments and homework
- `view_own_progress`: Can view their learning progress and statistics

## Instructor Role

### Basic WordPress Capabilities
- `read`: Can access the WordPress dashboard
- `edit_posts`: Can create and edit posts
- `delete_posts`: Can delete their own posts
- `publish_posts`: Can publish posts
- `upload_files`: Can upload files to media library
- `edit_published_posts`: Can edit their published posts
- `delete_published_posts`: Can delete their published posts
- Cannot edit or delete other users' posts
- Cannot manage pages or categories

### Custom TOEIC Capabilities
- `create_toeic_tests`: Can create new TOEIC practice tests
- `edit_toeic_tests`: Can edit existing tests
- `delete_toeic_tests`: Can delete tests
- `manage_classes`: Can manage class settings and information
- `create_classes`: Can create new classes
- `edit_classes`: Can edit class details
- `delete_classes`: Can delete classes
- `view_student_progress`: Can view progress of enrolled students
- `grade_assignments`: Can grade student assignments
- `manage_enrollments`: Can manage student enrollments
- `view_class_analytics`: Can view class performance analytics
- `export_student_data`: Can export student data and reports

## Administrator Role

Administrators automatically receive all Student and Instructor capabilities in addition to their existing WordPress capabilities.

## Database Tables

The plugin creates two new database tables to support the class management system:

### wp_class
Stores class information including:
- Course code and title
- Instructor assignment
- Schedule and location
- Enrollment limits
- Start/end dates
- Class status

### wp_class_enrollment
Stores student enrollment information including:
- Class and student relationships
- Enrollment status
- Grades and completion dates
- Enrollment timestamps

## Usage

### For Students
1. Users with the Student role can:
   - Take TOEIC practice tests
   - View their test results and progress
   - Enroll in available classes
   - Access class materials
   - Submit assignments

### For Instructors
1. Users with the Instructor role can:
   - Create and manage TOEIC tests
   - Create and manage classes
   - View student progress and analytics
   - Grade assignments
   - Manage student enrollments
   - Export student data

### For Administrators
1. Administrators can perform all Student and Instructor functions
2. Additionally, they can manage the plugin settings and user roles

## Installation Notes

- User roles are created automatically when the plugin is activated
- User roles are removed when the plugin is deactivated
- Database tables are created during plugin activation
- All plugin data is removed when the plugin is uninstalled

## Security

The plugin implements proper capability checks to ensure:
- Students can only access their own data
- Instructors can only manage their assigned classes
- Proper permission checks for all TOEIC-related functions
- Data isolation between different user roles
