<?php
/**
 * Plugin Uninstaller
 *
 * @package ToeicPractice
 */

namespace ToeicPractice;

/**
 * Plugin Uninstaller Class
 * 
 * Handles plugin uninstallation tasks such as removing database tables,
 * deleting options, and cleaning up files.
 */
class Uninstaller {
    
    /**
     * Uninstall the plugin
     * 
     * This method is called when the plugin is uninstalled.
     * It performs complete cleanup of all plugin data.
     */
    public static function uninstall() {
        // Check if user has permission to uninstall plugins
        if (!current_user_can('activate_plugins')) {
            return;
        }
        
        // Check if this is a valid uninstall request
        if (!defined('WP_UNINSTALL_PLUGIN')) {
            return;
        }
        
        // Remove database tables
        self::removeDatabaseTables();

        // Remove custom user roles
        self::removeUserRoles();

        // Remove plugin options
        self::removeOptions();
        
        // Remove user meta data
        self::removeUserMeta();
        
        // Remove uploaded files
        self::removeUploadedFiles();
        
        // Remove transients
        self::removeTransients();
        
        // Clear any remaining cache
        self::clearCache();
    }
    
    /**
     * Remove database tables
     */
    private static function removeDatabaseTables() {
        global $wpdb;
        
        $tables = [
            $wpdb->prefix . 'toeic_user_progress',
            $wpdb->prefix . 'toeic_vocabulary',
            $wpdb->prefix . 'toeic_questions',
            $wpdb->prefix . 'toeic_question_meta',
            $wpdb->prefix . 'toeic_tests',
            $wpdb->prefix . 'toeic_sections',
            $wpdb->prefix . 'toeic_section_questions',
            $wpdb->prefix . 'toeic_results',
            $wpdb->prefix . 'toeic_pronunciation_topics',
            $wpdb->prefix . 'toeic_pronunciation_topic_items',
            $wpdb->prefix . 'class_enrollment',
            $wpdb->prefix . 'class'
        ];
        
        foreach ($tables as $table) {
            $wpdb->query("DROP TABLE IF EXISTS $table");
        }
    }

    /**
     * Remove custom user roles
     */
    private static function removeUserRoles() {
        // Remove custom user roles
        remove_role('student');
        remove_role('instructor');

        // Remove custom capabilities from administrator role
        $admin_role = get_role('administrator');
        if ($admin_role) {
            // Student capabilities
            $admin_role->remove_cap('take_toeic_tests');
            $admin_role->remove_cap('view_test_results');
            $admin_role->remove_cap('enroll_in_classes');
            $admin_role->remove_cap('view_class_materials');
            $admin_role->remove_cap('submit_assignments');
            $admin_role->remove_cap('view_own_progress');

            // Instructor capabilities
            $admin_role->remove_cap('create_toeic_tests');
            $admin_role->remove_cap('edit_toeic_tests');
            $admin_role->remove_cap('delete_toeic_tests');
            $admin_role->remove_cap('manage_classes');
            $admin_role->remove_cap('create_classes');
            $admin_role->remove_cap('edit_classes');
            $admin_role->remove_cap('delete_classes');
            $admin_role->remove_cap('view_student_progress');
            $admin_role->remove_cap('grade_assignments');
            $admin_role->remove_cap('manage_enrollments');
            $admin_role->remove_cap('view_class_analytics');
            $admin_role->remove_cap('export_student_data');
        }
    }

    /**
     * Remove plugin options
     */
    private static function removeOptions() {
        $options = [
            'toeic_practice_version',
            'toeic_practice_activated_time',
            'toeic_practice_deactivated_time',
            'toeic_practice_enable_listening',
            'toeic_practice_enable_reading',
            'toeic_practice_time_limit',
            'toeic_practice_questions_per_test',
            'toeic_practice_show_explanations',
            'toeic_practice_allow_retakes',
            'toeic_practice_save_progress',
            'toeic_practice_difficulty_levels',
            'toeic_practice_settings',
            'toeic_practice_statistics'
        ];
        
        foreach ($options as $option) {
            delete_option($option);
        }
        
        // Remove any options that start with our prefix
        $wpdb = $GLOBALS['wpdb'];
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE 'toeic_practice_%'");
    }
    
    /**
     * Remove user meta data
     */
    private static function removeUserMeta() {
        global $wpdb;
        
        $meta_keys = [
            'toeic_practice_progress',
            'toeic_practice_preferences',
            'toeic_practice_last_test',
            'toeic_practice_statistics'
        ];
        
        foreach ($meta_keys as $meta_key) {
            delete_metadata('user', 0, $meta_key, '', true);
        }
        
        // Remove any user meta that starts with our prefix
        $wpdb->query("DELETE FROM {$wpdb->usermeta} WHERE meta_key LIKE 'toeic_practice_%'");
    }
    
    /**
     * Remove uploaded files and directories
     */
    private static function removeUploadedFiles() {
        $upload_dir = wp_upload_dir();
        $toeic_dir = $upload_dir['basedir'] . '/toeic-practice';
        
        if (file_exists($toeic_dir)) {
            self::removeDirectory($toeic_dir);
        }
    }
    
    /**
     * Recursively remove directory and its contents
     *
     * @param string $dir Directory path
     */
    private static function removeDirectory($dir) {
        if (!is_dir($dir)) {
            return;
        }
        
        $files = array_diff(scandir($dir), ['.', '..']);
        
        foreach ($files as $file) {
            $path = $dir . '/' . $file;
            if (is_dir($path)) {
                self::removeDirectory($path);
            } else {
                unlink($path);
            }
        }
        
        rmdir($dir);
    }
    
    /**
     * Remove transients
     */
    private static function removeTransients() {
        global $wpdb;
        
        // Remove specific transients
        $transients = [
            'toeic_practice_statistics',
            'toeic_practice_leaderboard',
            'toeic_practice_user_progress',
            'toeic_practice_cache'
        ];
        
        foreach ($transients as $transient) {
            delete_transient($transient);
            delete_site_transient($transient);
        }
        
        // Remove any transients that start with our prefix
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_toeic_practice_%'");
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_toeic_practice_%'");
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_site_transient_toeic_practice_%'");
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_site_transient_timeout_toeic_practice_%'");
    }
    
    /**
     * Clear cache
     */
    private static function clearCache() {
        // Clear object cache if available
        if (function_exists('wp_cache_flush')) {
            wp_cache_flush();
        }
        
        // Clear any external cache if available
        if (function_exists('w3tc_flush_all')) {
            \w3tc_flush_all();
        }

        if (function_exists('wp_cache_clear_cache')) {
            \wp_cache_clear_cache();
        }
    }
}
