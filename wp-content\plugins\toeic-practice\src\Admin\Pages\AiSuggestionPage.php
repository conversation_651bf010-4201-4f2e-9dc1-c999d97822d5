<?php
/**
 * AI Suggestion Page
 *
 * @package ToeicPractice\Admin\Pages
 */

namespace ToeicPractice\Admin\Pages;

/**
 * AI Suggestion Page Class
 * 
 * Handles the display and functionality of the AI suggestion management page.
 */
class AiSuggestionPage {
    
    /**
     * Display the AI suggestion page
     */
    public function display() {
        // Process form submission
        if (isset($_POST['submit_suggestions'])) {
            $this->processFormSubmission();
        }
        
        // Get current suggestions
        $suggestions = $this->getCurrentSuggestions();
        
        // Display the page
        $this->renderPage($suggestions);
    }
    
    /**
     * Process form submission
     */
    private function processFormSubmission() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['ai_suggestion_nonce'], 'ai_suggestion_save')) {
            wp_die(__('Security check failed', 'toeic-practice'));
        }
        
        // Get suggestions from form
        $suggestions = [];
        if (isset($_POST['suggestions']) && is_array($_POST['suggestions'])) {
            foreach ($_POST['suggestions'] as $suggestion) {
                $text = sanitize_textarea_field($suggestion['text']);
                if (!empty($text)) {
                    $suggestions[] = $text;
                }
            }
        }
        
        // Save suggestions
        update_option('toeic_practice_ai_suggestion_list', $suggestions);
        
        // Redirect with success message
        $redirect_url = add_query_arg([
            'page' => 'toeic-practice-ai-suggestions',
            'message' => urlencode(__('AI suggestions saved successfully!', 'toeic-practice'))
        ], admin_url('admin.php'));
        
        wp_redirect($redirect_url);
        exit;
    }
    
    /**
     * Get current suggestions
     */
    private function getCurrentSuggestions() {
        $suggestions = get_option('toeic_practice_ai_suggestion_list', []);
        
        // Ensure we have at least one empty suggestion for new entries
        if (empty($suggestions)) {
            $suggestions = [''];
        }
        
        return $suggestions;
    }
    
    /**
     * Render the page
     */
    private function renderPage($suggestions) {
        ?>
        <div class="wrap toeic-practice-ai-suggestions">
            <h1><?php echo esc_html__('AI Suggestions', 'toeic-practice'); ?></h1>
            
            <?php if (isset($_GET['message'])): ?>
                <div class="notice notice-success is-dismissible">
                    <p><?php echo esc_html(urldecode($_GET['message'])); ?></p>
                </div>
            <?php endif; ?>
            
            <div class="ai-suggestions-description">
                <p><?php echo esc_html__('Manage AI suggestion text that will be used throughout the TOEIC practice system. You can add, remove, and reorder suggestions using the interface below.', 'toeic-practice'); ?></p>
            </div>
            
            <form method="post" id="ai-suggestions-form">
                <?php wp_nonce_field('ai_suggestion_save', 'ai_suggestion_nonce'); ?>
                
                <div class="ai-suggestions-container">
                    <div class="suggestions-header">
                        <h2><?php echo esc_html__('Suggestion List', 'toeic-practice'); ?></h2>
                        <button type="button" id="add-suggestion" class="button button-secondary">
                            <span class="dashicons dashicons-plus-alt"></span>
                            <?php echo esc_html__('Add Suggestion', 'toeic-practice'); ?>
                        </button>
                    </div>
                    
                    <div id="suggestions-list" class="suggestions-list">
                        <?php foreach ($suggestions as $index => $suggestion): ?>
                            <div class="suggestion-item" data-index="<?php echo $index; ?>">
                                <div class="suggestion-header">
                                    <span class="dashicons dashicons-menu handle" title="<?php echo esc_attr__('Drag to reorder', 'toeic-practice'); ?>"></span>
                                    <span class="suggestion-number"><?php echo $index + 1; ?></span>
                                    <button type="button" class="remove-suggestion button-link-delete" title="<?php echo esc_attr__('Remove suggestion', 'toeic-practice'); ?>">
                                        <span class="dashicons dashicons-trash"></span>
                                    </button>
                                </div>
                                <div class="suggestion-content">
                                    <textarea 
                                        name="suggestions[<?php echo $index; ?>][text]" 
                                        class="suggestion-text large-text" 
                                        rows="3" 
                                        placeholder="<?php echo esc_attr__('Enter your AI suggestion text here...', 'toeic-practice'); ?>"
                                    ><?php echo esc_textarea($suggestion); ?></textarea>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <div class="suggestions-actions">
                        <p class="submit">
                            <input type="submit" name="submit_suggestions" id="submit" class="button button-primary" value="<?php echo esc_attr__('Save Suggestions', 'toeic-practice'); ?>">
                        </p>
                    </div>
                </div>
            </form>
        </div>
        
        <?php $this->renderStyles(); ?>
        <?php
    }
    
    /**
     * Render page styles
     */
    private function renderStyles() {
        ?>
        <style>
        .toeic-practice-ai-suggestions {
            max-width: 1200px;
        }
        
        .ai-suggestions-description {
            background: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .ai-suggestions-container {
            background: #fff;
            border: 1px solid #ccd0d4;
            border-radius: 4px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .suggestions-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #ddd;
        }
        
        .suggestions-header h2 {
            margin: 0;
            font-size: 18px;
        }
        
        .suggestions-list {
            min-height: 100px;
        }
        
        .suggestion-item {
            background: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 15px;
            padding: 15px;
            position: relative;
            transition: all 0.3s ease;
        }
        
        .suggestion-item:hover {
            background: #f5f5f5;
            border-color: #999;
        }
        
        .suggestion-item.ui-sortable-helper {
            background: #fff;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            transform: rotate(2deg);
        }
        
        .suggestion-header {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .handle {
            cursor: move;
            color: #666;
            margin-right: 10px;
            font-size: 16px;
        }
        
        .handle:hover {
            color: #0073aa;
        }
        
        .suggestion-number {
            font-weight: 600;
            color: #0073aa;
            margin-right: auto;
            font-size: 14px;
        }
        
        .remove-suggestion {
            color: #d63638;
            text-decoration: none;
            padding: 2px;
        }
        
        .remove-suggestion:hover {
            color: #d63638;
        }
        
        .suggestion-text {
            width: 100%;
            resize: vertical;
            min-height: 60px;
        }
        
        .suggestions-actions {
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #ddd;
        }
        
        .ui-state-highlight {
            height: 80px;
            background: #e6f3ff;
            border: 2px dashed #0073aa;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        
        #suggestions-list.ui-sortable .suggestion-item {
            cursor: move;
        }
        
        .empty-suggestions {
            text-align: center;
            padding: 40px;
            color: #666;
            font-style: italic;
        }
        </style>
        <?php
    }
}
