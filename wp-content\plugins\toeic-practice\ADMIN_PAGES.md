# TOEIC Practice Plugin - Admin Pages

This document describes the admin pages added for managing students and instructors.

## Overview

The plugin adds two new admin pages under the TOEIC Practice menu:
- **Students**: View and manage student users
- **Instructors**: View and manage instructor users

## Students Page

### Location
`TOEIC Practice > Students` in the WordPress admin menu

### Features
- **List View**: Displays all users with the "Student" role
- **Search**: Search students by username, email, or display name
- **Pagination**: Shows 20 students per page with navigation
- **Bulk Actions**: Delete multiple students at once
- **Individual Actions**: Edit or delete individual students

### Columns Displayed
- **Username**: Student's login name (clickable to edit)
- **Name**: Student's display name
- **Email**: Student's email address (clickable mailto link)
- **Registered**: Date when the student registered
- **Classes Enrolled**: Number of classes the student is enrolled in

### Actions Available
- **Add New Student**: Redirects to WordPress user creation page with "student" role pre-selected
- **Edit**: Redirects to WordPress user edit page
- **Delete**: Removes the student user (with confirmation)
- **Bulk Delete**: Remove multiple students at once

## Instructors Page

### Location
`TOEIC Practice > Instructors` in the WordPress admin menu

### Features
- **List View**: Displays all users with the "Instructor" role
- **Search**: Search instructors by username, email, or display name
- **Pagination**: Shows 20 instructors per page with navigation
- **Bulk Actions**: Delete multiple instructors at once
- **Individual Actions**: Edit or delete individual instructors

### Columns Displayed
- **Username**: Instructor's login name (clickable to edit)
- **Name**: Instructor's display name
- **Email**: Instructor's email address (clickable mailto link)
- **Registered**: Date when the instructor registered
- **Classes Teaching**: Number of classes the instructor is teaching

### Actions Available
- **Add New Instructor**: Redirects to WordPress user creation page with "instructor" role pre-selected
- **Edit**: Redirects to WordPress user edit page
- **Delete**: Removes the instructor user (with confirmation)
- **Bulk Delete**: Remove multiple instructors at once

## Technical Implementation

### File Structure
```
src/Admin/Pages/
├── StudentPage.php      # Handles student listing and management
└── InstructorPage.php   # Handles instructor listing and management
```

### Database Queries
- Students page queries users with `role = 'student'`
- Instructors page queries users with `role = 'instructor'`
- Class counts are retrieved from the `wp_class` and `wp_class_enrollment` tables

### Security Features
- **Capability Checks**: Only users with `manage_options` capability can access these pages
- **Nonce Verification**: All actions are protected with WordPress nonces
- **Input Sanitization**: All user inputs are properly sanitized
- **SQL Injection Prevention**: Uses prepared statements for database queries

### JavaScript Enhancements
The admin.js file includes:
- **Role Parameter Handling**: Automatically selects the correct role when creating new users
- **Bulk Action Confirmation**: Confirms before deleting multiple users
- **Select All Functionality**: Checkbox to select/deselect all items
- **Form Validation**: Prevents submission without selecting items for bulk actions

## Usage Instructions

### For Administrators

#### Adding a New Student
1. Go to `TOEIC Practice > Students`
2. Click "Add New Student"
3. Fill out the user creation form (role will be pre-selected as "Student")
4. Click "Add New User"

#### Adding a New Instructor
1. Go to `TOEIC Practice > Instructors`
2. Click "Add New Instructor"
3. Fill out the user creation form (role will be pre-selected as "Instructor")
4. Click "Add New User"

#### Managing Existing Users
1. Navigate to the appropriate page (Students or Instructors)
2. Use the search box to find specific users
3. Click on usernames to edit user details
4. Use row actions (Edit/Delete) for individual users
5. Use bulk actions for multiple users

#### Viewing User Statistics
- The "Classes Enrolled" column shows how many classes each student is enrolled in
- The "Classes Teaching" column shows how many classes each instructor is teaching
- Click on the numbers to see more details (future enhancement)

## Permissions Required

### To Access These Pages
- `manage_options` capability (typically Administrators only)

### To Perform Actions
- **View Lists**: `manage_options`
- **Edit Users**: `edit_users` (inherited from WordPress)
- **Delete Users**: `delete_users` (inherited from WordPress)
- **Create Users**: `create_users` (inherited from WordPress)

## Integration with WordPress

### User Management
- Uses WordPress's built-in user system
- Leverages WordPress user roles and capabilities
- Redirects to standard WordPress user edit/create pages
- Maintains compatibility with other user management plugins

### Styling
- Uses WordPress admin styling classes
- Follows WordPress admin UI patterns
- Responsive design for mobile devices
- Consistent with other WordPress admin tables

## Future Enhancements

Potential improvements that could be added:
- Export user lists to CSV
- Import users from CSV
- Advanced filtering options
- User activity tracking
- Email communication tools
- Class assignment interface
- Progress reporting integration
