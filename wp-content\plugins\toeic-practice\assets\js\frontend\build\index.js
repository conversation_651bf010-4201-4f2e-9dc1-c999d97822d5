(function webpackUniversalModuleDefinition(root, factory) {
	if(typeof exports === 'object' && typeof module === 'object')
		module.exports = factory();
	else if(typeof define === 'function' && define.amd)
		define([], factory);
	else if(typeof exports === 'object')
		exports["ToeicPractice"] = factory();
	else
		root["ToeicPractice"] = factory();
})(self, () => {
return /******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./assets/js/frontend/src/components/Loader.js":
/*!*****************************************************!*\
  !*** ./assets/js/frontend/src/components/Loader.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }
function _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError("Cannot call a class as a function"); }
function _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, "value" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }
function _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, "prototype", { writable: !1 }), e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
/**
 * Loader Component
 * 
 * A reusable loading indicator component that can be shown during
 * long-running processes like AJAX requests.
 */
var Loader = /*#__PURE__*/function () {
  /**
   * Constructor
   * 
   * @param {Object} options - Configuration options
   * @param {string} options.containerId - ID of the container element (default: 'toeic-loader-container')
   * @param {string} options.text - Loading text to display (default: 'Loading...')
   * @param {string} options.size - Size of the loader ('small', 'medium', 'large') (default: 'medium')
   * @param {boolean} options.fullScreen - Whether to show loader as a fullscreen overlay (default: false)
   */
  function Loader() {
    var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
    _classCallCheck(this, Loader);
    this.options = {
      containerId: options.containerId || 'toeic-loader-container',
      text: options.text || 'Loading...',
      size: options.size || 'medium',
      fullScreen: options.fullScreen || false
    };
    this.container = null;
    this.loader = null;
    this.isVisible = false;
    this.init();
  }

  /**
   * Initialize the loader
   */
  return _createClass(Loader, [{
    key: "init",
    value: function init() {
      // Check if container already exists
      this.container = document.getElementById(this.options.containerId);

      // If container doesn't exist, create it
      if (!this.container) {
        this.container = document.createElement('div');
        this.container.id = this.options.containerId;
        this.container.className = 'toeic-loader-container';
        if (this.options.fullScreen) {
          this.container.classList.add('fullscreen');
        }
        document.body.appendChild(this.container);
      }

      // Create loader element
      this.createLoader();
    }

    /**
     * Create the loader element
     */
  }, {
    key: "createLoader",
    value: function createLoader() {
      this.loader = document.createElement('div');
      this.loader.className = "toeic-loader ".concat(this.options.size);

      // Create spinner
      var spinner = document.createElement('div');
      spinner.className = 'toeic-spinner';
      this.loader.appendChild(spinner);

      // Create text element if text is provided
      if (this.options.text) {
        var textElement = document.createElement('div');
        textElement.className = 'toeic-loader-text';
        textElement.textContent = this.options.text;
        this.loader.appendChild(textElement);
      }

      // Add to container but keep hidden initially
      this.container.appendChild(this.loader);
      this.hide();
    }

    /**
     * Show the loader
     * 
     * @param {string} text - Optional text to update the loader with
     */
  }, {
    key: "show",
    value: function show() {
      var text = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;
      if (text) {
        this.updateText(text);
      }
      this.container.classList.add('show');
      this.isVisible = true;
      return this;
    }

    /**
     * Hide the loader
     */
  }, {
    key: "hide",
    value: function hide() {
      this.container.style.display = 'none';
      this.isVisible = false;
      return this;
    }

    /**
     * Update the loader text
     * 
     * @param {string} text - New text to display
     */
  }, {
    key: "updateText",
    value: function updateText(text) {
      var textElement = this.loader.querySelector('.toeic-loader-text');
      if (textElement) {
        textElement.textContent = text;
      } else if (text) {
        // Create text element if it doesn't exist
        var newTextElement = document.createElement('div');
        newTextElement.className = 'toeic-loader-text';
        newTextElement.textContent = text;
        this.loader.appendChild(newTextElement);
      }
      return this;
    }

    /**
     * Toggle the loader visibility
     * 
     * @param {string} text - Optional text to update the loader with
     */
  }, {
    key: "toggle",
    value: function toggle() {
      var text = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;
      if (this.isVisible) {
        this.hide();
      } else {
        this.show(text);
      }
      return this;
    }

    /**
     * Show the loader for a specific duration
     * 
     * @param {number} duration - Duration in milliseconds
     * @param {string} text - Optional text to update the loader with
     * @returns {Promise} - Promise that resolves when the loader is hidden
     */
  }, {
    key: "showFor",
    value: function showFor(duration) {
      var _this = this;
      var text = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;
      return new Promise(function (resolve) {
        _this.show(text);
        setTimeout(function () {
          _this.hide();
          resolve();
        }, duration);
      });
    }

    /**
     * Create a loader instance for a specific element
     * 
     * @param {HTMLElement|string} element - Element or element selector to show loader in
     * @param {Object} options - Loader options
     * @returns {Loader} - New Loader instance
     */
  }], [{
    key: "forElement",
    value: function forElement(element) {
      var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
      // If element is a string, treat it as a selector
      if (typeof element === 'string') {
        element = document.querySelector(element);
      }
      if (!element) {
        console.error('Element not found for Loader.forElement');
        return null;
      }

      // Generate a unique ID for this loader
      var uniqueId = 'toeic-loader-' + Math.random().toString(36).substr(2, 9);

      // Make sure the element has position relative for proper loader positioning
      var computedStyle = window.getComputedStyle(element);
      if (computedStyle.position === 'static') {
        element.style.position = 'relative';
      }

      // Create loader container inside the element
      var container = document.createElement('div');
      container.id = uniqueId;
      container.className = 'toeic-loader-container';
      element.appendChild(container);

      // Create and return loader instance
      return new Loader(_objectSpread(_objectSpread({}, options), {}, {
        containerId: uniqueId,
        fullScreen: false
      }));
    }
  }]);
}();
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Loader);

/***/ }),

/***/ "./assets/js/frontend/src/components/Sidebar.js":
/*!******************************************************!*\
  !*** ./assets/js/frontend/src/components/Sidebar.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError("Cannot call a class as a function"); }
function _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, "value" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }
function _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, "prototype", { writable: !1 }), e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
/**
 * Sidebar Component
 * 
 * Handles the left sidebar navigation that's common across all pages
 * in the TOEIC Practice plugin frontend.
 */
var Sidebar = /*#__PURE__*/function () {
  /**
   * Constructor
   * 
   * @param {HTMLElement} element - The sidebar container element
   */
  function Sidebar(element) {
    _classCallCheck(this, Sidebar);
    this.element = element;
    this.menuItems = this.element.querySelectorAll('.toeic-menu-item');
    this.sidebarToggle = this.element.querySelector('.toeic-sidebar__toggle');
  }

  /**
   * Initialize the sidebar
   */
  return _createClass(Sidebar, [{
    key: "init",
    value: function init() {
      this.setupEventListeners();
      this.highlightCurrentPage();
    }

    /**
     * Set up event listeners for menu items
     */
  }, {
    key: "setupEventListeners",
    value: function setupEventListeners() {
      var _this = this;
      this.menuItems.forEach(function (item) {
        item.addEventListener('click', _this.handleMenuItemClick.bind(_this));
      });
      this.element.addEventListener('click', function (e) {
        e.stopPropagation();
      });
      this.sidebarToggle.addEventListener('click', this.handleSidebarToggle.bind(this));

      // when click on document, hide sidebar
      document.addEventListener('click', this.handleDocumentClick.bind(this));
    }

    /**
     * Handle document click
     */
  }, {
    key: "handleDocumentClick",
    value: function handleDocumentClick() {
      this.element.classList.remove('show');
    }

    /**
     * Handle sidebar toggle
     */
  }, {
    key: "handleSidebarToggle",
    value: function handleSidebarToggle() {
      this.element.classList.toggle('show');
    }

    /**
     * Handle menu item click
     * 
     * @param {Event} event - Click event
     */
  }, {
    key: "handleMenuItemClick",
    value: function handleMenuItemClick(event) {
      // Remove active class from all menu items
      this.menuItems.forEach(function (item) {
        item.classList.remove('active');
      });

      // Add active class to clicked menu item
      event.currentTarget.classList.add('active');
    }

    /**
     * Highlight the current page in the menu
     */
  }, {
    key: "highlightCurrentPage",
    value: function highlightCurrentPage() {
      // Get current page path
      var currentPath = window.location.pathname;

      // Find and highlight the corresponding menu item
      this.menuItems.forEach(function (item) {
        var link = item.querySelector('a');
        if (link && link.getAttribute('href') === currentPath) {
          item.classList.add('active');
        }
      });
    }
  }]);
}();
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Sidebar);

/***/ }),

/***/ "./assets/js/frontend/src/components/questions/MatchingRenderer.js":
/*!*************************************************************************!*\
  !*** ./assets/js/frontend/src/components/questions/MatchingRenderer.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _QuestionRenderer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./QuestionRenderer */ "./assets/js/frontend/src/components/questions/QuestionRenderer.js");
function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError("Cannot call a class as a function"); }
function _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, "value" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }
function _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, "prototype", { writable: !1 }), e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
function _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }
function _possibleConstructorReturn(t, e) { if (e && ("object" == _typeof(e) || "function" == typeof e)) return e; if (void 0 !== e) throw new TypeError("Derived constructors may only return object or undefined"); return _assertThisInitialized(t); }
function _assertThisInitialized(e) { if (void 0 === e) throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); return e; }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
function _getPrototypeOf(t) { return _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function (t) { return t.__proto__ || Object.getPrototypeOf(t); }, _getPrototypeOf(t); }
function _inherits(t, e) { if ("function" != typeof e && null !== e) throw new TypeError("Super expression must either be null or a function"); t.prototype = Object.create(e && e.prototype, { constructor: { value: t, writable: !0, configurable: !0 } }), Object.defineProperty(t, "prototype", { writable: !1 }), e && _setPrototypeOf(t, e); }
function _setPrototypeOf(t, e) { return _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function (t, e) { return t.__proto__ = e, t; }, _setPrototypeOf(t, e); }
/**
 * Matching Question Renderer
 * 
 * Handles rendering of matching questions where users match items from two columns.
 */


var MatchingRenderer = /*#__PURE__*/function (_QuestionRenderer) {
  function MatchingRenderer() {
    _classCallCheck(this, MatchingRenderer);
    return _callSuper(this, MatchingRenderer, arguments);
  }
  _inherits(MatchingRenderer, _QuestionRenderer);
  return _createClass(MatchingRenderer, [{
    key: "render",
    value:
    /**
     * Render the matching question
     */
    function render() {
      var _this = this;
      this.clearContainer();

      // Parse items if needed
      var items = [];
      try {
        items = typeof this.question.items === 'string' ? JSON.parse(this.question.items) : this.question.items;
      } catch (e) {
        console.error('Error parsing matching items:', e);
        return;
      }

      // If no items found, return
      if (!items || !items.length) {
        console.error('No items found for matching question:', this.question.id);
        return;
      }

      // Create matching container
      var matchingContainer = document.createElement('div');
      matchingContainer.className = 'toeic-matching-container';

      // Get user answers
      var userAnswers = this.getUserAnswer() || {};

      // Create left column (English words)
      var leftColumn = document.createElement('div');
      leftColumn.className = 'toeic-matching-column toeic-matching-left';

      // Create right column (Vietnamese words)
      var rightColumn = document.createElement('div');
      rightColumn.className = 'toeic-matching-column toeic-matching-right';

      // Add column headers
      var leftHeader = document.createElement('div');
      leftHeader.className = 'toeic-matching-header';
      leftHeader.textContent = 'English';
      leftColumn.appendChild(leftHeader);
      var rightHeader = document.createElement('div');
      rightHeader.className = 'toeic-matching-header';
      rightHeader.textContent = 'Vietnamese';
      rightColumn.appendChild(rightHeader);

      // Track selected items for matching
      this.selectedLeft = null;
      this.selectedRight = null;

      // Track total number of pairs and paired items
      this.totalPairs = items.length;
      this.pairedCount = 0;

      // Add items to columns
      items.forEach(function (item, index) {
        // Left column item (English word)
        var leftItem = document.createElement('div');
        leftItem.className = 'toeic-matching-card';
        leftItem.dataset.itemId = item.id;
        var wordContainer = document.createElement('div');
        wordContainer.className = 'toeic-matching-word-container';

        // Add the word text
        var wordText = document.createElement('span');
        wordText.className = 'toeic-matching-word';
        wordText.textContent = item.prompt;
        wordContainer.appendChild(wordText);

        // Add audio button if available
        if (item.audio_link) {
          var audioButton = document.createElement('button');
          audioButton.className = 'toeic-matching-audio-btn';
          audioButton.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16"><path d="M11.536 14.01A8.473 8.473 0 0 0 14.026 8a8.473 8.473 0 0 0-2.49-6.01l-.708.707A7.476 7.476 0 0 1 13.025 8c0 2.071-.84 3.946-2.197 5.303l.708.707z"/><path d="M10.121 12.596A6.48 6.48 0 0 0 12.025 8a6.48 6.48 0 0 0-1.904-4.596l-.707.707A5.483 5.483 0 0 1 11.025 8a5.483 5.483 0 0 1-1.61 3.89l.706.706z"/><path d="M8.707 11.182A4.486 4.486 0 0 0 10.025 8a4.486 4.486 0 0 0-1.318-3.182L8 5.525A3.489 3.489 0 0 1 9.025 8 3.49 3.49 0 0 1 8 10.475l.707.707zM6.717 3.55A.5.5 0 0 1 7 4v8a.5.5 0 0 1-.812.39L3.825 10.5H1.5A.5.5 0 0 1 1 10V6a.5.5 0 0 1 .5-.5h2.325l2.363-1.89a.5.5 0 0 1 .529-.06z"/></svg>';
          audioButton.addEventListener('click', function (e) {
            e.stopPropagation(); // Prevent card selection when clicking audio button
            var audio = new Audio(item.audio_link);
            audio.play()["catch"](function (e) {
              console.error('Error playing audio:', e);
            });
          });
          wordContainer.appendChild(audioButton);
        }
        leftItem.appendChild(wordContainer);

        // Add click event for selection
        leftItem.addEventListener('click', function () {
          if (_this.isReviewMode) return;

          // Toggle selection
          _this.toggleLeftSelection(leftItem);

          // Check if we have a match
          _this.checkForMatch();
        });
        leftColumn.appendChild(leftItem);

        // Right column item (Vietnamese translation)
        var rightItem = document.createElement('div');
        rightItem.className = 'toeic-matching-card';
        rightItem.dataset.itemId = item.id;
        rightItem.textContent = item.response;

        // Add click event for selection
        rightItem.addEventListener('click', function () {
          if (_this.isReviewMode) return;
          console.log('Right item clicked');

          // Toggle selection
          _this.toggleRightSelection(rightItem);

          // Check if we have a match
          _this.checkForMatch();
        });
        rightColumn.appendChild(rightItem);
      });

      // Add columns to container
      matchingContainer.appendChild(leftColumn);
      matchingContainer.appendChild(rightColumn);

      // Add to main container
      this.container.appendChild(matchingContainer);

      // Create submit button (initially hidden)
      var submitButton = document.createElement('button');
      submitButton.className = 'toeic-submit-btn';
      submitButton.textContent = 'Submit Answers';
      submitButton.disabled = true;
      submitButton.addEventListener('click', function () {
        // Trigger submission of the question
        if (typeof _this.options.onSubmit === 'function') {
          _this.options.onSubmit(_this.question.id);
        }
      });
      this.container.appendChild(submitButton);
      this.submitButton = submitButton;

      // Add CSS for the matching cards
      this.addStyles();

      // In review mode, show the correct matches
      if (this.isReviewMode) {
        this.showCorrectMatches(items, userAnswers);
      } else {
        // If we have existing answers, show them
        this.showExistingAnswers(items, userAnswers);
      }
    }

    /**
     * Add custom styles for matching cards
     */
  }, {
    key: "addStyles",
    value: function addStyles() {
      // Check if styles are already added
      if (document.getElementById('toeic-matching-styles')) return;
      var styleEl = document.createElement('style');
      styleEl.id = 'toeic-matching-styles';
      styleEl.textContent = "\n            .toeic-matching-container {\n                display: flex;\n                gap: 20px;\n                margin-bottom: 20px;\n            }\n            \n            .toeic-matching-column {\n                flex: 1;\n                display: flex;\n                flex-direction: column;\n                gap: 10px;\n            }\n            \n            .toeic-matching-header {\n                font-weight: bold;\n                font-size: 16px;\n                padding: 10px;\n                background-color: #f5f5f5;\n                border-radius: 5px;\n                text-align: center;\n            }\n            \n            .toeic-matching-card {\n                padding: 15px;\n                border-radius: 5px;\n                background-color: #fff;\n                box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n                cursor: pointer;\n                transition: all 0.3s ease;\n                position: relative;\n                display: flex;\n                align-items: center;\n                height: 60px;\n                font-size: 24px;\n            }\n            \n            .toeic-matching-word-container {\n                display: flex;\n                align-items: center;\n                justify-content: space-between;\n                width: 100%;\n            }\n            \n            .toeic-matching-card:hover:not(.paired):not(.disabled) {\n                background: linear-gradient(135deg, #ff9966, #ff5e62);\n                color: white;\n                transform: translateY(-2px);\n            }\n            \n            .toeic-matching-card.selected {\n                background: linear-gradient(135deg, #ff9966, #ff5e62);\n                color: white;\n                transform: translateY(-2px);\n                box-shadow: 0 4px 8px rgba(0,0,0,0.2);\n            }\n            \n            .toeic-matching-card.fade-out {\n                animation: fadeOut 0.8s forwards;\n                pointer-events: none;\n            }\n            \n            @keyframes fadeOut {\n                0% { opacity: 1; transform: scale(1); }\n                50% { opacity: 0.8; transform: scale(1.05); }\n                100% { opacity: 0.4; transform: scale(0.95); }\n            }\n            \n            .toeic-matching-card.paired {\n                opacity: 0.4;\n                transform: scale(0.95);\n                background-color: #f0f0f0;\n                color: #666;\n                border: 1px dashed #ccc;\n                pointer-events: none;\n            }\n            \n            .toeic-matching-card.disabled {\n                cursor: default;\n                pointer-events: none;\n            }\n            \n            .toeic-matching-audio-btn {\n                background: transparent;\n                border: none;\n                color: inherit;\n                cursor: pointer;\n                padding: 5px;\n                margin-left: 10px;\n                width: 30px;\n                height: 30px;\n                display: flex;\n                align-items: center;\n                justify-content: center;\n                border-radius: 50%;\n                transition: all 0.2s ease;\n            }\n            \n            .toeic-matching-audio-btn:hover {\n                background-color: rgba(255, 255, 255, 0.3);\n                transform: scale(1.1);\n            }\n            \n            .toeic-matching-word {\n                flex: 1;\n            }\n            \n            .toeic-submit-btn {\n                margin-top: 20px;\n                padding: 10px 20px;\n                background: linear-gradient(135deg, #ff9966, #ff5e62);\n                color: white;\n                border: none;\n                border-radius: 5px;\n                cursor: pointer;\n                font-weight: bold;\n                display: none;\n            }\n            \n            .toeic-submit-btn.active {\n                display: block;\n            }\n            \n            .toeic-submit-btn:disabled {\n                background: #cccccc;\n                cursor: not-allowed;\n            }\n        ";
      document.head.appendChild(styleEl);
    }

    /**
     * Toggle selection of a left column item
     * 
     * @param {HTMLElement} item - The item to toggle
     */
  }, {
    key: "toggleLeftSelection",
    value: function toggleLeftSelection(item) {
      // If item is already paired or disabled, do nothing
      if (item.classList.contains('paired') || item.classList.contains('disabled')) {
        return;
      }

      // If this item is already selected, deselect it
      if (this.selectedLeft === item) {
        item.classList.remove('selected');
        this.selectedLeft = null;
        return;
      }

      // Deselect any previously selected item
      if (this.selectedLeft) {
        this.selectedLeft.classList.remove('selected');
      }

      // Select this item
      item.classList.add('selected');
      this.selectedLeft = item;
    }

    /**
     * Toggle selection of a right column item
     * 
     * @param {HTMLElement} item - The item to toggle
     */
  }, {
    key: "toggleRightSelection",
    value: function toggleRightSelection(item) {
      // If item is already paired or disabled, do nothing
      if (item.classList.contains('paired') || item.classList.contains('disabled')) {
        return;
      }

      // If this item is already selected, deselect it
      if (this.selectedRight === item) {
        item.classList.remove('selected');
        this.selectedRight = null;
        return;
      }

      // Deselect any previously selected item
      if (this.selectedRight) {
        this.selectedRight.classList.remove('selected');
      }

      // Select this item
      item.classList.add('selected');
      this.selectedRight = item;
    }

    /**
     * Check if we have a match between selected items
     */
  }, {
    key: "checkForMatch",
    value: function checkForMatch() {
      console.log('Checking for match...');
      if (!this.selectedLeft || !this.selectedRight) return;
      var leftId = this.selectedLeft.dataset.itemId;
      var rightId = this.selectedRight.dataset.itemId;

      // Update user answer
      var answer = this.getUserAnswer() || {};
      answer[leftId] = rightId;
      this.setUserAnswer(answer);

      // Apply fade out animation to both cards
      this.selectedLeft.classList.add('fade-out');
      this.selectedRight.classList.add('fade-out');

      // Mark cards as paired (regardless of correct or not)
      this.selectedLeft.classList.add('paired');
      this.selectedRight.classList.add('paired');
      this.selectedLeft.classList.remove('selected');
      this.selectedRight.classList.remove('selected');

      // Disable paired cards
      this.selectedLeft.classList.add('disabled');
      this.selectedRight.classList.add('disabled');

      // Reset selection
      this.selectedLeft = null;
      this.selectedRight = null;

      // Check if all pairs have been matched
      this.checkAllPaired();
    }

    /**
     * Set user answer
     * 
     * @param {Object} answer - The user's answer
     */
  }, {
    key: "setUserAnswer",
    value: function setUserAnswer(answer) {
      this.userAnswer = answer;
    }

    /**
     * Show correct matches in review mode
     * 
     * @param {Array} items - The items array
     * @param {Object} userAnswers - User's answers
     */
  }, {
    key: "showCorrectMatches",
    value: function showCorrectMatches(items, userAnswers) {
      var leftCards = this.container.querySelectorAll('.toeic-matching-left .toeic-matching-card');
      var rightCards = this.container.querySelectorAll('.toeic-matching-right .toeic-matching-card');
      items.forEach(function (item) {
        var leftCard = Array.from(leftCards).find(function (card) {
          return card.dataset.itemId === item.id;
        });
        var rightCard = Array.from(rightCards).find(function (card) {
          return card.dataset.itemId === item.id;
        });
        if (leftCard && rightCard) {
          var userAnswer = userAnswers[item.id];
          if (userAnswer === item.id) {
            // Correct match
            leftCard.classList.add('matched');
            rightCard.classList.add('matched');
          } else if (userAnswer) {
            // Incorrect match
            leftCard.classList.add('incorrect');

            // Find the incorrectly matched right card
            var wrongRightCard = Array.from(rightCards).find(function (card) {
              return card.dataset.itemId === userAnswer;
            });
            if (wrongRightCard) {
              wrongRightCard.classList.add('incorrect');
            }
          }
        }
      });
    }

    /**
     * Show existing answers
     * 
     * @param {Array} items - The items array
     * @param {Object} userAnswers - User's answers
     */
  }, {
    key: "showExistingAnswers",
    value: function showExistingAnswers(items, userAnswers) {
      var leftCards = this.container.querySelectorAll('.toeic-matching-left .toeic-matching-card');
      var rightCards = this.container.querySelectorAll('.toeic-matching-right .toeic-matching-card');
      var pairedCount = 0;
      items.forEach(function (item) {
        var leftCard = Array.from(leftCards).find(function (card) {
          return card.dataset.itemId === item.id;
        });
        var userAnswer = userAnswers[item.id];
        if (leftCard && userAnswer) {
          var rightCard = Array.from(rightCards).find(function (card) {
            return card.dataset.itemId === userAnswer;
          });
          if (rightCard) {
            // Mark as paired
            leftCard.classList.add('paired');
            rightCard.classList.add('paired');
            leftCard.classList.add('disabled');
            rightCard.classList.add('disabled');
            pairedCount++;
          }
        }
      });

      // Update paired count
      this.pairedCount = pairedCount;

      // Check if all pairs are matched
      this.checkAllPaired();
    }

    /**
     * Check if all pairs have been matched
     */
  }, {
    key: "checkAllPaired",
    value: function checkAllPaired() {
      if (this.pairedCount >= this.totalPairs) {
        // All pairs have been matched, show submit button
        if (this.submitButton) {
          this.submitButton.classList.add('active');
          this.submitButton.disabled = false;
        }
      } else {
        // Not all pairs matched yet
        if (this.submitButton) {
          this.submitButton.disabled = true;
        }
      }

      // Update paired count
      this.pairedCount = this.container.querySelectorAll('.toeic-matching-card.paired').length / 2;
    }

    /**
     * Shuffle the response items to prevent ordering hints
     * 
     * @param {Array} items - The original items array
     * @returns {Array} Shuffled array of response items
     */
  }, {
    key: "shuffleResponses",
    value: function shuffleResponses(items) {
      // Extract just the response data needed
      var responses = items.map(function (item) {
        return {
          id: item.id,
          response: item.response
        };
      });

      // Shuffle using Fisher-Yates algorithm
      // Only shuffle in non-review mode to keep consistent ordering in review
      if (!this.isReviewMode) {
        for (var i = responses.length - 1; i > 0; i--) {
          var j = Math.floor(Math.random() * (i + 1));
          var _ref = [responses[j], responses[i]];
          responses[i] = _ref[0];
          responses[j] = _ref[1];
        }
      }
      return responses;
    }
  }]);
}(_QuestionRenderer__WEBPACK_IMPORTED_MODULE_0__["default"]);
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MatchingRenderer);

/***/ }),

/***/ "./assets/js/frontend/src/components/questions/MultipleChoiceRenderer.js":
/*!*******************************************************************************!*\
  !*** ./assets/js/frontend/src/components/questions/MultipleChoiceRenderer.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _QuestionRenderer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./QuestionRenderer */ "./assets/js/frontend/src/components/questions/QuestionRenderer.js");
function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError("Cannot call a class as a function"); }
function _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, "value" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }
function _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, "prototype", { writable: !1 }), e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
function _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }
function _possibleConstructorReturn(t, e) { if (e && ("object" == _typeof(e) || "function" == typeof e)) return e; if (void 0 !== e) throw new TypeError("Derived constructors may only return object or undefined"); return _assertThisInitialized(t); }
function _assertThisInitialized(e) { if (void 0 === e) throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); return e; }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
function _getPrototypeOf(t) { return _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function (t) { return t.__proto__ || Object.getPrototypeOf(t); }, _getPrototypeOf(t); }
function _inherits(t, e) { if ("function" != typeof e && null !== e) throw new TypeError("Super expression must either be null or a function"); t.prototype = Object.create(e && e.prototype, { constructor: { value: t, writable: !0, configurable: !0 } }), Object.defineProperty(t, "prototype", { writable: !1 }), e && _setPrototypeOf(t, e); }
function _setPrototypeOf(t, e) { return _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function (t, e) { return t.__proto__ = e, t; }, _setPrototypeOf(t, e); }
/**
 * Multiple Choice Question Renderer
 * 
 * Handles rendering of multiple choice questions with radio button options.
 */


var MultipleChoiceRenderer = /*#__PURE__*/function (_QuestionRenderer) {
  function MultipleChoiceRenderer() {
    _classCallCheck(this, MultipleChoiceRenderer);
    return _callSuper(this, MultipleChoiceRenderer, arguments);
  }
  _inherits(MultipleChoiceRenderer, _QuestionRenderer);
  return _createClass(MultipleChoiceRenderer, [{
    key: "render",
    value:
    /**
     * Render the multiple choice question
     */
    function render() {
      var _this = this;
      this.clearContainer();

      // Parse options if needed
      var options = [];
      try {
        options = typeof this.question.options === 'string' ? JSON.parse(this.question.options) : this.question.options;
      } catch (e) {
        console.error('Error parsing question options:', e);
        return;
      }

      // If no options found, return
      if (!options || !options.length) {
        console.error('No options found for multiple choice question:', this.question.id);
        return;
      }

      // Create option elements
      options.forEach(function (option, index) {
        var optionElement = _this.createOptionElement(option, index);
        _this.container.appendChild(optionElement);
      });
    }

    /**
     * Create an option element for a multiple choice question
     * 
     * @param {Object} option - The option object with value and text
     * @param {number} index - The index of the option
     * @returns {HTMLElement} The created option element
     */
  }, {
    key: "createOptionElement",
    value: function createOptionElement(option, index) {
      var _this2 = this;
      var optionElement = document.createElement('div');
      optionElement.className = 'toeic-question-option';
      var inputId = "question-".concat(this.question.id, "-option-").concat(index);
      var isChecked = this.getUserAnswer() === option.value;
      optionElement.innerHTML = "\n            <input type=\"radio\" \n                   id=\"".concat(inputId, "\" \n                   name=\"question-").concat(this.question.id, "\" \n                   value=\"").concat(option.value, "\" \n                   ").concat(isChecked ? 'checked' : '', "\n                   ").concat(this.isReviewMode ? 'disabled' : '', "\n            >\n            <label for=\"").concat(inputId, "\">").concat(option.text, "</label>\n        ");

      // In review mode, highlight correct and incorrect answers
      if (this.isReviewMode && this.question.correct_answer) {
        if (option.value === this.question.correct_answer) {
          optionElement.classList.add('correct');
        } else if (isChecked) {
          optionElement.classList.add('incorrect');
        }
      }

      // Add event listener for option selection
      if (!this.isReviewMode) {
        var input = optionElement.querySelector('input');
        if (input && typeof this.options.onAnswerSelected === 'function') {
          input.addEventListener('change', function () {
            _this2.options.onAnswerSelected(_this2.question.id, option.value);
          });
        }
      }
      return optionElement;
    }
  }]);
}(_QuestionRenderer__WEBPACK_IMPORTED_MODULE_0__["default"]);
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MultipleChoiceRenderer);

/***/ }),

/***/ "./assets/js/frontend/src/components/questions/QuestionRenderer.js":
/*!*************************************************************************!*\
  !*** ./assets/js/frontend/src/components/questions/QuestionRenderer.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError("Cannot call a class as a function"); }
function _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, "value" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }
function _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, "prototype", { writable: !1 }), e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
/**
 * Base Question Renderer
 * 
 * Abstract base class for all question type renderers.
 * Each specific question type should extend this class.
 */
var QuestionRenderer = /*#__PURE__*/function () {
  /**
   * Constructor
   * 
   * @param {Object} question - The question object to render
   * @param {HTMLElement} container - The container element to render into
   * @param {Object} options - Additional options for rendering
   */
  function QuestionRenderer(question, container) {
    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
    _classCallCheck(this, QuestionRenderer);
    if ((this instanceof QuestionRenderer ? this.constructor : void 0) === QuestionRenderer) {
      throw new Error('QuestionRenderer is an abstract class and cannot be instantiated directly');
    }
    this.question = question;
    this.container = container;
    this.options = options;
    this.isReviewMode = options.isReviewMode || false;
    this.userAnswers = options.userAnswers || {};
  }

  /**
   * Render the question
   * This method must be implemented by subclasses
   */
  return _createClass(QuestionRenderer, [{
    key: "render",
    value: function render() {
      throw new Error('render() method must be implemented by subclass');
    }

    /**
     * Get the user's answer for this question
     * 
     * @returns {*} The user's answer or null if not answered
     */
  }, {
    key: "getUserAnswer",
    value: function getUserAnswer() {
      return this.userAnswers[this.question.id] || null;
    }

    /**
     * Check if the question has been answered
     * 
     * @returns {boolean} True if the question has been answered
     */
  }, {
    key: "isAnswered",
    value: function isAnswered() {
      return this.question.id in this.userAnswers;
    }

    /**
     * Clear the container before rendering
     */
  }, {
    key: "clearContainer",
    value: function clearContainer() {
      if (this.container) {
        this.container.innerHTML = '';
      }
    }

    /**
     * Create a basic wrapper element for the question
     * 
     * @returns {HTMLElement} The wrapper element
     */
  }, {
    key: "createWrapper",
    value: function createWrapper() {
      var wrapper = document.createElement('div');
      wrapper.className = 'toeic-question-wrapper';
      wrapper.dataset.questionId = this.question.id;
      wrapper.dataset.questionType = this.question.type;
      return wrapper;
    }
  }]);
}();
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (QuestionRenderer);

/***/ }),

/***/ "./assets/js/frontend/src/components/questions/QuestionRendererFactory.js":
/*!********************************************************************************!*\
  !*** ./assets/js/frontend/src/components/questions/QuestionRendererFactory.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _MultipleChoiceRenderer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./MultipleChoiceRenderer */ "./assets/js/frontend/src/components/questions/MultipleChoiceRenderer.js");
/* harmony import */ var _TextInputRenderer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./TextInputRenderer */ "./assets/js/frontend/src/components/questions/TextInputRenderer.js");
/* harmony import */ var _MatchingRenderer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./MatchingRenderer */ "./assets/js/frontend/src/components/questions/MatchingRenderer.js");
function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError("Cannot call a class as a function"); }
function _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, "value" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }
function _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, "prototype", { writable: !1 }), e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
/**
 * Question Renderer Factory
 * 
 * Factory class that creates the appropriate renderer for a given question type.
 */




var QuestionRendererFactory = /*#__PURE__*/function () {
  function QuestionRendererFactory() {
    _classCallCheck(this, QuestionRendererFactory);
  }
  return _createClass(QuestionRendererFactory, null, [{
    key: "createRenderer",
    value:
    /**
     * Create a renderer instance for the given question
     * 
     * @param {Object} question - The question object
     * @param {HTMLElement} container - The container to render into
     * @param {Object} options - Additional options for rendering
     * @returns {QuestionRenderer} An instance of the appropriate renderer
     * @throws {Error} If no renderer is available for the question type
     */
    function createRenderer(question, container) {
      var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
      if (!question || !question.question_type) {
        throw new Error('Question object is invalid or missing type');
      }

      // Select the appropriate renderer based on question type
      switch (question.question_type.toLowerCase()) {
        case 'multiple_choice':
          return new _MultipleChoiceRenderer__WEBPACK_IMPORTED_MODULE_0__["default"](question, container, options);
        case 'text_input':
        case 'short_answer':
        case 'fill_in_blank':
          return new _TextInputRenderer__WEBPACK_IMPORTED_MODULE_1__["default"](question, container, options);
        case 'matching':
          return new _MatchingRenderer__WEBPACK_IMPORTED_MODULE_2__["default"](question, container, options);
        default:
          throw new Error("No renderer available for question type: ".concat(question.question_type));
      }
    }

    /**
     * Check if a renderer is available for the given question type
     * 
     * @param {string} questionType - The question type to check
     * @returns {boolean} True if a renderer is available
     */
  }, {
    key: "hasRendererForType",
    value: function hasRendererForType(questionType) {
      if (!questionType) return false;
      var supportedTypes = ['multiple_choice', 'text_input', 'short_answer', 'fill_in_blank', 'matching'];
      return supportedTypes.includes(questionType.toLowerCase());
    }
  }]);
}();
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (QuestionRendererFactory);

/***/ }),

/***/ "./assets/js/frontend/src/components/questions/TextInputRenderer.js":
/*!**************************************************************************!*\
  !*** ./assets/js/frontend/src/components/questions/TextInputRenderer.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _QuestionRenderer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./QuestionRenderer */ "./assets/js/frontend/src/components/questions/QuestionRenderer.js");
function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError("Cannot call a class as a function"); }
function _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, "value" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }
function _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, "prototype", { writable: !1 }), e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
function _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }
function _possibleConstructorReturn(t, e) { if (e && ("object" == _typeof(e) || "function" == typeof e)) return e; if (void 0 !== e) throw new TypeError("Derived constructors may only return object or undefined"); return _assertThisInitialized(t); }
function _assertThisInitialized(e) { if (void 0 === e) throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); return e; }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
function _getPrototypeOf(t) { return _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function (t) { return t.__proto__ || Object.getPrototypeOf(t); }, _getPrototypeOf(t); }
function _inherits(t, e) { if ("function" != typeof e && null !== e) throw new TypeError("Super expression must either be null or a function"); t.prototype = Object.create(e && e.prototype, { constructor: { value: t, writable: !0, configurable: !0 } }), Object.defineProperty(t, "prototype", { writable: !1 }), e && _setPrototypeOf(t, e); }
function _setPrototypeOf(t, e) { return _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function (t, e) { return t.__proto__ = e, t; }, _setPrototypeOf(t, e); }
/**
 * Text Input Question Renderer
 * 
 * Handles rendering of text input questions where users type their answers.
 */


var TextInputRenderer = /*#__PURE__*/function (_QuestionRenderer) {
  function TextInputRenderer() {
    _classCallCheck(this, TextInputRenderer);
    return _callSuper(this, TextInputRenderer, arguments);
  }
  _inherits(TextInputRenderer, _QuestionRenderer);
  return _createClass(TextInputRenderer, [{
    key: "render",
    value:
    /**
     * Render the text input question
     */
    function render() {
      var _this = this;
      this.clearContainer();

      // Create wrapper
      var wrapper = this.createWrapper();

      // Create input element
      var inputContainer = document.createElement('div');
      inputContainer.className = 'toeic-question-input-container';
      var inputId = "question-".concat(this.question.id, "-input");
      var userAnswer = this.getUserAnswer() || '';
      inputContainer.innerHTML = "\n            <input type=\"text\" \n                   id=\"".concat(inputId, "\" \n                   class=\"toeic-question-text-input\"\n                   value=\"").concat(userAnswer, "\"\n                   placeholder=\"").concat(this.question.placeholder || 'Type your answer here...', "\"\n                   ").concat(this.isReviewMode ? 'disabled' : '', "\n            >\n        ");

      // In review mode, show correct answer and highlight
      if (this.isReviewMode && this.question.correct_answer) {
        var reviewInfo = document.createElement('div');
        reviewInfo.className = 'toeic-question-review-info';
        var isCorrect = userAnswer.toLowerCase() === this.question.correct_answer.toLowerCase();
        reviewInfo.classList.add(isCorrect ? 'correct' : 'incorrect');
        reviewInfo.innerHTML = "\n                <div class=\"toeic-question-correct-answer\">\n                    <strong>Correct answer:</strong> ".concat(this.question.correct_answer, "\n                </div>\n            ");
        inputContainer.appendChild(reviewInfo);
      }
      wrapper.appendChild(inputContainer);
      this.container.appendChild(wrapper);

      // Add event listener for input changes
      if (!this.isReviewMode) {
        var input = wrapper.querySelector('input');
        if (input && typeof this.options.onAnswerSelected === 'function') {
          input.addEventListener('change', function (e) {
            _this.options.onAnswerSelected(_this.question.id, e.target.value);
          });

          // Also listen for input event to capture typing in real-time
          input.addEventListener('input', function (e) {
            _this.options.onAnswerSelected(_this.question.id, e.target.value);
          });
        }
      }
    }
  }]);
}(_QuestionRenderer__WEBPACK_IMPORTED_MODULE_0__["default"]);
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TextInputRenderer);

/***/ }),

/***/ "./assets/js/frontend/src/components/questions/index.js":
/*!**************************************************************!*\
  !*** ./assets/js/frontend/src/components/questions/index.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   MatchingRenderer: () => (/* reexport safe */ _MatchingRenderer__WEBPACK_IMPORTED_MODULE_3__["default"]),
/* harmony export */   MultipleChoiceRenderer: () => (/* reexport safe */ _MultipleChoiceRenderer__WEBPACK_IMPORTED_MODULE_1__["default"]),
/* harmony export */   QuestionRenderer: () => (/* reexport safe */ _QuestionRenderer__WEBPACK_IMPORTED_MODULE_0__["default"]),
/* harmony export */   QuestionRendererFactory: () => (/* reexport safe */ _QuestionRendererFactory__WEBPACK_IMPORTED_MODULE_4__["default"]),
/* harmony export */   TextInputRenderer: () => (/* reexport safe */ _TextInputRenderer__WEBPACK_IMPORTED_MODULE_2__["default"])
/* harmony export */ });
/* harmony import */ var _QuestionRenderer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./QuestionRenderer */ "./assets/js/frontend/src/components/questions/QuestionRenderer.js");
/* harmony import */ var _MultipleChoiceRenderer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./MultipleChoiceRenderer */ "./assets/js/frontend/src/components/questions/MultipleChoiceRenderer.js");
/* harmony import */ var _TextInputRenderer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./TextInputRenderer */ "./assets/js/frontend/src/components/questions/TextInputRenderer.js");
/* harmony import */ var _MatchingRenderer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./MatchingRenderer */ "./assets/js/frontend/src/components/questions/MatchingRenderer.js");
/* harmony import */ var _QuestionRendererFactory__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./QuestionRendererFactory */ "./assets/js/frontend/src/components/questions/QuestionRendererFactory.js");
/**
 * Question Renderers Index
 * 
 * Exports all question renderers and the factory.
 */








/***/ }),

/***/ "./assets/js/frontend/src/pages/pronunciation-page.js":
/*!************************************************************!*\
  !*** ./assets/js/frontend/src/pages/pronunciation-page.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   PronunciationRecorder: () => (/* binding */ PronunciationRecorder)
/* harmony export */ });
/* harmony import */ var _services_PronunciationService__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../services/PronunciationService */ "./assets/js/frontend/src/services/PronunciationService.js");
function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function _regenerator() { /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */ var e, t, r = "function" == typeof Symbol ? Symbol : {}, n = r.iterator || "@@iterator", o = r.toStringTag || "@@toStringTag"; function i(r, n, o, i) { var c = n && n.prototype instanceof Generator ? n : Generator, u = Object.create(c.prototype); return _regeneratorDefine2(u, "_invoke", function (r, n, o) { var i, c, u, f = 0, p = o || [], y = !1, G = { p: 0, n: 0, v: e, a: d, f: d.bind(e, 4), d: function d(t, r) { return i = t, c = 0, u = e, G.n = r, a; } }; function d(r, n) { for (c = r, u = n, t = 0; !y && f && !o && t < p.length; t++) { var o, i = p[t], d = G.p, l = i[2]; r > 3 ? (o = l === n) && (u = i[(c = i[4]) ? 5 : (c = 3, 3)], i[4] = i[5] = e) : i[0] <= d && ((o = r < 2 && d < i[1]) ? (c = 0, G.v = n, G.n = i[1]) : d < l && (o = r < 3 || i[0] > n || n > l) && (i[4] = r, i[5] = n, G.n = l, c = 0)); } if (o || r > 1) return a; throw y = !0, n; } return function (o, p, l) { if (f > 1) throw TypeError("Generator is already running"); for (y && 1 === p && d(p, l), c = p, u = l; (t = c < 2 ? e : u) || !y;) { i || (c ? c < 3 ? (c > 1 && (G.n = -1), d(c, u)) : G.n = u : G.v = u); try { if (f = 2, i) { if (c || (o = "next"), t = i[o]) { if (!(t = t.call(i, u))) throw TypeError("iterator result is not an object"); if (!t.done) return t; u = t.value, c < 2 && (c = 0); } else 1 === c && (t = i["return"]) && t.call(i), c < 2 && (u = TypeError("The iterator does not provide a '" + o + "' method"), c = 1); i = e; } else if ((t = (y = G.n < 0) ? u : r.call(n, G)) !== a) break; } catch (t) { i = e, c = 1, u = t; } finally { f = 1; } } return { value: t, done: y }; }; }(r, o, i), !0), u; } var a = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} t = Object.getPrototypeOf; var c = [][n] ? t(t([][n]())) : (_regeneratorDefine2(t = {}, n, function () { return this; }), t), u = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(c); function f(e) { return Object.setPrototypeOf ? Object.setPrototypeOf(e, GeneratorFunctionPrototype) : (e.__proto__ = GeneratorFunctionPrototype, _regeneratorDefine2(e, o, "GeneratorFunction")), e.prototype = Object.create(u), e; } return GeneratorFunction.prototype = GeneratorFunctionPrototype, _regeneratorDefine2(u, "constructor", GeneratorFunctionPrototype), _regeneratorDefine2(GeneratorFunctionPrototype, "constructor", GeneratorFunction), GeneratorFunction.displayName = "GeneratorFunction", _regeneratorDefine2(GeneratorFunctionPrototype, o, "GeneratorFunction"), _regeneratorDefine2(u), _regeneratorDefine2(u, o, "Generator"), _regeneratorDefine2(u, n, function () { return this; }), _regeneratorDefine2(u, "toString", function () { return "[object Generator]"; }), (_regenerator = function _regenerator() { return { w: i, m: f }; })(); }
function _regeneratorDefine2(e, r, n, t) { var i = Object.defineProperty; try { i({}, "", {}); } catch (e) { i = 0; } _regeneratorDefine2 = function _regeneratorDefine(e, r, n, t) { if (r) i ? i(e, r, { value: n, enumerable: !t, configurable: !t, writable: !t }) : e[r] = n;else { var o = function o(r, n) { _regeneratorDefine2(e, r, function (e) { return this._invoke(r, n, e); }); }; o("next", 0), o("throw", 1), o("return", 2); } }, _regeneratorDefine2(e, r, n, t); }
function asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }
function _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, "next", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, "throw", n); } _next(void 0); }); }; }
function _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError("Cannot call a class as a function"); }
function _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, "value" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }
function _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, "prototype", { writable: !1 }), e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
// pronunciation-page.js


var PronunciationRecorder = /*#__PURE__*/function () {
  function PronunciationRecorder(_ref) {
    var paragraphText = _ref.paragraphText;
    _classCallCheck(this, PronunciationRecorder);
    this.isRecording = false;
    this.mediaRecorder = null;
    this.audioChunks = [];
    this.startTime = null;
    this.timerInterval = null;
    this.paragraphText = paragraphText;

    // Initialize pronunciation service
    this.pronunciationService = new _services_PronunciationService__WEBPACK_IMPORTED_MODULE_0__["default"]();

    // DOM elements
    this.recordBtn = document.getElementById('record-btn');
    this.recordingStatus = document.querySelector('.recording-status');
    this.recordingDuration = document.querySelector('.recording-duration');
    this.durationText = document.querySelector('.recording-duration-text');
    this.suggestionBtns = document.querySelectorAll('.suggestion-btn');
    this.init();
  }
  return _createClass(PronunciationRecorder, [{
    key: "init",
    value: function init() {
      var _this = this;
      // Check if required elements exist
      if (!this.recordBtn) {
        console.warn('Record button not found');
        return;
      }
      console.log('Record button found');

      // Initially hide recording status elements
      this.hideRecordingElements();

      // Bind event listeners
      this.recordBtn.addEventListener('click', function () {
        return _this.toggleRecording();
      });
      this.suggestionBtns.forEach(function (button) {
        return button.addEventListener('click', function (e) {
          return _this.handleSuggestion(e);
        });
      });

      // Check for microphone permissions
      this.checkMicrophonePermissions();
    }
  }, {
    key: "checkMicrophonePermissions",
    value: function () {
      var _checkMicrophonePermissions = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee() {
        var stream, _t;
        return _regenerator().w(function (_context) {
          while (1) switch (_context.p = _context.n) {
            case 0:
              _context.p = 0;
              _context.n = 1;
              return navigator.mediaDevices.getUserMedia({
                audio: true
              });
            case 1:
              stream = _context.v;
              stream.getTracks().forEach(function (track) {
                return track.stop();
              });
              console.log('Microphone access granted');
              _context.n = 3;
              break;
            case 2:
              _context.p = 2;
              _t = _context.v;
              console.error('Microphone access denied:', _t);
              this.showError('Microphone access is required for recording. Please allow microphone permissions.');
            case 3:
              return _context.a(2);
          }
        }, _callee, this, [[0, 2]]);
      }));
      function checkMicrophonePermissions() {
        return _checkMicrophonePermissions.apply(this, arguments);
      }
      return checkMicrophonePermissions;
    }()
  }, {
    key: "toggleRecording",
    value: function () {
      var _toggleRecording = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee2() {
        return _regenerator().w(function (_context2) {
          while (1) switch (_context2.n) {
            case 0:
              if (!this.isRecording) {
                _context2.n = 1;
                break;
              }
              this.stopRecording();
              _context2.n = 2;
              break;
            case 1:
              _context2.n = 2;
              return this.startRecording();
            case 2:
              return _context2.a(2);
          }
        }, _callee2, this);
      }));
      function toggleRecording() {
        return _toggleRecording.apply(this, arguments);
      }
      return toggleRecording;
    }()
  }, {
    key: "startRecording",
    value: function () {
      var _startRecording = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee3() {
        var _this2 = this;
        var stream, mimeType, _t2;
        return _regenerator().w(function (_context3) {
          while (1) switch (_context3.p = _context3.n) {
            case 0:
              _context3.p = 0;
              _context3.n = 1;
              return navigator.mediaDevices.getUserMedia({
                audio: {
                  echoCancellation: true,
                  noiseSuppression: true,
                  sampleRate: 16000,
                  // Reduced from 44100 to 16000 for speech
                  channelCount: 1 // Mono channel for speech
                }
              });
            case 1:
              stream = _context3.v;
              // Create MediaRecorder with fallback MIME types
              mimeType = 'audio/webm;codecs=opus';
              if (!MediaRecorder.isTypeSupported(mimeType)) {
                mimeType = 'audio/webm';
                if (!MediaRecorder.isTypeSupported(mimeType)) {
                  mimeType = 'audio/mp4';
                  if (!MediaRecorder.isTypeSupported(mimeType)) {
                    mimeType = ''; // Let browser choose
                  }
                }
              }
              this.mediaRecorder = new MediaRecorder(stream, {
                mimeType: mimeType
              });

              // Store the actual MIME type used
              this.actualMimeType = mimeType;

              // Reset audio chunks
              this.audioChunks = [];

              // Set up event handlers
              this.mediaRecorder.ondataavailable = function (event) {
                if (event.data.size > 0) {
                  _this2.audioChunks.push(event.data);
                }
              };
              this.mediaRecorder.onstop = function () {
                _this2.processRecording();
              };

              // Start recording
              this.mediaRecorder.start();
              this.isRecording = true;
              this.startTime = Date.now();

              // Update UI
              this.showRecordingElements();
              this.updateButtonState();
              this.startTimer();
              _context3.n = 3;
              break;
            case 2:
              _context3.p = 2;
              _t2 = _context3.v;
              console.error('Error starting recording:', _t2);
              this.showError('Failed to start recording. Please check your microphone permissions.');
            case 3:
              return _context3.a(2);
          }
        }, _callee3, this, [[0, 2]]);
      }));
      function startRecording() {
        return _startRecording.apply(this, arguments);
      }
      return startRecording;
    }()
  }, {
    key: "stopRecording",
    value: function stopRecording() {
      if (this.mediaRecorder && this.isRecording) {
        this.mediaRecorder.stop();
        this.isRecording = false;

        // Stop all tracks
        if (this.mediaRecorder.stream) {
          this.mediaRecorder.stream.getTracks().forEach(function (track) {
            return track.stop();
          });
        }

        // Update UI
        this.hideRecordingElements();
        this.updateButtonState();
        this.stopTimer();
        console.log('Recording stopped');
      }
    }
  }, {
    key: "processRecording",
    value: function processRecording() {
      var _this3 = this;
      if (this.audioChunks.length === 0) {
        this.showError('No audio data recorded');
        return;
      }

      // Create blob with the correct MIME type that was actually recorded
      var audioBlob = new Blob(this.audioChunks, {
        type: this.actualMimeType || 'audio/webm'
      });

      // Convert to WAV if needed for better compatibility
      this.convertToWAV(audioBlob).then(function (wavBlob) {
        _this3.submitForGrading(wavBlob);
      })["catch"](function (error) {
        console.warn('WAV conversion failed, using original format:', error);
        // Fallback to original blob if conversion fails
        _this3.submitForGrading(audioBlob);
      });
    }
  }, {
    key: "convertToWAV",
    value: function () {
      var _convertToWAV = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee5(audioBlob) {
        var _this4 = this;
        return _regenerator().w(function (_context5) {
          while (1) switch (_context5.n) {
            case 0:
              return _context5.a(2, new Promise(function (resolve, reject) {
                var audioContext = new (window.AudioContext || window.webkitAudioContext)();
                var fileReader = new FileReader();
                fileReader.onload = /*#__PURE__*/function () {
                  var _ref2 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee4(e) {
                    var arrayBuffer, audioBuffer, trimmedBuffer, wavBlob, _t3;
                    return _regenerator().w(function (_context4) {
                      while (1) switch (_context4.p = _context4.n) {
                        case 0:
                          _context4.p = 0;
                          // Decode the audio data
                          arrayBuffer = e.target.result;
                          _context4.n = 1;
                          return audioContext.decodeAudioData(arrayBuffer);
                        case 1:
                          audioBuffer = _context4.v;
                          // Trim silence from the audio buffer
                          trimmedBuffer = _this4.trimSilence(audioBuffer, audioContext); // Convert to WAV
                          wavBlob = _this4.audioBufferToWav(trimmedBuffer);
                          resolve(wavBlob);
                          _context4.n = 3;
                          break;
                        case 2:
                          _context4.p = 2;
                          _t3 = _context4.v;
                          reject(_t3);
                        case 3:
                          return _context4.a(2);
                      }
                    }, _callee4, null, [[0, 2]]);
                  }));
                  return function (_x2) {
                    return _ref2.apply(this, arguments);
                  };
                }();
                fileReader.onerror = function () {
                  return reject(new Error('Failed to read audio file'));
                };
                fileReader.readAsArrayBuffer(audioBlob);
              }));
          }
        }, _callee5);
      }));
      function convertToWAV(_x) {
        return _convertToWAV.apply(this, arguments);
      }
      return convertToWAV;
    }()
    /**
     * Trim silence from the beginning and end of an audio buffer
     * @param {AudioBuffer} audioBuffer - The original audio buffer
     * @param {AudioContext} audioContext - The audio context to use for creating new buffers
     * @param {number} silenceThreshold - Threshold for silence detection (0-1, default: 0.01)
     * @param {number} marginMs - Margin to keep before/after speech in milliseconds (default: 100ms)
     * @returns {AudioBuffer} - Trimmed audio buffer
     */
  }, {
    key: "trimSilence",
    value: function trimSilence(audioBuffer, audioContext) {
      var silenceThreshold = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0.01;
      var marginMs = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 100;
      var sampleRate = audioBuffer.sampleRate;
      var numberOfChannels = audioBuffer.numberOfChannels;
      var length = audioBuffer.length;

      // Convert margin from milliseconds to samples
      var marginSamples = Math.floor(marginMs / 1000 * sampleRate);

      // Get audio data for analysis (use first channel if stereo)
      var audioData = audioBuffer.getChannelData(0);

      // Find start of speech (first non-silent sample)
      var startIndex = 0;
      for (var i = 0; i < length; i++) {
        if (Math.abs(audioData[i]) > silenceThreshold) {
          startIndex = Math.max(0, i - marginSamples);
          break;
        }
      }

      // Find end of speech (last non-silent sample)
      var endIndex = length - 1;
      for (var _i = length - 1; _i >= 0; _i--) {
        if (Math.abs(audioData[_i]) > silenceThreshold) {
          endIndex = Math.min(length - 1, _i + marginSamples);
          break;
        }
      }

      // If no speech detected, return a minimal buffer
      if (startIndex >= endIndex) {
        console.warn('No speech detected in audio, returning minimal buffer');
        var minimalLength = Math.floor(sampleRate * 0.1); // 100ms minimal buffer
        var minimalBuffer = audioContext.createBuffer(numberOfChannels, minimalLength, sampleRate);
        return minimalBuffer;
      }

      // Calculate new buffer length
      var newLength = endIndex - startIndex + 1;

      // Create new trimmed buffer
      var trimmedBuffer = audioContext.createBuffer(numberOfChannels, newLength, sampleRate);

      // Copy trimmed audio data for all channels
      for (var channel = 0; channel < numberOfChannels; channel++) {
        var originalData = audioBuffer.getChannelData(channel);
        var trimmedData = trimmedBuffer.getChannelData(channel);
        for (var _i2 = 0; _i2 < newLength; _i2++) {
          trimmedData[_i2] = originalData[startIndex + _i2];
        }
      }

      // Log trimming results
      var originalDuration = (length / sampleRate * 1000).toFixed(0);
      var trimmedDuration = (newLength / sampleRate * 1000).toFixed(0);
      var trimmedStart = (startIndex / sampleRate * 1000).toFixed(0);
      var trimmedEnd = ((length - endIndex - 1) / sampleRate * 1000).toFixed(0);
      console.log("Audio trimmed: ".concat(originalDuration, "ms \u2192 ").concat(trimmedDuration, "ms (removed ").concat(trimmedStart, "ms from start, ").concat(trimmedEnd, "ms from end)"));
      return trimmedBuffer;
    }
  }, {
    key: "audioBufferToWav",
    value: function audioBufferToWav(buffer) {
      // Target settings for 256kbps (approximately)
      var targetSampleRate = 16000; // 16kHz for speech quality
      var targetChannels = 1; // Mono for speech
      var bitsPerSample = 16;

      // Resample and convert to mono if needed
      var resampledBuffer = this.resampleAndConvertToMono(buffer, targetSampleRate);
      var length = resampledBuffer.length;
      var numberOfChannels = targetChannels;
      var sampleRate = targetSampleRate;
      var bytesPerSample = bitsPerSample / 8;
      var blockAlign = numberOfChannels * bytesPerSample;
      var byteRate = sampleRate * blockAlign; // This will be 32000 bytes/sec = 256kbps
      var dataSize = length * blockAlign;
      var bufferSize = 44 + dataSize;
      var arrayBuffer = new ArrayBuffer(bufferSize);
      var view = new DataView(arrayBuffer);

      // Write WAV header
      var writeString = function writeString(offset, string) {
        for (var i = 0; i < string.length; i++) {
          view.setUint8(offset + i, string.charCodeAt(i));
        }
      };

      // RIFF header
      writeString(0, 'RIFF');
      view.setUint32(4, bufferSize - 8, true);
      writeString(8, 'WAVE');

      // Format chunk
      writeString(12, 'fmt ');
      view.setUint32(16, 16, true); // Subchunk1Size
      view.setUint16(20, 1, true); // AudioFormat (PCM)
      view.setUint16(22, numberOfChannels, true);
      view.setUint32(24, sampleRate, true);
      view.setUint32(28, byteRate, true);
      view.setUint16(32, blockAlign, true);
      view.setUint16(34, bitsPerSample, true);

      // Data chunk
      writeString(36, 'data');
      view.setUint32(40, dataSize, true);

      // Write audio data (mono)
      var offset = 44;
      for (var i = 0; i < length; i++) {
        var sample = Math.max(-1, Math.min(1, resampledBuffer[i]));
        var intSample = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;
        view.setInt16(offset, intSample, true);
        offset += 2;
      }
      return new Blob([arrayBuffer], {
        type: 'audio/wav'
      });
    }
  }, {
    key: "resampleAndConvertToMono",
    value: function resampleAndConvertToMono(buffer, targetSampleRate) {
      var originalSampleRate = buffer.sampleRate;
      var originalLength = buffer.length;
      var numberOfChannels = buffer.numberOfChannels;

      // Calculate new length after resampling
      var resampleRatio = targetSampleRate / originalSampleRate;
      var newLength = Math.floor(originalLength * resampleRatio);

      // Create output array
      var resampledData = new Float32Array(newLength);

      // Convert to mono first (mix all channels)
      var monoData = new Float32Array(originalLength);
      for (var i = 0; i < originalLength; i++) {
        var sum = 0;
        for (var channel = 0; channel < numberOfChannels; channel++) {
          sum += buffer.getChannelData(channel)[i];
        }
        monoData[i] = sum / numberOfChannels; // Average all channels
      }

      // Resample using linear interpolation
      for (var _i3 = 0; _i3 < newLength; _i3++) {
        var originalIndex = _i3 / resampleRatio;
        var index = Math.floor(originalIndex);
        var fraction = originalIndex - index;
        if (index + 1 < originalLength) {
          // Linear interpolation between two samples
          resampledData[_i3] = monoData[index] * (1 - fraction) + monoData[index + 1] * fraction;
        } else {
          // Use the last sample if we're at the end
          resampledData[_i3] = monoData[index] || 0;
        }
      }
      return resampledData;
    }
  }, {
    key: "submitForGrading",
    value: function () {
      var _submitForGrading = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee6(audioBlob) {
        var params, result, _t4;
        return _regenerator().w(function (_context6) {
          while (1) switch (_context6.p = _context6.n) {
            case 0:
              _context6.p = 0;
              // Show loading state
              this.showLoadingState();

              // Get current practice parameters
              params = {
                topic_id: topicItemData.topic_id || '',
                // topicItemData is global variable
                id: topicItemData.id || '',
                item_type: topicItemData.item_type || 'question'
              }; // Submit to pronunciation service
              _context6.n = 1;
              return this.pronunciationService.gradeRecording(audioBlob, params);
            case 1:
              result = _context6.v;
              // Display results
              this.displayResults(result);
              _context6.n = 3;
              break;
            case 2:
              _context6.p = 2;
              _t4 = _context6.v;
              console.error('Error submitting recording:', _t4);
              this.showError(_t4.message || 'Failed to submit recording for grading');
            case 3:
              _context6.p = 3;
              this.hideLoadingState();
              return _context6.f(3);
            case 4:
              return _context6.a(2);
          }
        }, _callee6, this, [[0, 2, 3, 4]]);
      }));
      function submitForGrading(_x3) {
        return _submitForGrading.apply(this, arguments);
      }
      return submitForGrading;
    }()
  }, {
    key: "displayResults",
    value: function displayResults(data) {
      // Extract real data from Azure pronunciation assessment response
      if (!data || !data.analysis) {
        this.showError('Invalid pronunciation analysis data received');
        return;
      }
      var analysis = data.analysis;
      var scores = analysis.scores || {};
      var itemData = data.item_data || {};

      // Extract scores with fallback values
      var overallScore = Math.round(analysis.overall_score || 0);
      var accuracy = Math.round(scores.accuracy || 0);
      var fluency = Math.round(scores.fluency || 0);
      var completeness = Math.round(scores.completeness || 0);
      var pronunciation = Math.round(scores.pronunciation || 0);

      // Extract text data
      var expectedText = data.expected_text || itemData.word || 'Unknown';
      var recognizedText = analysis.recognized_text || 'Not recognized';
      var feedback = analysis.feedback || 'No feedback available';

      // Extract additional information
      var detectedIssues = analysis.detected_issues || [];
      var suggestions = analysis.suggestions || [];

      // Build issues and suggestions HTML
      var issuesHtml = '';
      if (detectedIssues.length > 0) {
        issuesHtml = "\n                <div class=\"detected-issues\">\n                    <h4>Detected Issues:</h4>\n                    <ul>\n                        ".concat(detectedIssues.map(function (issue) {
          return "<li>".concat(issue, "</li>");
        }).join(''), "\n                    </ul>\n                </div>\n            ");
      }
      var suggestionsHtml = '';
      if (suggestions.length > 0) {
        suggestionsHtml = "\n                <div class=\"suggestions\">\n                    <h4>Suggestions for Improvement:</h4>\n                    <ul>\n                        ".concat(suggestions.map(function (suggestion) {
          return "<li>".concat(suggestion, "</li>");
        }).join(''), "\n                    </ul>\n                </div>\n            ");
      }

      // Update practice result section
      var practiceResult = document.querySelector('.practice-result');
      if (practiceResult) {
        practiceResult.innerHTML = "\n                <div class=\"result-card\">\n                    <h3>Pronunciation Analysis</h3>\n                    \n                    <div class=\"text-comparison\">\n                        <div class=\"expected-text\">\n                            <span class=\"text-label\">Expected:</span>\n                            <span class=\"text-value\">\"".concat(expectedText, "\"</span>\n                        </div>\n                        <div class=\"recognized-text\">\n                            <span class=\"text-label\">You said:</span>\n                            <span class=\"text-value\">\"").concat(recognizedText, "\"</span>\n                        </div>\n                    </div>\n                    \n                    <div class=\"overall-score\">\n                        <div class=\"circular-progress ").concat(this.getScoreClass(overallScore), "\">\n                            <div class=\"circular-progress-inner\">\n                                <div class=\"circular-progress-circle\">\n                                    <div class=\"circular-progress-mask full\">\n                                        <div class=\"circular-progress-fill\" style=\"transform: rotate(").concat(overallScore * 3.6, "deg)\"></div>\n                                    </div>\n                                    <div class=\"circular-progress-mask\">\n                                        <div class=\"circular-progress-fill\" style=\"transform: rotate(").concat(Math.min(180, overallScore * 3.6), "deg)\"></div>\n                                    </div>\n                                    <div class=\"circular-progress-inside\">\n                                        <span class=\"circular-progress-percentage\">").concat(overallScore, "<span class=\"percentage-sign\">%</span></span>\n                                        <span class=\"circular-progress-label\">Overall Score</span>\n                                    </div>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n                    \n                    <div class=\"detailed-metrics\">\n                        <div class=\"metric\">\n                            <span class=\"metric-label\">Accuracy:</span>\n                            <div class=\"metric-bar\">\n                                <div class=\"metric-fill ").concat(this.getScoreClass(accuracy), "\" style=\"width: ").concat(accuracy, "%\"></div>\n                            </div>\n                            <span class=\"metric-value\">").concat(accuracy, "%</span>\n                        </div>\n                        <div class=\"metric\">\n                            <span class=\"metric-label\">Fluency:</span>\n                            <div class=\"metric-bar\">\n                                <div class=\"metric-fill ").concat(this.getScoreClass(fluency), "\" style=\"width: ").concat(fluency, "%\"></div>\n                            </div>\n                            <span class=\"metric-value\">").concat(fluency, "%</span>\n                        </div>\n                        <div class=\"metric\">\n                            <span class=\"metric-label\">Completeness:</span>\n                            <div class=\"metric-bar\">\n                                <div class=\"metric-fill ").concat(this.getScoreClass(completeness), "\" style=\"width: ").concat(completeness, "%\"></div>\n                            </div>\n                            <span class=\"metric-value\">").concat(completeness, "%</span>\n                        </div>\n                        <div class=\"metric\">\n                            <span class=\"metric-label\">Pronunciation:</span>\n                            <div class=\"metric-bar\">\n                                <div class=\"metric-fill ").concat(this.getScoreClass(pronunciation), "\" style=\"width: ").concat(pronunciation, "%\"></div>\n                            </div>\n                            <span class=\"metric-value\">").concat(pronunciation, "%</span>\n                        </div>\n                    </div>\n                    \n                    <div class=\"feedback-text\">\n                        <h4>Feedback:</h4>\n                        <p>").concat(feedback, "</p>\n                    </div>\n                    \n                    ").concat(issuesHtml, "\n                    ").concat(suggestionsHtml, "\n                    \n                    <div class=\"result-actions\">\n                        <button class=\"btn btn-primary\" onclick=\"pronunciationRecorder.resetRecording()\">Try Again</button>\n                        <button class=\"btn btn-secondary\" onclick=\"window.history.back()\">Back to Topics</button>\n                    </div>\n                </div>\n            ");
      }
      console.log('Pronunciation results displayed:', {
        overallScore: overallScore,
        scores: {
          accuracy: accuracy,
          fluency: fluency,
          completeness: completeness,
          pronunciation: pronunciation
        },
        expectedText: expectedText,
        recognizedText: recognizedText,
        feedback: feedback,
        detectedIssues: detectedIssues,
        suggestions: suggestions
      });
    }
  }, {
    key: "getScoreClass",
    value: function getScoreClass(score) {
      if (score >= 80) return 'score-excellent';
      if (score >= 60) return 'score-good';
      if (score >= 40) return 'score-fair';
      return 'score-poor';
    }
  }, {
    key: "showRecordingElements",
    value: function showRecordingElements() {
      if (this.recordingStatus) {
        this.recordingStatus.style.display = 'block';
      }
      if (this.recordingDuration) {
        this.recordingDuration.style.display = 'flex';
      }
    }
  }, {
    key: "hideRecordingElements",
    value: function hideRecordingElements() {
      if (this.recordingStatus) {
        this.recordingStatus.style.display = 'none';
      }
      if (this.recordingDuration) {
        this.recordingDuration.style.display = 'none';
      }
    }
  }, {
    key: "updateButtonState",
    value: function updateButtonState() {
      if (this.recordBtn) {
        var recordText = this.recordBtn.querySelector('.record-text');
        if (this.isRecording) {
          this.recordBtn.classList.add('recording');
          if (recordText) recordText.textContent = 'Stop Recording';
        } else {
          this.recordBtn.classList.remove('recording');
          if (recordText) recordText.textContent = 'Record now';
        }
      }
    }
  }, {
    key: "startTimer",
    value: function startTimer() {
      var _this5 = this;
      this.timerInterval = setInterval(function () {
        if (_this5.startTime && _this5.durationText) {
          var elapsed = Date.now() - _this5.startTime;
          var minutes = Math.floor(elapsed / 60000);
          var seconds = Math.floor(elapsed % 60000 / 1000);
          var timeString = "".concat(minutes.toString().padStart(2, '0'), ":").concat(seconds.toString().padStart(2, '0'));
          _this5.durationText.textContent = timeString;
        }
      }, 100);
    }
  }, {
    key: "stopTimer",
    value: function stopTimer() {
      if (this.timerInterval) {
        clearInterval(this.timerInterval);
        this.timerInterval = null;
      }
    }
  }, {
    key: "showLoadingState",
    value: function showLoadingState() {
      if (this.recordBtn) {
        this.recordBtn.disabled = true;
        var recordText = this.recordBtn.querySelector('.record-text');
        if (recordText) recordText.textContent = 'Processing...';
      }
    }
  }, {
    key: "hideLoadingState",
    value: function hideLoadingState() {
      if (this.recordBtn) {
        this.recordBtn.disabled = false;
        this.updateButtonState();
      }
    }
  }, {
    key: "showError",
    value: function showError(message) {
      console.error('PronunciationRecorder Error:', message);

      // You can implement a more sophisticated error display here
      var practiceResult = document.querySelector('.practice-result');
      if (practiceResult) {
        practiceResult.innerHTML = "\n                <div class=\"error-message\">\n                    <h3>Error</h3>\n                    <p>".concat(message, "</p>\n                    <button class=\"btn btn-primary\" onclick=\"pronunciationRecorder.resetRecording()\">Try Again</button>\n                </div>\n            ");
      }
    }
  }, {
    key: "resetRecording",
    value: function resetRecording() {
      // Reset the recording state
      this.isRecording = false;
      this.audioChunks = [];
      this.startTime = null;

      // Clear any existing intervals
      this.stopTimer();

      // Reset UI
      this.hideRecordingElements();
      this.updateButtonState();

      // Clear results
      var practiceResult = document.querySelector('.practice-result');
      if (practiceResult) {
        practiceResult.innerHTML = '';
      }

      // Reset duration text
      if (this.durationText) {
        this.durationText.textContent = '00:00';
      }
    }
  }, {
    key: "getNonce",
    value: function getNonce() {
      // Get WordPress nonce if available
      return window.toeic_ajax_nonce || '';
    }
  }, {
    key: "handleSuggestion",
    value: function handleSuggestion(e) {
      var _this6 = this;
      var prompt = e.target.dataset.prompt;
      var subject = this.paragraphText;
      this.getSuggestion({
        prompt: prompt,
        subject: subject
      }).then(function (data) {
        _this6.showSuggestion(data);
      });
    }
  }, {
    key: "showSuggestion",
    value: function showSuggestion(_ref3) {
      var title = _ref3.title,
        suggestion = _ref3.suggestion;
      var template = "\n            <div class=\"suggestion-item\">\n                <div class=\"suggestion-item__content\">".concat(suggestion, "</div>\n            </div>\n        ");
      var suggestionsResult = document.querySelector('.suggestion-result');
      if (suggestionsResult) {
        suggestionsResult.innerHTML += template;
      }
    }
  }, {
    key: "getSuggestion",
    value: function () {
      var _getSuggestion = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee7(_ref4) {
        var prompt, subject, data, _t5;
        return _regenerator().w(function (_context7) {
          while (1) switch (_context7.p = _context7.n) {
            case 0:
              prompt = _ref4.prompt, subject = _ref4.subject;
              _context7.p = 1;
              _context7.n = 2;
              return this.pronunciationService.getSuggestions({
                prompt: prompt,
                subject: subject
              });
            case 2:
              data = _context7.v;
              return _context7.a(2, {
                "suggestion": data.suggestion
              });
            case 3:
              _context7.p = 3;
              _t5 = _context7.v;
              console.error('Error fetching suggestions:', _t5);
            case 4:
              return _context7.a(2);
          }
        }, _callee7, this, [[1, 3]]);
      }));
      function getSuggestion(_x4) {
        return _getSuggestion.apply(this, arguments);
      }
      return getSuggestion;
    }()
  }]);
}();


/***/ }),

/***/ "./assets/js/frontend/src/pages/test-detail-page.js":
/*!**********************************************************!*\
  !*** ./assets/js/frontend/src/pages/test-detail-page.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _components_Loader__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../components/Loader */ "./assets/js/frontend/src/components/Loader.js");
/* harmony import */ var _services_TestService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../services/TestService */ "./assets/js/frontend/src/services/TestService.js");
/* harmony import */ var _components_questions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/questions */ "./assets/js/frontend/src/components/questions/index.js");
function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError("Cannot call a class as a function"); }
function _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, "value" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }
function _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, "prototype", { writable: !1 }), e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
/**
 * TOEIC Test Detail Page
 * 
 * Handles the test-taking functionality including:
 * 1. Timer countdown with progress bar
 * 2. Question navigation and display
 * 3. Answer selection and submission
 */




var TestDetailPage = /*#__PURE__*/function () {
  /**
   * Constructor
   * 
   * @param {HTMLElement} element - The test detail page container element
   */
  function TestDetailPage(element) {
    _classCallCheck(this, TestDetailPage);
    // Main container and data
    this.element = element;
    this.testData = window.toeicTestData || {};
    this.questions = [];
    this.sections = this.testData.sections || [];
    this.currentQuestionIndex = 0;
    this.userAnswers = {};
    this.isTestActive = false;
    this.isReviewMode = false;

    // Timer elements
    this.timerBar = document.getElementById('toeic-timer-bar');
    this.timerDisplay = document.getElementById('toeic-timer-display');
    this.timerMinutes = document.getElementById('toeic-timer-minutes');
    this.timerSeconds = document.getElementById('toeic-timer-seconds');

    // Question elements
    this.questionContainer = document.getElementById('toeic-question-container');
    this.sectionInfo = document.getElementById('toeic-section-info');
    this.sectionTitle = document.getElementById('toeic-section-title');
    this.sectionInstructions = document.getElementById('toeic-section-instructions');
    this.questionNumber = document.getElementById('toeic-question-number');
    this.questionText = document.getElementById('toeic-question-text');
    this.questionOptions = document.getElementById('toeic-question-options');

    // Navigation elements
    this.prevButton = document.getElementById('toeic-prev-btn');
    this.nextButton = document.getElementById('toeic-next-btn');
    this.submitButton = document.getElementById('toeic-submit-btn');
    this.questionIndicators = document.getElementById('toeic-question-indicators');
    this.currentQuestionDisplay = document.getElementById('toeic-current-question');
    this.totalQuestionsDisplay = document.getElementById('toeic-total-questions');

    // Results elements
    this.resultsContainer = document.getElementById('toeic-results-container');
    this.resultsContent = document.getElementById('toeic-results-content');
    this.reviewButton = document.getElementById('toeic-review-btn');

    // Timer variables
    this.timeLimit = this.testData.time_limit || 3600; // Default 60 minutes
    this.timeRemaining = this.timeLimit;
    this.timerInterval = null;

    // Create loader
    this.loader = _components_Loader__WEBPACK_IMPORTED_MODULE_0__["default"].forElement(this.element, {
      text: 'Loading test data...'
    });
  }

  /**
   * Initialize the test page
   */
  return _createClass(TestDetailPage, [{
    key: "init",
    value: function init() {
      // Load questions
      this.loadQuestions();

      // Set up event listeners
      this.setupEventListeners();
    }

    /**
     * Load questions from the server
     */
  }, {
    key: "loadQuestions",
    value: function loadQuestions() {
      var _this = this;
      if (!this.testData.id) {
        console.error('No test ID provided');
        return;
      }
      this.loader.show('Loading test questions...');
      _services_TestService__WEBPACK_IMPORTED_MODULE_1__["default"].getTestQuestions(this.testData.id).then(function (data) {
        _this.questions = data.questions || [];
        _this.renderQuestionIndicators();
        _this.startTest();
      })["catch"](function (error) {
        console.error('Error loading questions:', error);
        alert('Failed to load test questions. Please try again.');
      })["finally"](function () {
        _this.loader.hide();
      });
    }

    /**
     * Set up event listeners
     */
  }, {
    key: "setupEventListeners",
    value: function setupEventListeners() {
      var _this2 = this;
      // Navigation buttons
      if (this.prevButton) {
        this.prevButton.addEventListener('click', this.goToPreviousQuestion.bind(this));
      }
      if (this.nextButton) {
        this.nextButton.addEventListener('click', this.goToNextQuestion.bind(this));
      }
      if (this.submitButton) {
        this.submitButton.addEventListener('click', this.confirmSubmitTest.bind(this));
      }
      if (this.reviewButton) {
        this.reviewButton.addEventListener('click', this.startReviewMode.bind(this));
      }

      // Handle beforeunload event to warn user before leaving
      window.addEventListener('beforeunload', function (event) {
        if (_this2.isTestActive && !_this2.isReviewMode) {
          var message = 'You are in the middle of a test. Are you sure you want to leave?';
          event.returnValue = message;
          return message;
        }
      });
    }

    /**
     * Start the test
     */
  }, {
    key: "startTest",
    value: function startTest() {
      if (!this.questions.length) {
        alert('No questions available for this test.');
        return;
      }
      this.isTestActive = true;
      this.currentQuestionIndex = 0;
      this.userAnswers = {};

      // Start the timer
      this.startTimer();

      // Show the first question
      this.showCurrentQuestion();
    }

    /**
     * Start the timer
     */
  }, {
    key: "startTimer",
    value: function startTimer() {
      var _this3 = this;
      // Reset timer
      this.timeRemaining = this.timeLimit;
      this.updateTimerDisplay();

      // Clear any existing interval
      if (this.timerInterval) {
        clearInterval(this.timerInterval);
      }

      // Start new interval
      var startTime = Date.now();
      this.timerInterval = setInterval(function () {
        var elapsedSeconds = Math.floor((Date.now() - startTime) / 1000);
        _this3.timeRemaining = Math.max(0, _this3.timeLimit - elapsedSeconds);
        _this3.updateTimerDisplay();

        // If time is up, auto-submit the test
        if (_this3.timeRemaining <= 0) {
          clearInterval(_this3.timerInterval);
          _this3.submitTest();
        }
      }, 1000);
    }

    /**
     * Update the timer display
     */
  }, {
    key: "updateTimerDisplay",
    value: function updateTimerDisplay() {
      // Calculate minutes and seconds
      var minutes = Math.floor(this.timeRemaining / 60);
      var seconds = this.timeRemaining % 60;

      // Update text display
      if (this.timerMinutes) {
        this.timerMinutes.textContent = minutes.toString().padStart(2, '0');
      }
      if (this.timerSeconds) {
        this.timerSeconds.textContent = seconds.toString().padStart(2, '0');
      }

      // Update progress bar
      if (this.timerBar) {
        var percentRemaining = this.timeRemaining / this.timeLimit * 100;
        this.timerBar.style.width = "".concat(percentRemaining, "%");

        // Change color based on time remaining
        if (percentRemaining < 20) {
          this.timerBar.style.backgroundColor = '#ff4d4d'; // Red
        } else if (percentRemaining < 50) {
          this.timerBar.style.backgroundColor = '#ffa64d'; // Orange
        } else {
          this.timerBar.style.backgroundColor = '#4CAF50'; // Green
        }
      }
    }

    /**
     * Render question indicators for navigation
     */
  }, {
    key: "renderQuestionIndicators",
    value: function renderQuestionIndicators() {
      var _this4 = this;
      if (!this.questionIndicators || !this.questions.length) return;
      this.questionIndicators.innerHTML = '';
      this.questions.forEach(function (_, index) {
        var indicator = document.createElement('div');
        indicator.className = 'toeic-question-indicator';
        indicator.dataset.index = index;
        indicator.addEventListener('click', function () {
          return _this4.goToQuestion(index);
        });
        _this4.questionIndicators.appendChild(indicator);
      });
    }

    /**
     * Update question indicators based on answered status
     */
  }, {
    key: "updateQuestionIndicators",
    value: function updateQuestionIndicators() {
      var _this5 = this;
      if (!this.questionIndicators) return;
      var indicators = this.questionIndicators.querySelectorAll('.toeic-question-indicator');
      indicators.forEach(function (indicator, index) {
        var _this5$questions$inde;
        // Remove all classes first
        indicator.classList.remove('current', 'answered', 'unanswered');

        // Add appropriate class
        if (index === _this5.currentQuestionIndex) {
          indicator.classList.add('current');
        }
        var questionId = (_this5$questions$inde = _this5.questions[index]) === null || _this5$questions$inde === void 0 ? void 0 : _this5$questions$inde.id;
        if (questionId && _this5.userAnswers[questionId]) {
          indicator.classList.add('answered');
        } else {
          indicator.classList.add('unanswered');
        }
      });
    }

    /**
     * Show the current question
     */
  }, {
    key: "showCurrentQuestion",
    value: function showCurrentQuestion() {
      if (!this.questions.length) return;
      var question = this.questions[this.currentQuestionIndex];
      if (!question) return;

      // Update question number display
      if (this.currentQuestionDisplay) {
        this.currentQuestionDisplay.textContent = (this.currentQuestionIndex + 1).toString();
      }
      if (this.totalQuestionsDisplay) {
        this.totalQuestionsDisplay.textContent = this.questions.length.toString();
      }

      // Find section for this question
      var section = this.sections.find(function (s) {
        return s.id === question.section_id;
      });

      // Update section info if it's available and different from current
      if (section) {
        if (this.sectionTitle) {
          this.sectionTitle.textContent = section.title || '';
        }
        if (this.sectionInstructions) {
          this.sectionInstructions.innerHTML = section.instructions || '';
        }
      }

      // Update question display
      if (this.questionNumber) {
        this.questionNumber.textContent = "Question ".concat(this.currentQuestionIndex + 1);
      }
      if (this.questionText) {
        this.questionText.innerHTML = question.content || '';
      }

      // Render question options
      this.renderQuestionOptions(question);

      // Update navigation buttons
      this.updateNavigationButtons();

      // Update question indicators
      this.updateQuestionIndicators();
    }

    /**
     * Render options for the current question
     * 
     * @param {Object} question - The question object
     */
  }, {
    key: "renderQuestionOptions",
    value: function renderQuestionOptions(question) {
      if (!this.questionOptions || !question) return;
      this.questionOptions.innerHTML = '';
      try {
        // Use the factory to create the appropriate renderer
        var renderer = _components_questions__WEBPACK_IMPORTED_MODULE_2__.QuestionRendererFactory.createRenderer(question, this.questionOptions, {
          isReviewMode: this.isReviewMode,
          userAnswers: this.userAnswers,
          onAnswerSelected: this.saveAnswer.bind(this)
        });

        // Render the question
        renderer.render();
      } catch (error) {
        console.error('Error rendering question:', error);

        // Fallback for unsupported question types
        this.renderFallbackQuestion(question);
      }
    }

    /**
     * Fallback renderer for unsupported question types
     * 
     * @param {Object} question - The question object
     */
  }, {
    key: "renderFallbackQuestion",
    value: function renderFallbackQuestion(question) {
      var fallbackElement = document.createElement('div');
      fallbackElement.className = 'toeic-question-fallback';
      fallbackElement.innerHTML = "\n            <p class=\"toeic-question-unsupported\">\n                This question type (".concat(question.type || 'unknown', ") is not supported in the current view.\n            </p>\n        ");
      this.questionOptions.appendChild(fallbackElement);
    }

    /**
     * Save the user's answer for a question
     * 
     * @param {number|string} questionId - The question ID
     * @param {string} answer - The selected answer
     */
  }, {
    key: "saveAnswer",
    value: function saveAnswer(questionId, answer) {
      this.userAnswers[questionId] = answer;
      this.updateQuestionIndicators();
    }

    /**
     * Update navigation buttons based on current question
     */
  }, {
    key: "updateNavigationButtons",
    value: function updateNavigationButtons() {
      // Previous button
      if (this.prevButton) {
        this.prevButton.disabled = this.currentQuestionIndex === 0;
      }

      // Next button
      if (this.nextButton) {
        var isLastQuestion = this.currentQuestionIndex === this.questions.length - 1;
        this.nextButton.style.display = isLastQuestion ? 'none' : 'inline-block';
      }

      // Submit button is always visible with the new UI
      // No need to toggle visibility based on question index
    }

    /**
     * Go to a specific question
     * 
     * @param {number} index - Question index to navigate to
     */
  }, {
    key: "goToQuestion",
    value: function goToQuestion(index) {
      if (index < 0 || index >= this.questions.length) return;
      this.currentQuestionIndex = index;
      this.showCurrentQuestion();
    }

    /**
     * Go to the previous question
     */
  }, {
    key: "goToPreviousQuestion",
    value: function goToPreviousQuestion() {
      if (this.currentQuestionIndex > 0) {
        this.currentQuestionIndex--;
        this.showCurrentQuestion();
      }
    }

    /**
     * Go to the next question
     */
  }, {
    key: "goToNextQuestion",
    value: function goToNextQuestion() {
      if (this.currentQuestionIndex < this.questions.length - 1) {
        this.currentQuestionIndex++;
        this.showCurrentQuestion();
      }
    }

    /**
     * Confirm before submitting the test
     */
  }, {
    key: "confirmSubmitTest",
    value: function confirmSubmitTest() {
      // Count unanswered questions
      var answeredCount = Object.keys(this.userAnswers).length;
      var unansweredCount = this.questions.length - answeredCount;
      var message = 'Are you sure you want to submit your test?';
      if (unansweredCount > 0) {
        message = "You have ".concat(unansweredCount, " unanswered question").concat(unansweredCount > 1 ? 's' : '', ". Are you sure you want to submit your test?");
      }
      if (confirm(message)) {
        this.submitTest();
      }
    }

    /**
     * Submit the test
     */
  }, {
    key: "submitTest",
    value: function submitTest() {
      var _this6 = this;
      // Stop the timer
      if (this.timerInterval) {
        clearInterval(this.timerInterval);
      }
      this.isTestActive = false;

      // Show loader
      this.loader.show('Submitting your test...');

      // Prepare data for submission
      var data = {
        test_id: this.testData.id,
        time_taken: this.timeLimit - this.timeRemaining,
        answers: this.userAnswers
      };

      // Submit to server
      _services_TestService__WEBPACK_IMPORTED_MODULE_1__["default"].submitTest(this.testData.id, this.userAnswers, this.timeLimit - this.timeRemaining).then(function (response) {
        _this6.showResults(response);
      })["catch"](function (error) {
        console.error('Error submitting test:', error);
        alert('Failed to submit your test. Please try again.');
      })["finally"](function () {
        _this6.loader.hide();
      });
    }

    /**
     * Show test results
     * 
     * @param {Object} results - Test results from server
     */
  }, {
    key: "showResults",
    value: function showResults(results) {
      // Hide question container
      if (this.questionContainer) {
        this.questionContainer.style.display = 'none';
      }

      // Show results container
      if (this.resultsContainer) {
        this.resultsContainer.style.display = 'block';
      }

      // Hide navigation
      var navigationElement = document.querySelector('.toeic-test-navigation');
      if (navigationElement) {
        navigationElement.style.display = 'none';
      }

      // Render results content
      if (this.resultsContent) {
        var score = results.score || 0;
        var totalPoints = results.total_points || this.questions.length;
        var percentage = Math.round(score / totalPoints * 100);
        var resultClass = 'average';
        if (percentage >= 80) {
          resultClass = 'excellent';
        } else if (percentage >= 60) {
          resultClass = 'good';
        } else if (percentage < 40) {
          resultClass = 'poor';
        }
        this.resultsContent.innerHTML = "\n                <div class=\"toeic-score ".concat(resultClass, "\">\n                    <div class=\"toeic-score-number\">").concat(score, "/").concat(totalPoints, "</div>\n                    <div class=\"toeic-score-percentage\">").concat(percentage, "%</div>\n                </div>\n                \n                <div class=\"toeic-score-breakdown\">\n                    <h3>Score Breakdown</h3>\n                    <table class=\"toeic-score-table\">\n                        <thead>\n                            <tr>\n                                <th>Section</th>\n                                <th>Score</th>\n                                <th>Out of</th>\n                                <th>Percentage</th>\n                            </tr>\n                        </thead>\n                        <tbody>\n                            ").concat(this.renderSectionScores(results.section_scores || []), "\n                        </tbody>\n                    </table>\n                </div>\n                \n                <div class=\"toeic-feedback\">\n                    <h3>Feedback</h3>\n                    <p>").concat(results.feedback || 'No feedback available.', "</p>\n                </div>\n            ");
      }
    }

    /**
     * Render section scores for the results table
     * 
     * @param {Array} sectionScores - Array of section score objects
     * @returns {string} HTML for the section scores table rows
     */
  }, {
    key: "renderSectionScores",
    value: function renderSectionScores(sectionScores) {
      if (!sectionScores || !sectionScores.length) {
        return '<tr><td colspan="4">No section scores available</td></tr>';
      }
      return sectionScores.map(function (section) {
        var percentage = Math.round(section.score / section.total * 100);
        return "\n                <tr>\n                    <td>".concat(section.title, "</td>\n                    <td>").concat(section.score, "</td>\n                    <td>").concat(section.total, "</td>\n                    <td>").concat(percentage, "%</td>\n                </tr>\n            ");
      }).join('');
    }

    /**
     * Start review mode to review answers
     */
  }, {
    key: "startReviewMode",
    value: function startReviewMode() {
      this.isReviewMode = true;

      // Show question container again
      if (this.questionContainer) {
        this.questionContainer.style.display = 'block';
      }

      // Show navigation again but with modified buttons
      var navigationElement = document.querySelector('.toeic-test-navigation');
      if (navigationElement) {
        navigationElement.style.display = 'block';

        // Hide submit button
        if (this.submitButton) {
          this.submitButton.style.display = 'none';
        }

        // Show next button for all questions
        if (this.nextButton) {
          this.nextButton.style.display = 'inline-block';
        }
      }

      // Hide results
      if (this.resultsContainer) {
        this.resultsContainer.style.display = 'none';
      }

      // Go to first question
      this.currentQuestionIndex = 0;
      this.showCurrentQuestion();
    }
  }]);
}(); // Initialize the page when DOM is loaded
document.addEventListener('DOMContentLoaded', function () {
  var testDetailElement = document.getElementById('toeic-test-detail');
  if (testDetailElement) {
    var testDetailPage = new TestDetailPage(testDetailElement);
    testDetailPage.init();
  }
});
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TestDetailPage);

/***/ }),

/***/ "./assets/js/frontend/src/services/AjaxService.js":
/*!********************************************************!*\
  !*** ./assets/js/frontend/src/services/AjaxService.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AjaxService: () => (/* binding */ AjaxService),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function _slicedToArray(r, e) { return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest(); }
function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }
function _iterableToArrayLimit(r, l) { var t = null == r ? null : "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t["return"] && (u = t["return"](), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }
function _arrayWithHoles(r) { if (Array.isArray(r)) return r; }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }
function _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError("Cannot call a class as a function"); }
function _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, "value" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }
function _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, "prototype", { writable: !1 }), e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
/**
 * TOEIC Practice Plugin - Ajax Service
 * 
 * A service class that provides a secure interface for making AJAX requests
 * to WordPress backend, handling nonces and other security aspects.
 */
var AjaxService = /*#__PURE__*/function () {
  /**
   * Constructor
   * 
   * @param {Object} options - Configuration options
   * @param {string} options.ajaxUrl - WordPress AJAX URL (default: from global toeicPracticeAjax)
   * @param {string} options.nonce - Security nonce (default: from global toeicPracticeAjax)
   * @param {string} options.nonceParam - Name of nonce parameter (default: 'nonce')
   * @param {Function} options.onError - Global error handler (optional)
   */
  function AjaxService() {
    var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
    _classCallCheck(this, AjaxService);
    // Check if global toeicPracticeAjax is available
    var globalData = window.toeicPracticeAjax || {};
    this.ajaxUrl = options.ajaxUrl || globalData.ajaxUrl || '/wp-admin/admin-ajax.php';
    this.nonce = options.nonce || globalData.nonce || '';
    this.nonceParam = options.nonceParam || 'nonce';
    this.onError = options.onError || null;

    // Validate required parameters
    if (!this.ajaxUrl) {
      console.error('AjaxService: ajaxUrl is required');
    }
    if (!this.nonce) {
      console.warn('AjaxService: nonce is not provided, requests may fail security checks');
    }

    // Keep track of pending requests
    this.pendingRequests = new Map();
  }

  /**
   * Make a GET request
   * 
   * @param {string} action - WordPress action name
   * @param {Object} params - Additional parameters (optional)
   * @param {Object} options - Request options (optional)
   * @returns {Promise} - Promise that resolves with the response data
   */
  return _createClass(AjaxService, [{
    key: "get",
    value: function get(action) {
      var params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
      var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
      var requestParams = _objectSpread(_defineProperty({
        action: action
      }, this.nonceParam, this.nonce), params);
      var queryString = new URLSearchParams(requestParams).toString();
      var url = "".concat(this.ajaxUrl, "?").concat(queryString);
      return this._makeRequest(url, _objectSpread({
        method: 'GET'
      }, options));
    }

    /**
     * Make a POST request
     * 
     * @param {string} action - WordPress action name
     * @param {Object} data - Data to send (optional)
     * @param {Object} options - Request options (optional)
     * @returns {Promise} - Promise that resolves with the response data
     */
  }, {
    key: "post",
    value: function post(action) {
      var data = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
      var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
      var formData = new FormData();

      // Add action and nonce
      formData.append('action', action);
      formData.append(this.nonceParam, this.nonce);

      // Add other data
      Object.entries(data).forEach(function (_ref) {
        var _ref2 = _slicedToArray(_ref, 2),
          key = _ref2[0],
          value = _ref2[1];
        // Handle Blob objects (including File and audio blobs)
        if (value instanceof Blob) {
          formData.append(key, value, value.name || "".concat(key, ".blob"));
        }
        // Handle arrays and other objects (but not Blobs)
        else if (_typeof(value) === 'object' && value !== null) {
          formData.append(key, JSON.stringify(value));
        } else {
          formData.append(key, value);
        }
      });
      return this._makeRequest(this.ajaxUrl, _objectSpread({
        method: 'POST',
        body: formData
      }, options));
    }

    /**
     * Make a POST request with JSON data
     * 
     * @param {string} action - WordPress action name
     * @param {Object} data - Data to send (optional)
     * @param {Object} options - Request options (optional)
     * @returns {Promise} - Promise that resolves with the response data
     */
  }, {
    key: "postJson",
    value: function postJson(action) {
      var data = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
      var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
      var requestData = _objectSpread(_defineProperty({
        action: action
      }, this.nonceParam, this.nonce), data);
      return this._makeRequest(this.ajaxUrl, _objectSpread({
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
      }, options));
    }

    /**
     * Make a request with the fetch API
     * 
     * @param {string} url - Request URL
     * @param {Object} options - Request options
     * @returns {Promise} - Promise that resolves with the response data
     * @private
     */
  }, {
    key: "_makeRequest",
    value: function _makeRequest(url, options) {
      var _this = this;
      // Generate a unique request ID
      var requestId = Math.random().toString(36).substring(2, 15);

      // Create abort controller for timeout
      var controller = new AbortController();
      var signal = controller.signal;

      // Set timeout if specified
      var timeout = options.timeout || 30000; // Default 30 seconds
      var timeoutId = setTimeout(function () {
        controller.abort();
      }, timeout);

      // Store the request in pending requests
      this.pendingRequests.set(requestId, {
        controller: controller
      });

      // Show loader if specified
      var loader = options.loader || window.toeicLoader || null;
      var showLoader = options.showLoader !== false;
      if (loader && showLoader) {
        loader.show(options.loaderText || 'Loading...');
      }

      // Make the request
      return fetch(url, _objectSpread(_objectSpread({}, options), {}, {
        signal: signal,
        credentials: 'same-origin'
      })).then(function (response) {
        // Clear timeout
        clearTimeout(timeoutId);

        // Check if response is OK
        if (!response.ok) {
          throw new Error("HTTP error ".concat(response.status, ": ").concat(response.statusText));
        }

        // Parse response based on content type
        var contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
          return response.json();
        }
        return response.text();
      }).then(function (data) {
        // Handle WordPress AJAX response format
        if (_typeof(data) === 'object' && data !== null) {
          // Check for WordPress error response
          if (data.success === false) {
            throw new Error(data.data || 'Unknown error');
          }

          // Return data or data.data if available
          return data.data !== undefined ? data.data : data;
        }
        return data;
      })["catch"](function (error) {
        // Handle aborted requests
        if (error.name === 'AbortError') {
          throw new Error('Request timed out');
        }

        // Call global error handler if available
        if (_this.onError) {
          _this.onError(error);
        }
        throw error;
      })["finally"](function () {
        // Remove from pending requests
        _this.pendingRequests["delete"](requestId);

        // Hide loader
        if (loader && showLoader) {
          // Only hide if no other requests are pending with this loader
          var otherRequestsWithLoader = Array.from(_this.pendingRequests.values()).some(function (req) {
            return req.loader === loader;
          });
          if (!otherRequestsWithLoader) {
            loader.hide();
          }
        }
      });
    }

    /**
     * Cancel all pending requests
     */
  }, {
    key: "cancelAll",
    value: function cancelAll() {
      this.pendingRequests.forEach(function (request) {
        request.controller.abort();
      });
      this.pendingRequests.clear();
    }

    /**
     * Update the nonce
     * 
     * @param {string} nonce - New nonce value
     */
  }, {
    key: "updateNonce",
    value: function updateNonce(nonce) {
      this.nonce = nonce;
    }

    /**
     * Create a new instance with custom settings
     * 
     * @param {Object} options - Configuration options
     * @returns {AjaxService} - New AjaxService instance
     */
  }], [{
    key: "create",
    value: function create() {
      var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
      return new AjaxService(options);
    }
  }]);
}(); // Create and export default instance
var ajaxService = new AjaxService();
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ajaxService);

// Also export the class for creating custom instances


/***/ }),

/***/ "./assets/js/frontend/src/services/PronunciationService.js":
/*!*****************************************************************!*\
  !*** ./assets/js/frontend/src/services/PronunciationService.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _AjaxService_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./AjaxService.js */ "./assets/js/frontend/src/services/AjaxService.js");
function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function _regenerator() { /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */ var e, t, r = "function" == typeof Symbol ? Symbol : {}, n = r.iterator || "@@iterator", o = r.toStringTag || "@@toStringTag"; function i(r, n, o, i) { var c = n && n.prototype instanceof Generator ? n : Generator, u = Object.create(c.prototype); return _regeneratorDefine2(u, "_invoke", function (r, n, o) { var i, c, u, f = 0, p = o || [], y = !1, G = { p: 0, n: 0, v: e, a: d, f: d.bind(e, 4), d: function d(t, r) { return i = t, c = 0, u = e, G.n = r, a; } }; function d(r, n) { for (c = r, u = n, t = 0; !y && f && !o && t < p.length; t++) { var o, i = p[t], d = G.p, l = i[2]; r > 3 ? (o = l === n) && (u = i[(c = i[4]) ? 5 : (c = 3, 3)], i[4] = i[5] = e) : i[0] <= d && ((o = r < 2 && d < i[1]) ? (c = 0, G.v = n, G.n = i[1]) : d < l && (o = r < 3 || i[0] > n || n > l) && (i[4] = r, i[5] = n, G.n = l, c = 0)); } if (o || r > 1) return a; throw y = !0, n; } return function (o, p, l) { if (f > 1) throw TypeError("Generator is already running"); for (y && 1 === p && d(p, l), c = p, u = l; (t = c < 2 ? e : u) || !y;) { i || (c ? c < 3 ? (c > 1 && (G.n = -1), d(c, u)) : G.n = u : G.v = u); try { if (f = 2, i) { if (c || (o = "next"), t = i[o]) { if (!(t = t.call(i, u))) throw TypeError("iterator result is not an object"); if (!t.done) return t; u = t.value, c < 2 && (c = 0); } else 1 === c && (t = i["return"]) && t.call(i), c < 2 && (u = TypeError("The iterator does not provide a '" + o + "' method"), c = 1); i = e; } else if ((t = (y = G.n < 0) ? u : r.call(n, G)) !== a) break; } catch (t) { i = e, c = 1, u = t; } finally { f = 1; } } return { value: t, done: y }; }; }(r, o, i), !0), u; } var a = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} t = Object.getPrototypeOf; var c = [][n] ? t(t([][n]())) : (_regeneratorDefine2(t = {}, n, function () { return this; }), t), u = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(c); function f(e) { return Object.setPrototypeOf ? Object.setPrototypeOf(e, GeneratorFunctionPrototype) : (e.__proto__ = GeneratorFunctionPrototype, _regeneratorDefine2(e, o, "GeneratorFunction")), e.prototype = Object.create(u), e; } return GeneratorFunction.prototype = GeneratorFunctionPrototype, _regeneratorDefine2(u, "constructor", GeneratorFunctionPrototype), _regeneratorDefine2(GeneratorFunctionPrototype, "constructor", GeneratorFunction), GeneratorFunction.displayName = "GeneratorFunction", _regeneratorDefine2(GeneratorFunctionPrototype, o, "GeneratorFunction"), _regeneratorDefine2(u), _regeneratorDefine2(u, o, "Generator"), _regeneratorDefine2(u, n, function () { return this; }), _regeneratorDefine2(u, "toString", function () { return "[object Generator]"; }), (_regenerator = function _regenerator() { return { w: i, m: f }; })(); }
function _regeneratorDefine2(e, r, n, t) { var i = Object.defineProperty; try { i({}, "", {}); } catch (e) { i = 0; } _regeneratorDefine2 = function _regeneratorDefine(e, r, n, t) { if (r) i ? i(e, r, { value: n, enumerable: !t, configurable: !t, writable: !t }) : e[r] = n;else { var o = function o(r, n) { _regeneratorDefine2(e, r, function (e) { return this._invoke(r, n, e); }); }; o("next", 0), o("throw", 1), o("return", 2); } }, _regeneratorDefine2(e, r, n, t); }
function asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }
function _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, "next", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, "throw", n); } _next(void 0); }); }; }
function _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError("Cannot call a class as a function"); }
function _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, "value" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }
function _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, "prototype", { writable: !1 }), e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
/**
 * Pronunciation Service
 * Handles pronunciation grading and related API calls
 */

var PronunciationService = /*#__PURE__*/function () {
  function PronunciationService() {
    _classCallCheck(this, PronunciationService);
    this.ajaxService = _AjaxService_js__WEBPACK_IMPORTED_MODULE_0__["default"];
  }

  /**
   * Submit audio recording for pronunciation grading
   * @param {Blob} audioBlob - The recorded audio blob
   * @param {Object} params - Practice parameters
   * @returns {Promise<Object>} - Grading results
   */
  return _createClass(PronunciationService, [{
    key: "gradeRecording",
    value: (function () {
      var _gradeRecording = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee(audioBlob) {
        var params,
          data,
          _args = arguments,
          _t;
        return _regenerator().w(function (_context) {
          while (1) switch (_context.p = _context.n) {
            case 0:
              params = _args.length > 1 && _args[1] !== undefined ? _args[1] : {};
              _context.p = 1;
              // Create data object with audio file and parameters
              data = {
                audio: audioBlob,
                id: params.id || ''
              }; // Use AjaxService post method which handles FormData automatically
              _context.n = 2;
              return this.ajaxService.post('grade_pronunciation', data);
            case 2:
              return _context.a(2, _context.v);
            case 3:
              _context.p = 3;
              _t = _context.v;
              console.error('Error grading recording:', _t);
              throw _t;
            case 4:
              return _context.a(2);
          }
        }, _callee, this, [[1, 3]]);
      }));
      function gradeRecording(_x) {
        return _gradeRecording.apply(this, arguments);
      }
      return gradeRecording;
    }()
    /**
     * Get pronunciation topics
     * @returns {Promise<Array>} - List of pronunciation topics
     */
    )
  }, {
    key: "getTopics",
    value: (function () {
      var _getTopics = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee2() {
        var _t2;
        return _regenerator().w(function (_context2) {
          while (1) switch (_context2.p = _context2.n) {
            case 0:
              _context2.p = 0;
              _context2.n = 1;
              return this.ajaxService.post('get_pronunciation_topics');
            case 1:
              return _context2.a(2, _context2.v);
            case 2:
              _context2.p = 2;
              _t2 = _context2.v;
              console.error('Error fetching topics:', _t2);
              throw _t2;
            case 3:
              return _context2.a(2);
          }
        }, _callee2, this, [[0, 2]]);
      }));
      function getTopics() {
        return _getTopics.apply(this, arguments);
      }
      return getTopics;
    }()
    /**
     * Get topic items (questions and vocabulary)
     * @param {number} topicId - Topic ID
     * @returns {Promise<Array>} - List of topic items
     */
    )
  }, {
    key: "getTopicItems",
    value: (function () {
      var _getTopicItems = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee3(topicId) {
        var _t3;
        return _regenerator().w(function (_context3) {
          while (1) switch (_context3.p = _context3.n) {
            case 0:
              _context3.p = 0;
              _context3.n = 1;
              return this.ajaxService.post('get_topic_items', {
                topic_id: topicId
              });
            case 1:
              return _context3.a(2, _context3.v);
            case 2:
              _context3.p = 2;
              _t3 = _context3.v;
              console.error('Error fetching topic items:', _t3);
              throw _t3;
            case 3:
              return _context3.a(2);
          }
        }, _callee3, this, [[0, 2]]);
      }));
      function getTopicItems(_x2) {
        return _getTopicItems.apply(this, arguments);
      }
      return getTopicItems;
    }()
    /**
     * Save pronunciation practice result
     * @param {Object} resultData - Practice result data
     * @returns {Promise<Object>} - Save result
     */
    )
  }, {
    key: "saveResult",
    value: (function () {
      var _saveResult = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee4(resultData) {
        var _t4;
        return _regenerator().w(function (_context4) {
          while (1) switch (_context4.p = _context4.n) {
            case 0:
              _context4.p = 0;
              _context4.n = 1;
              return this.ajaxService.post('save_pronunciation_result', {
                result_data: JSON.stringify(resultData)
              });
            case 1:
              return _context4.a(2, _context4.v);
            case 2:
              _context4.p = 2;
              _t4 = _context4.v;
              console.error('Error saving result:', _t4);
              throw _t4;
            case 3:
              return _context4.a(2);
          }
        }, _callee4, this, [[0, 2]]);
      }));
      function saveResult(_x3) {
        return _saveResult.apply(this, arguments);
      }
      return saveResult;
    }()
    /**
     * Get user's pronunciation history
     * @param {Object} filters - Filter options
     * @returns {Promise<Array>} - List of practice results
     */
    )
  }, {
    key: "getHistory",
    value: (function () {
      var _getHistory = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee5() {
        var filters,
          data,
          _args5 = arguments,
          _t5;
        return _regenerator().w(function (_context5) {
          while (1) switch (_context5.p = _context5.n) {
            case 0:
              filters = _args5.length > 0 && _args5[0] !== undefined ? _args5[0] : {};
              _context5.p = 1;
              data = {}; // Add filters if provided
              if (filters.topic_id) {
                data.topic_id = filters.topic_id;
              }
              if (filters.limit) {
                data.limit = filters.limit;
              }
              if (filters.offset) {
                data.offset = filters.offset;
              }
              _context5.n = 2;
              return this.ajaxService.post('get_pronunciation_history', data);
            case 2:
              return _context5.a(2, _context5.v);
            case 3:
              _context5.p = 3;
              _t5 = _context5.v;
              console.error('Error fetching history:', _t5);
              throw _t5;
            case 4:
              return _context5.a(2);
          }
        }, _callee5, this, [[1, 3]]);
      }));
      function getHistory() {
        return _getHistory.apply(this, arguments);
      }
      return getHistory;
    }()
    /**
     * Get suggestions for pronunciation practice
     * @returns {Promise<Array>} - List of suggestions
     */
    )
  }, {
    key: "getSuggestions",
    value: (function () {
      var _getSuggestions = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee6(_ref) {
        var prompt, subject, data, _t6;
        return _regenerator().w(function (_context6) {
          while (1) switch (_context6.p = _context6.n) {
            case 0:
              prompt = _ref.prompt, subject = _ref.subject;
              _context6.p = 1;
              data = {
                "prompt": prompt,
                "subject": subject
              };
              _context6.n = 2;
              return this.ajaxService.post('get_pronunciation_suggestions', data);
            case 2:
              return _context6.a(2, _context6.v);
            case 3:
              _context6.p = 3;
              _t6 = _context6.v;
              console.error('Error fetching suggestions:', _t6);
              throw _t6;
            case 4:
              return _context6.a(2);
          }
        }, _callee6, this, [[1, 3]]);
      }));
      function getSuggestions(_x4) {
        return _getSuggestions.apply(this, arguments);
      }
      return getSuggestions;
    }())
  }]);
}(); // Export for use in other modules
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PronunciationService);

/***/ }),

/***/ "./assets/js/frontend/src/services/RestService.js":
/*!********************************************************!*\
  !*** ./assets/js/frontend/src/services/RestService.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   RestService: () => (/* binding */ RestService),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }
function _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError("Cannot call a class as a function"); }
function _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, "value" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }
function _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, "prototype", { writable: !1 }), e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
/**
 * TOEIC Practice Plugin - REST Service
 * 
 * A service class that provides a secure interface for making REST API requests
 * to WordPress backend, handling nonces and other security aspects.
 */
var RestService = /*#__PURE__*/function () {
  /**
   * Constructor
   * 
   * @param {Object} options - Configuration options
   * @param {string} options.restUrl - WordPress REST API URL (default: from global toeicPracticeRest)
   * @param {string} options.nonce - Security nonce (default: from global toeicPracticeRest)
   * @param {Function} options.onError - Global error handler (optional)
   */
  function RestService() {
    var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
    _classCallCheck(this, RestService);
    // Check if global toeicPracticeRest is available
    var globalData = window.toeicPracticeRest || {};
    this.restUrl = options.restUrl || globalData.restUrl || '/wp-json/toeic-practice/v1';
    this.nonce = options.nonce || globalData.restNonce || '';
    this.onError = options.onError || null;

    // Validate required parameters
    if (!this.restUrl) {
      console.error('RestService: restUrl is required');
    }
    if (!this.nonce) {
      console.warn('RestService: nonce is not provided, requests may fail security checks');
    }

    // Keep track of pending requests
    this.pendingRequests = new Map();
  }

  /**
   * Make a GET request
   * 
   * @param {string} endpoint - REST API endpoint
   * @param {Object} params - Query parameters (optional)
   * @param {Object} options - Request options (optional)
   * @returns {Promise} - Promise that resolves with the response data
   */
  return _createClass(RestService, [{
    key: "get",
    value: function get(endpoint) {
      var params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
      var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
      // Build query string
      var queryString = Object.keys(params).length ? '?' + new URLSearchParams(params).toString() : '';
      var url = "".concat(this.restUrl).concat(endpoint).concat(queryString);
      return this._makeRequest(url, _objectSpread({
        method: 'GET'
      }, options));
    }

    /**
     * Make a POST request
     * 
     * @param {string} endpoint - REST API endpoint
     * @param {Object} data - Data to send (optional)
     * @param {Object} options - Request options (optional)
     * @returns {Promise} - Promise that resolves with the response data
     */
  }, {
    key: "post",
    value: function post(endpoint) {
      var data = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
      var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
      var url = "".concat(this.restUrl).concat(endpoint);
      return this._makeRequest(url, _objectSpread({
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
      }, options));
    }

    /**
     * Make a request with the fetch API
     * 
     * @param {string} url - Request URL
     * @param {Object} options - Request options
     * @returns {Promise} - Promise that resolves with the response data
     * @private
     */
  }, {
    key: "_makeRequest",
    value: function _makeRequest(url, options) {
      var _this = this;
      // Generate a unique request ID
      var requestId = Math.random().toString(36).substring(2, 15);

      // Create abort controller for timeout
      var controller = new AbortController();
      var signal = controller.signal;

      // Set timeout if specified
      var timeout = options.timeout || 30000; // Default 30 seconds
      var timeoutId = setTimeout(function () {
        controller.abort();
      }, timeout);

      // Store the request in pending requests
      this.pendingRequests.set(requestId, {
        controller: controller
      });

      // Show loader if specified
      var loader = options.loader || window.toeicLoader || null;
      var showLoader = options.showLoader !== false;
      if (loader && showLoader) {
        loader.show(options.loaderText || 'Loading...');
      }

      // Add nonce to headers
      var headers = options.headers || {};
      headers['X-WP-Nonce'] = this.nonce; // Use the REST API nonce

      // Make the request
      return fetch(url, _objectSpread(_objectSpread({}, options), {}, {
        headers: headers,
        signal: signal,
        credentials: 'same-origin'
      })).then(function (response) {
        // Clear timeout
        clearTimeout(timeoutId);

        // Check if response is OK
        if (!response.ok) {
          throw new Error("HTTP error ".concat(response.status, ": ").concat(response.statusText));
        }

        // Parse response based on content type
        var contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
          return response.json();
        }
        return response.text();
      })["catch"](function (error) {
        // Handle aborted requests
        if (error.name === 'AbortError') {
          throw new Error('Request timed out');
        }

        // Call global error handler if available
        if (_this.onError) {
          _this.onError(error);
        }
        throw error;
      })["finally"](function () {
        // Remove from pending requests
        _this.pendingRequests["delete"](requestId);

        // Hide loader
        if (loader && showLoader) {
          // Only hide if no other requests are pending with this loader
          var otherRequestsWithLoader = Array.from(_this.pendingRequests.values()).some(function (req) {
            return req.loader === loader;
          });
          if (!otherRequestsWithLoader) {
            loader.hide();
          }
        }
      });
    }

    /**
     * Cancel all pending requests
     */
  }, {
    key: "cancelAll",
    value: function cancelAll() {
      this.pendingRequests.forEach(function (request) {
        request.controller.abort();
      });
      this.pendingRequests.clear();
    }

    /**
     * Update the nonce
     * 
     * @param {string} nonce - New nonce value
     */
  }, {
    key: "updateNonce",
    value: function updateNonce(nonce) {
      this.nonce = nonce;
    }

    /**
     * Create a new instance with custom settings
     * 
     * @param {Object} options - Configuration options
     * @returns {RestService} - New RestService instance
     */
  }], [{
    key: "create",
    value: function create() {
      var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
      return new RestService(options);
    }
  }]);
}(); // Create and export default instance
var restService = new RestService();
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (restService);

// Also export the class for creating custom instances


/***/ }),

/***/ "./assets/js/frontend/src/services/TestService.js":
/*!********************************************************!*\
  !*** ./assets/js/frontend/src/services/TestService.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   TestService: () => (/* binding */ TestService),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _RestService__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./RestService */ "./assets/js/frontend/src/services/RestService.js");
function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError("Cannot call a class as a function"); }
function _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, "value" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }
function _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, "prototype", { writable: !1 }), e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
/**
 * Test Service
 * 
 * Handles all test-related REST API operations.
 * Uses the RestService for making requests to the server.
 */


var TestService = /*#__PURE__*/function () {
  function TestService() {
    _classCallCheck(this, TestService);
  }
  return _createClass(TestService, [{
    key: "getTestDetails",
    value:
    /**
     * Fetch test details by ID
     * 
     * @param {number} testId - The ID of the test to fetch
     * @returns {Promise} - Promise that resolves with test details
     */
    function getTestDetails(testId) {
      return _RestService__WEBPACK_IMPORTED_MODULE_0__["default"].get("/tests/".concat(testId));
    }

    /**
     * Fetch test questions by test ID
     * 
     * @param {number} testId - The ID of the test to fetch questions for
     * @returns {Promise} - Promise that resolves with test questions
     */
  }, {
    key: "getTestQuestions",
    value: function getTestQuestions(testId) {
      return _RestService__WEBPACK_IMPORTED_MODULE_0__["default"].get("/tests/".concat(testId, "/questions"));
    }

    /**
     * Submit a completed test
     * 
     * @param {number} testId - The ID of the test being submitted
     * @param {Object} answers - Object containing question IDs as keys and selected answers as values
     * @param {number} timeTaken - Time taken to complete the test in seconds
     * @returns {Promise} - Promise that resolves with test results
     */
  }, {
    key: "submitTest",
    value: function submitTest(testId, answers, timeTaken) {
      return _RestService__WEBPACK_IMPORTED_MODULE_0__["default"].post("/tests/".concat(testId, "/submit"), {
        answers: answers,
        time_taken: timeTaken
      });
    }

    /**
     * Get all available tests
     * 
     * @param {Object} filters - Optional filters for tests (e.g., category, difficulty)
     * @returns {Promise} - Promise that resolves with list of available tests
     */
  }, {
    key: "getAvailableTests",
    value: function getAvailableTests() {
      var filters = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
      return _RestService__WEBPACK_IMPORTED_MODULE_0__["default"].get('/tests', filters);
    }

    /**
     * Get test results history for current user
     * 
     * @param {number} limit - Optional limit for number of results to return
     * @param {number} page - Optional page number for pagination
     * @returns {Promise} - Promise that resolves with test results history
     */
  }, {
    key: "getTestResultsHistory",
    value: function getTestResultsHistory() {
      var limit = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 10;
      var page = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 1;
      return _RestService__WEBPACK_IMPORTED_MODULE_0__["default"].get('/results', {
        limit: limit,
        page: page
      });
    }

    /**
     * Get detailed test result by result ID
     * 
     * @param {number} resultId - The ID of the test result to fetch
     * @returns {Promise} - Promise that resolves with detailed test result
     */
  }, {
    key: "getTestResult",
    value: function getTestResult(resultId) {
      return _RestService__WEBPACK_IMPORTED_MODULE_0__["default"].get("/results/".concat(resultId));
    }

    /**
     * Save user progress on a test (for resuming later)
     * 
     * @param {number} testId - The ID of the test
     * @param {Object} progress - Progress data including answers and current position
     * @returns {Promise} - Promise that resolves with save status
     */
  }, {
    key: "saveTestProgress",
    value: function saveTestProgress(testId, progress) {
      return _RestService__WEBPACK_IMPORTED_MODULE_0__["default"].post("/tests/".concat(testId, "/progress"), {
        progress: progress,
        current_question: progress.currentQuestion || 0
      });
    }

    /**
     * Load saved test progress
     * 
     * @param {number} testId - The ID of the test to load progress for
     * @returns {Promise} - Promise that resolves with saved progress data
     */
  }, {
    key: "loadTestProgress",
    value: function loadTestProgress(testId) {
      return _RestService__WEBPACK_IMPORTED_MODULE_0__["default"].get("/tests/".concat(testId, "/progress"));
    }

    /**
     * Get test statistics for a specific test
     * 
     * @param {number} testId - The ID of the test
     * @returns {Promise} - Promise that resolves with test statistics
     */
  }, {
    key: "getTestStatistics",
    value: function getTestStatistics(testId) {
      return _RestService__WEBPACK_IMPORTED_MODULE_0__["default"].get("/tests/".concat(testId, "/statistics"));
    }

    /**
     * Get section details by section ID
     * 
     * @param {number} sectionId - The ID of the section to fetch
     * @returns {Promise} - Promise that resolves with section details
     */
  }, {
    key: "getSectionDetails",
    value: function getSectionDetails(sectionId) {
      // This method can be implemented when needed
      return Promise.resolve({});
    }
  }]);
}(); // Create and export a singleton instance
var testService = new TestService();
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (testService);

// Also export the class for potential extension


/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
var __webpack_exports__ = {};
// This entry needs to be wrapped in an IIFE because it needs to be isolated against other modules in the chunk.
(() => {
/*!*****************************************!*\
  !*** ./assets/js/frontend/src/index.js ***!
  \*****************************************/
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AjaxService: () => (/* reexport safe */ _services_AjaxService__WEBPACK_IMPORTED_MODULE_2__.AjaxService),
/* harmony export */   Loader: () => (/* reexport safe */ _components_Loader__WEBPACK_IMPORTED_MODULE_1__["default"]),
/* harmony export */   Sidebar: () => (/* reexport safe */ _components_Sidebar__WEBPACK_IMPORTED_MODULE_0__["default"]),
/* harmony export */   TestDetailPage: () => (/* reexport safe */ _pages_test_detail_page__WEBPACK_IMPORTED_MODULE_4__["default"]),
/* harmony export */   TestService: () => (/* reexport safe */ _services_TestService__WEBPACK_IMPORTED_MODULE_3__.TestService),
/* harmony export */   ajaxService: () => (/* reexport safe */ _services_AjaxService__WEBPACK_IMPORTED_MODULE_2__["default"]),
/* harmony export */   testService: () => (/* reexport safe */ _services_TestService__WEBPACK_IMPORTED_MODULE_3__["default"])
/* harmony export */ });
/* harmony import */ var _components_Sidebar__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./components/Sidebar */ "./assets/js/frontend/src/components/Sidebar.js");
/* harmony import */ var _components_Loader__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./components/Loader */ "./assets/js/frontend/src/components/Loader.js");
/* harmony import */ var _services_AjaxService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./services/AjaxService */ "./assets/js/frontend/src/services/AjaxService.js");
/* harmony import */ var _services_TestService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./services/TestService */ "./assets/js/frontend/src/services/TestService.js");
/* harmony import */ var _pages_test_detail_page__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./pages/test-detail-page */ "./assets/js/frontend/src/pages/test-detail-page.js");
/* harmony import */ var _pages_pronunciation_page__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages/pronunciation-page */ "./assets/js/frontend/src/pages/pronunciation-page.js");
/**
 * TOEIC Practice Plugin - Frontend Main Entry Point
 * 
 * This file serves as the main entry point for the frontend JavaScript
 * functionality of the TOEIC Practice plugin.
 */

// Import components







// Initialize the application when DOM is fully loaded
document.addEventListener('DOMContentLoaded', function () {
  // Global loader instance for full-screen loading
  window.toeicLoader = new _components_Loader__WEBPACK_IMPORTED_MODULE_1__["default"]({
    fullScreen: true
  });

  // Make services available globally
  window.toeicAjax = _services_AjaxService__WEBPACK_IMPORTED_MODULE_2__["default"];
  window.toeicTest = _services_TestService__WEBPACK_IMPORTED_MODULE_3__["default"];

  // check if record button exists
  var recordBtn = document.getElementById('record-btn');
  if (recordBtn) {
    window.toeicPronunciation = new _pages_pronunciation_page__WEBPACK_IMPORTED_MODULE_5__.PronunciationRecorder({
      paragraphText: recordBtn.dataset.paragraphText
    });
  }

  // Initialize sidebar
  var sidebar = document.querySelector('.toeic-sidebar');
  if (sidebar) {
    var _sidebar = new _components_Sidebar__WEBPACK_IMPORTED_MODULE_0__["default"](_sidebar);
    _sidebar.init();
  }
});

// Export components for potential reuse

})();

__webpack_exports__ = __webpack_exports__["default"];
/******/ 	return __webpack_exports__;
/******/ })()
;
});
//# sourceMappingURL=index.js.map