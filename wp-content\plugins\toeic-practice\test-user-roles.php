<?php
/**
 * Test script for TOEIC Practice User Roles
 * 
 * This script can be used to test if the user roles are working correctly.
 * Run this script from WordPress admin or via WP-CLI to verify the implementation.
 * 
 * @package ToeicPractice
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit('Direct access not allowed.');
}

/**
 * Test TOEIC Practice User Roles
 */
function test_toeic_user_roles() {
    echo "<h2>TOEIC Practice User Roles Test</h2>\n";
    
    // Test if roles exist
    echo "<h3>1. Testing Role Existence</h3>\n";
    
    $student_role = get_role('student');
    $instructor_role = get_role('instructor');
    $admin_role = get_role('administrator');
    
    if ($student_role) {
        echo "✅ Student role exists<br>\n";
    } else {
        echo "❌ Student role does not exist<br>\n";
    }
    
    if ($instructor_role) {
        echo "✅ Instructor role exists<br>\n";
    } else {
        echo "❌ Instructor role does not exist<br>\n";
    }
    
    // Test student capabilities
    echo "<h3>2. Testing Student Capabilities</h3>\n";
    if ($student_role) {
        $student_caps = [
            'read',
            'take_toeic_tests',
            'view_test_results',
            'enroll_in_classes',
            'view_class_materials',
            'submit_assignments',
            'view_own_progress'
        ];
        
        foreach ($student_caps as $cap) {
            if ($student_role->has_cap($cap)) {
                echo "✅ Student has capability: {$cap}<br>\n";
            } else {
                echo "❌ Student missing capability: {$cap}<br>\n";
            }
        }
        
        // Test that students don't have admin capabilities
        $forbidden_caps = ['edit_posts', 'delete_posts', 'manage_options'];
        foreach ($forbidden_caps as $cap) {
            if (!$student_role->has_cap($cap)) {
                echo "✅ Student correctly lacks capability: {$cap}<br>\n";
            } else {
                echo "❌ Student incorrectly has capability: {$cap}<br>\n";
            }
        }
    }
    
    // Test instructor capabilities
    echo "<h3>3. Testing Instructor Capabilities</h3>\n";
    if ($instructor_role) {
        $instructor_caps = [
            'read',
            'edit_posts',
            'delete_posts',
            'publish_posts',
            'upload_files',
            'create_toeic_tests',
            'edit_toeic_tests',
            'delete_toeic_tests',
            'manage_classes',
            'create_classes',
            'edit_classes',
            'delete_classes',
            'view_student_progress',
            'grade_assignments',
            'manage_enrollments',
            'view_class_analytics',
            'export_student_data'
        ];
        
        foreach ($instructor_caps as $cap) {
            if ($instructor_role->has_cap($cap)) {
                echo "✅ Instructor has capability: {$cap}<br>\n";
            } else {
                echo "❌ Instructor missing capability: {$cap}<br>\n";
            }
        }
        
        // Test that instructors don't have admin capabilities
        $forbidden_caps = ['manage_options', 'edit_others_posts', 'delete_others_posts'];
        foreach ($forbidden_caps as $cap) {
            if (!$instructor_role->has_cap($cap)) {
                echo "✅ Instructor correctly lacks capability: {$cap}<br>\n";
            } else {
                echo "❌ Instructor incorrectly has capability: {$cap}<br>\n";
            }
        }
    }
    
    // Test admin capabilities
    echo "<h3>4. Testing Administrator Capabilities</h3>\n";
    if ($admin_role) {
        $toeic_caps = [
            'take_toeic_tests',
            'view_test_results',
            'enroll_in_classes',
            'view_class_materials',
            'submit_assignments',
            'view_own_progress',
            'create_toeic_tests',
            'edit_toeic_tests',
            'delete_toeic_tests',
            'manage_classes',
            'create_classes',
            'edit_classes',
            'delete_classes',
            'view_student_progress',
            'grade_assignments',
            'manage_enrollments',
            'view_class_analytics',
            'export_student_data'
        ];
        
        foreach ($toeic_caps as $cap) {
            if ($admin_role->has_cap($cap)) {
                echo "✅ Administrator has TOEIC capability: {$cap}<br>\n";
            } else {
                echo "❌ Administrator missing TOEIC capability: {$cap}<br>\n";
            }
        }
    }
    
    // Test database tables
    echo "<h3>5. Testing Database Tables</h3>\n";
    global $wpdb;
    
    $class_table = $wpdb->prefix . 'class';
    $enrollment_table = $wpdb->prefix . 'class_enrollment';
    
    $class_exists = $wpdb->get_var("SHOW TABLES LIKE '{$class_table}'") == $class_table;
    $enrollment_exists = $wpdb->get_var("SHOW TABLES LIKE '{$enrollment_table}'") == $enrollment_table;
    
    if ($class_exists) {
        echo "✅ Class table exists: {$class_table}<br>\n";
    } else {
        echo "❌ Class table does not exist: {$class_table}<br>\n";
    }
    
    if ($enrollment_exists) {
        echo "✅ Enrollment table exists: {$enrollment_table}<br>\n";
    } else {
        echo "❌ Enrollment table does not exist: {$enrollment_table}<br>\n";
    }
    
    echo "<h3>Test Complete</h3>\n";
}

// Run the test if accessed directly (for debugging)
if (isset($_GET['test_toeic_roles']) && current_user_can('manage_options')) {
    test_toeic_user_roles();
}
