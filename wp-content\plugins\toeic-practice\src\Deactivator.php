<?php
/**
 * Plugin Deactivator
 *
 * @package ToeicPractice
 */

namespace ToeicPractice;

/**
 * Plugin Deactivator Class
 * 
 * Handles plugin deactivation tasks such as clearing scheduled events
 * and flushing rewrite rules.
 */
class Deactivator {
    
    /**
     * Deactivate the plugin
     * 
     * This method is called when the plugin is deactivated.
     * It performs cleanup tasks that should happen on deactivation.
     */
    public static function deactivate() {
        // Clear scheduled events
        self::clearScheduledEvents();
        
        // Flush rewrite rules
        self::flushRewriteRules();
        
        // Clear any cached data
        self::clearCache();

        // Remove custom user roles
        self::removeUserRoles();

        // Set deactivation timestamp
        update_option('toeic_practice_deactivated_time', current_time('timestamp'));
    }
    
    /**
     * Clear scheduled events
     */
    private static function clearScheduledEvents() {
        // Clear any wp-cron events that the plugin may have scheduled
        $scheduled_events = [
            'toeic_practice_cleanup_temp_files',
            'toeic_practice_update_statistics',
            'toeic_practice_send_progress_reports'
        ];
        
        foreach ($scheduled_events as $event) {
            $timestamp = wp_next_scheduled($event);
            if ($timestamp) {
                wp_unschedule_event($timestamp, $event);
            }
        }
    }
    
    /**
     * Flush rewrite rules
     */
    private static function flushRewriteRules() {
        flush_rewrite_rules();
    }

    /**
     * Remove custom user roles
     */
    private static function removeUserRoles() {
        // Remove custom user roles
        remove_role('student');
        remove_role('instructor');

        // Remove custom capabilities from administrator role
        $admin_role = get_role('administrator');
        if ($admin_role) {
            // Student capabilities
            $admin_role->remove_cap('take_toeic_tests');
            $admin_role->remove_cap('view_test_results');
            $admin_role->remove_cap('enroll_in_classes');
            $admin_role->remove_cap('view_class_materials');
            $admin_role->remove_cap('submit_assignments');
            $admin_role->remove_cap('view_own_progress');

            // Instructor capabilities
            $admin_role->remove_cap('create_toeic_tests');
            $admin_role->remove_cap('edit_toeic_tests');
            $admin_role->remove_cap('delete_toeic_tests');
            $admin_role->remove_cap('manage_classes');
            $admin_role->remove_cap('create_classes');
            $admin_role->remove_cap('edit_classes');
            $admin_role->remove_cap('delete_classes');
            $admin_role->remove_cap('view_student_progress');
            $admin_role->remove_cap('grade_assignments');
            $admin_role->remove_cap('manage_enrollments');
            $admin_role->remove_cap('view_class_analytics');
            $admin_role->remove_cap('export_student_data');
        }
    }

    /**
     * Clear cached data
     */
    private static function clearCache() {
        // Clear any transients used by the plugin
        $transients = [
            'toeic_practice_statistics',
            'toeic_practice_leaderboard',
            'toeic_practice_user_progress'
        ];
        
        foreach ($transients as $transient) {
            delete_transient($transient);
        }
        
        // Clear object cache if available
        if (function_exists('wp_cache_flush')) {
            wp_cache_flush();
        }
    }
}
