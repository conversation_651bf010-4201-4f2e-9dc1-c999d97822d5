/**
 * TOEIC Practice Admin JavaScript
 */

jQuery(document).ready(function($) {

    // Handle role parameter in user-new.php URLs
    function handleUserNewRoleParameter() {
        // Check if we're on the user-new.php page
        if (window.location.pathname.indexOf('user-new.php') !== -1) {
            // Get the role parameter from URL
            const urlParams = new URLSearchParams(window.location.search);
            const role = urlParams.get('role');

            if (role && (role === 'student' || role === 'instructor')) {
                // Set the role dropdown to the specified role
                const roleSelect = $('#role');
                if (roleSelect.length) {
                    roleSelect.val(role);
                }
            }
        }
    }

    // Initialize role parameter handling
    handleUserNewRoleParameter();

    // Handle bulk actions confirmation
    $('.wp-list-table').on('submit', function(e) {
        const action = $(this).find('select[name="action"]').val();

        if (action === 'bulk_delete') {
            const checkedItems = $(this).find('input[type="checkbox"]:checked').not('#cb-select-all-1');

            if (checkedItems.length === 0) {
                e.preventDefault();
                alert('Please select items to delete.');
                return false;
            }

            const confirmMessage = checkedItems.length === 1
                ? 'Are you sure you want to delete this item?'
                : 'Are you sure you want to delete these ' + checkedItems.length + ' items?';

            if (!confirm(confirmMessage)) {
                e.preventDefault();
                return false;
            }
        }
    });

    // Handle select all checkbox
    $('#cb-select-all-1').on('change', function() {
        const isChecked = $(this).is(':checked');
        $(this).closest('table').find('tbody input[type="checkbox"]').prop('checked', isChecked);
    });

    // Update select all checkbox when individual checkboxes change
    $('.wp-list-table tbody').on('change', 'input[type="checkbox"]', function() {
        const table = $(this).closest('table');
        const totalCheckboxes = table.find('tbody input[type="checkbox"]').length;
        const checkedCheckboxes = table.find('tbody input[type="checkbox"]:checked').length;

        const selectAllCheckbox = table.find('#cb-select-all-1');

        if (checkedCheckboxes === 0) {
            selectAllCheckbox.prop('indeterminate', false).prop('checked', false);
        } else if (checkedCheckboxes === totalCheckboxes) {
            selectAllCheckbox.prop('indeterminate', false).prop('checked', true);
        } else {
            selectAllCheckbox.prop('indeterminate', true);
        }
    });

});