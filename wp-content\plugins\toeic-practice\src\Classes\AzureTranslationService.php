<?php

namespace ToeicPractice\Classes;

/**
 * Azure Translation Service
 * 
 * Integrates with Azure Cognitive Services Translator API for text translation
 */
class AzureTranslationService {
    
    private $subscription_key;
    private $region;
    private $api_endpoint = 'https://api.cognitive.microsofttranslator.com/';
    
    /**
     * Constructor
     * 
     * @param string $subscription_key Azure subscription key
     * @param string $region Azure region (e.g., 'eastus')
     */
    public function __construct($subscription_key = null, $region = null) {
        $this->subscription_key = $subscription_key ?: get_option('toeic_practice_azure_translator_subscription_key', '');
        $this->region = $region ?: get_option('toeic_practice_azure_translator_service_region', 'eastasia');
    }
    
    /**
     * Check if Azure credentials are configured
     * 
     * @return bool
     */
    public function isConfigured() {
        return !empty($this->subscription_key) && !empty($this->region);
    }
    
    /**
     * Translate text using Azure Translator API
     * 
     * @param string $text Text to translate
     * @param string $to Target language code
     * @param string $from Source language code (optional, auto-detect if not provided)
     * @param array $options Additional options
     * @return array|false Translation result or false on failure
     */
    public function translateText($text, $to = 'en', $from = null, $options = []) {
        if (!$this->isConfigured()) {
            error_log('Azure Translation Service not configured');
            return false;
        }
        
        if (empty($text)) {
            error_log('No text provided for translation');
            return false;
        }
        
        // Build API URL for translation
        $api_url = $this->api_endpoint . 'translate';
        
        // Build query parameters
        $query_params = [
            'api-version' => '3.0',
            'to' => $to
        ];
        
        if ($from) {
            $query_params['from'] = $from;
        }
        
        $api_url .= '?' . http_build_query($query_params);
        
        // Prepare request body
        $request_body = [
            [
                'text' => $text
            ]
        ];
        
        // Prepare headers
        $headers = [
            'Ocp-Apim-Subscription-Key: ' . $this->subscription_key,
            'Ocp-Apim-Subscription-Region: ' . $this->region,
            'Content-Type: application/json',
            'Accept: application/json'
        ];
        
        // Initialize cURL
        $ch = curl_init();
        
        curl_setopt_array($ch, [
            CURLOPT_URL => $api_url,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($request_body),
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_SSL_VERIFYPEER => true,
            CURLOPT_SSL_VERIFYHOST => 2
        ]);
        
        // Set CA bundle path if available
        $ca_bundle_path = get_option('toeic_practice_azure_ca_bundle_path', '');
        if (!empty($ca_bundle_path) && file_exists($ca_bundle_path)) {
            curl_setopt($ch, CURLOPT_CAINFO, $ca_bundle_path);
        }
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curl_error = curl_error($ch);
        curl_close($ch);
        
        if ($curl_error) {
            error_log("cURL error in translation: " . $curl_error);
            return false;
        }
        
        if ($http_code !== 200) {
            error_log("HTTP error in translation: " . $http_code . " - " . $response);
            return false;
        }
        
        $result = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            error_log("JSON decode error in translation: " . json_last_error_msg());
            return false;
        }
        
        // Parse and return the translation result
        return $this->parseTranslationResult($result);
    }
    
    /**
     * Parse translation result from Azure Translator API
     * 
     * @param array $result Raw result from Azure API
     * @return array Parsed translation result
     */
    private function parseTranslationResult($result) {
        if (empty($result) || !isset($result[0]['translations'])) {
            return [
                'success' => false,
                'error' => 'Invalid response from translation service'
            ];
        }
        
        $translation_data = $result[0];
        $translations = $translation_data['translations'];
        
        $parsed_result = [
            'success' => true,
            'original_text' => $translation_data['text'] ?? '',
            'detected_language' => $translation_data['detectedLanguage']['language'] ?? null,
            'confidence' => $translation_data['detectedLanguage']['score'] ?? null,
            'translations' => []
        ];
        
        foreach ($translations as $translation) {
            $parsed_result['translations'][] = [
                'language' => $translation['to'],
                'text' => $translation['text']
            ];
        }
        
        return $parsed_result;
    }
    
    /**
     * Detect language of given text
     * 
     * @param string $text Text to detect language for
     * @return array|false Detection result or false on failure
     */
    public function detectLanguage($text) {
        if (!$this->isConfigured()) {
            error_log('Azure Translation Service not configured');
            return false;
        }
        
        if (empty($text)) {
            error_log('No text provided for language detection');
            return false;
        }
        
        // Build API URL for language detection
        $api_url = $this->api_endpoint . 'detect?api-version=3.0';
        
        // Prepare request body
        $request_body = [
            [
                'text' => $text
            ]
        ];
        
        // Prepare headers
        $headers = [
            'Ocp-Apim-Subscription-Key: ' . $this->subscription_key,
            'Ocp-Apim-Subscription-Region: ' . $this->region,
            'Content-Type: application/json',
            'Accept: application/json'
        ];
        
        // Initialize cURL
        $ch = curl_init();
        
        curl_setopt_array($ch, [
            CURLOPT_URL => $api_url,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($request_body),
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_SSL_VERIFYPEER => true,
            CURLOPT_SSL_VERIFYHOST => 2
        ]);
        
        // Set CA bundle path if available
        $ca_bundle_path = get_option('toeic_practice_azure_ca_bundle_path', '');
        if (!empty($ca_bundle_path) && file_exists($ca_bundle_path)) {
            curl_setopt($ch, CURLOPT_CAINFO, $ca_bundle_path);
        }
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curl_error = curl_error($ch);
        curl_close($ch);
        
        if ($curl_error) {
            error_log("cURL error in language detection: " . $curl_error);
            return false;
        }
        
        if ($http_code !== 200) {
            error_log("HTTP error in language detection: " . $http_code . " - " . $response);
            return false;
        }
        
        $result = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            error_log("JSON decode error in language detection: " . json_last_error_msg());
            return false;
        }
        
        return $this->parseDetectionResult($result);
    }
    
    /**
     * Parse language detection result from Azure Translator API
     * 
     * @param array $result Raw result from Azure API
     * @return array Parsed detection result
     */
    private function parseDetectionResult($result) {
        if (empty($result) || !isset($result[0])) {
            return [
                'success' => false,
                'error' => 'Invalid response from language detection service'
            ];
        }
        
        $detection_data = $result[0];
        
        return [
            'success' => true,
            'language' => $detection_data['language'],
            'confidence' => $detection_data['score'],
            'is_translation_supported' => $detection_data['isTranslationSupported'],
            'is_transliteration_supported' => $detection_data['isTransliterationSupported']
        ];
    }
    
    /**
     * Get supported languages for translation
     * 
     * @return array|false List of supported languages or false on failure
     */
    public function getSupportedLanguages() {
        if (!$this->isConfigured()) {
            error_log('Azure Translation Service not configured');
            return false;
        }
        
        // Build API URL for supported languages
        $api_url = $this->api_endpoint . 'languages?api-version=3.0&scope=translation';
        
        // Prepare headers
        $headers = [
            'Ocp-Apim-Subscription-Key: ' . $this->subscription_key,
            'Ocp-Apim-Subscription-Region: ' . $this->region,
            'Accept: application/json'
        ];
        
        // Initialize cURL
        $ch = curl_init();
        
        curl_setopt_array($ch, [
            CURLOPT_URL => $api_url,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_SSL_VERIFYPEER => true,
            CURLOPT_SSL_VERIFYHOST => 2
        ]);
        
        // Set CA bundle path if available
        $ca_bundle_path = get_option('toeic_practice_azure_ca_bundle_path', '');
        if (!empty($ca_bundle_path) && file_exists($ca_bundle_path)) {
            curl_setopt($ch, CURLOPT_CAINFO, $ca_bundle_path);
        }
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curl_error = curl_error($ch);
        curl_close($ch);
        
        if ($curl_error) {
            error_log("cURL error in getting supported languages: " . $curl_error);
            return false;
        }
        
        if ($http_code !== 200) {
            error_log("HTTP error in getting supported languages: " . $http_code . " - " . $response);
            return false;
        }
        
        $result = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            error_log("JSON decode error in getting supported languages: " . json_last_error_msg());
            return false;
        }
        
        return $result['translation'] ?? [];
    }
    
    /**
     * Test Azure Translator connection
     * 
     * @return bool True if connection successful, false otherwise
     */
    public function testConnection() {
        if (!$this->isConfigured()) {
            return false;
        }
        
        // Test with a simple translation
        $test_result = $this->translateText('Hello', 'es');
        return $test_result !== false && isset($test_result['success']) && $test_result['success'];
    }
    
    /**
     * Get mock translation result for testing
     * 
     * @param string $text Text to translate
     * @param string $to Target language
     * @param string $from Source language
     * @return array Mock translation result
     */
    public function getMockTranslation($text, $to = 'en', $from = null) {
        // Simple mock translations for common phrases
        $mock_translations = [
            'hello' => [
                'es' => 'hola',
                'fr' => 'bonjour',
                'de' => 'hallo',
                'ja' => 'こんにちは',
                'ko' => '안녕하세요',
                'zh' => '你好'
            ],
            'goodbye' => [
                'es' => 'adiós',
                'fr' => 'au revoir',
                'de' => 'auf wiedersehen',
                'ja' => 'さようなら',
                'ko' => '안녕히 가세요',
                'zh' => '再见'
            ],
            'thank you' => [
                'es' => 'gracias',
                'fr' => 'merci',
                'de' => 'danke',
                'ja' => 'ありがとう',
                'ko' => '감사합니다',
                'zh' => '谢谢'
            ]
        ];
        
        $text_lower = strtolower($text);
        $translated_text = $mock_translations[$text_lower][$to] ?? '[Mock Translation: ' . $text . ' -> ' . $to . ']';
        
        return [
            'success' => true,
            'original_text' => $text,
            'detected_language' => $from ?: 'en',
            'confidence' => 0.95,
            'translations' => [
                [
                    'language' => $to,
                    'text' => $translated_text
                ]
            ]
        ];
    }
}