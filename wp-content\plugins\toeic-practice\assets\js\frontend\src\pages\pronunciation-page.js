// pronunciation-page.js

import PronunciationService from '../services/PronunciationService';

class PronunciationRecorder {
    constructor({paragraphText}) {
        this.isRecording = false;
        this.mediaRecorder = null;
        this.audioChunks = [];
        this.startTime = null;
        this.timerInterval = null;
        this.paragraphText = paragraphText;
        
        // Initialize pronunciation service
        this.pronunciationService = new PronunciationService();
        
        // DOM elements
        this.recordBtn = document.getElementById('record-btn');
        this.recordingStatus = document.querySelector('.recording-status');
        this.recordingDuration = document.querySelector('.recording-duration');
        this.durationText = document.querySelector('.recording-duration-text');
        this.suggestionBtns = document.querySelectorAll('.suggestion-btn');
        
        this.init();
    }
    
    init() {
        // Check if required elements exist
        if (!this.recordBtn) {
            console.warn('Record button not found');
            return;
        }

        console.log('Record button found');
        
        // Initially hide recording status elements
        this.hideRecordingElements();
        
        // Bind event listeners
        this.recordBtn.addEventListener('click', () => this.toggleRecording());
        this.suggestionBtns.forEach(button => button.addEventListener('click', (e) => this.handleSuggestion(e)));
        
        // Check for microphone permissions
        this.checkMicrophonePermissions();
    }
    
    async checkMicrophonePermissions() {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
            stream.getTracks().forEach(track => track.stop());
            console.log('Microphone access granted');
        } catch (error) {
            console.error('Microphone access denied:', error);
            this.showError('Microphone access is required for recording. Please allow microphone permissions.');
        }
    }
    
    async toggleRecording() {
        if (this.isRecording) {
            this.stopRecording();
        } else {
            await this.startRecording();
        }
    }
    
    async startRecording() {
        try {
            // Get user media with optimized settings for speech (16kHz, mono)
            const stream = await navigator.mediaDevices.getUserMedia({ 
                audio: {
                    echoCancellation: true,
                    noiseSuppression: true,
                    sampleRate: 16000, // Reduced from 44100 to 16000 for speech
                    channelCount: 1     // Mono channel for speech
                }
            });
            
            // Create MediaRecorder with fallback MIME types
            let mimeType = 'audio/webm;codecs=opus';
            if (!MediaRecorder.isTypeSupported(mimeType)) {
                mimeType = 'audio/webm';
                if (!MediaRecorder.isTypeSupported(mimeType)) {
                    mimeType = 'audio/mp4';
                    if (!MediaRecorder.isTypeSupported(mimeType)) {
                        mimeType = ''; // Let browser choose
                    }
                }
            }
            
            this.mediaRecorder = new MediaRecorder(stream, {
                mimeType: mimeType
            });
            
            // Store the actual MIME type used
            this.actualMimeType = mimeType;
            
            // Reset audio chunks
            this.audioChunks = [];
            
            // Set up event handlers
            this.mediaRecorder.ondataavailable = (event) => {
                if (event.data.size > 0) {
                    this.audioChunks.push(event.data);
                }
            };
            
            this.mediaRecorder.onstop = () => {
                this.processRecording();
            };
            
            // Start recording
            this.mediaRecorder.start();
            this.isRecording = true;
            this.startTime = Date.now();
            
            // Update UI
            this.showRecordingElements();
            this.updateButtonState();
            this.startTimer();
        } catch (error) {
            console.error('Error starting recording:', error);
            this.showError('Failed to start recording. Please check your microphone permissions.');
        }
    }
    
    stopRecording() {
        if (this.mediaRecorder && this.isRecording) {
            this.mediaRecorder.stop();
            this.isRecording = false;
            
            // Stop all tracks
            if (this.mediaRecorder.stream) {
                this.mediaRecorder.stream.getTracks().forEach(track => track.stop());
            }
            
            // Update UI
            this.hideRecordingElements();
            this.updateButtonState();
            this.stopTimer();
            
            console.log('Recording stopped');
        }
    }
    
    processRecording() {
        if (this.audioChunks.length === 0) {
            this.showError('No audio data recorded');
            return;
        }
        
        // Create blob with the correct MIME type that was actually recorded
        const audioBlob = new Blob(this.audioChunks, { 
            type: this.actualMimeType || 'audio/webm' 
        });

        // Convert to WAV if needed for better compatibility
        this.convertToWAV(audioBlob).then(wavBlob => {
            this.submitForGrading(wavBlob);
        }).catch(error => {
            console.warn('WAV conversion failed, using original format:', error);
            // Fallback to original blob if conversion fails
            this.submitForGrading(audioBlob);
        });
    }
    
    async convertToWAV(audioBlob) {
        return new Promise((resolve, reject) => {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const fileReader = new FileReader();
            
            fileReader.onload = async (e) => {
                try {
                    // Decode the audio data
                    const arrayBuffer = e.target.result;
                    const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);
                    
                    // Trim silence from the audio buffer
                    const trimmedBuffer = this.trimSilence(audioBuffer, audioContext);
                    
                    // Convert to WAV
                    const wavBlob = this.audioBufferToWav(trimmedBuffer);
                    resolve(wavBlob);
                } catch (error) {
                    reject(error);
                }
            };
            
            fileReader.onerror = () => reject(new Error('Failed to read audio file'));
            fileReader.readAsArrayBuffer(audioBlob);
        });
    }
    
    /**
     * Trim silence from the beginning and end of an audio buffer
     * @param {AudioBuffer} audioBuffer - The original audio buffer
     * @param {AudioContext} audioContext - The audio context to use for creating new buffers
     * @param {number} silenceThreshold - Threshold for silence detection (0-1, default: 0.01)
     * @param {number} marginMs - Margin to keep before/after speech in milliseconds (default: 100ms)
     * @returns {AudioBuffer} - Trimmed audio buffer
     */
    trimSilence(audioBuffer, audioContext, silenceThreshold = 0.01, marginMs = 100) {
        const sampleRate = audioBuffer.sampleRate;
        const numberOfChannels = audioBuffer.numberOfChannels;
        const length = audioBuffer.length;
        
        // Convert margin from milliseconds to samples
        const marginSamples = Math.floor((marginMs / 1000) * sampleRate);
        
        // Get audio data for analysis (use first channel if stereo)
        const audioData = audioBuffer.getChannelData(0);
        
        // Find start of speech (first non-silent sample)
        let startIndex = 0;
        for (let i = 0; i < length; i++) {
            if (Math.abs(audioData[i]) > silenceThreshold) {
                startIndex = Math.max(0, i - marginSamples);
                break;
            }
        }
        
        // Find end of speech (last non-silent sample)
        let endIndex = length - 1;
        for (let i = length - 1; i >= 0; i--) {
            if (Math.abs(audioData[i]) > silenceThreshold) {
                endIndex = Math.min(length - 1, i + marginSamples);
                break;
            }
        }
        
        // If no speech detected, return a minimal buffer
        if (startIndex >= endIndex) {
            console.warn('No speech detected in audio, returning minimal buffer');
            const minimalLength = Math.floor(sampleRate * 0.1); // 100ms minimal buffer
            const minimalBuffer = audioContext.createBuffer(
                numberOfChannels, 
                minimalLength, 
                sampleRate
            );
            return minimalBuffer;
        }
        
        // Calculate new buffer length
        const newLength = endIndex - startIndex + 1;
        
        // Create new trimmed buffer
        const trimmedBuffer = audioContext.createBuffer(
            numberOfChannels,
            newLength,
            sampleRate
        );
        
        // Copy trimmed audio data for all channels
        for (let channel = 0; channel < numberOfChannels; channel++) {
            const originalData = audioBuffer.getChannelData(channel);
            const trimmedData = trimmedBuffer.getChannelData(channel);
            
            for (let i = 0; i < newLength; i++) {
                trimmedData[i] = originalData[startIndex + i];
            }
        }
        
        // Log trimming results
        const originalDuration = (length / sampleRate * 1000).toFixed(0);
        const trimmedDuration = (newLength / sampleRate * 1000).toFixed(0);
        const trimmedStart = (startIndex / sampleRate * 1000).toFixed(0);
        const trimmedEnd = ((length - endIndex - 1) / sampleRate * 1000).toFixed(0);
        
        console.log(`Audio trimmed: ${originalDuration}ms → ${trimmedDuration}ms (removed ${trimmedStart}ms from start, ${trimmedEnd}ms from end)`);
        
        return trimmedBuffer;
    }
    
    audioBufferToWav(buffer) {
        // Target settings for 256kbps (approximately)
        const targetSampleRate = 16000; // 16kHz for speech quality
        const targetChannels = 1;       // Mono for speech
        const bitsPerSample = 16;
        
        // Resample and convert to mono if needed
        const resampledBuffer = this.resampleAndConvertToMono(buffer, targetSampleRate);
        
        const length = resampledBuffer.length;
        const numberOfChannels = targetChannels;
        const sampleRate = targetSampleRate;
        const bytesPerSample = bitsPerSample / 8;
        const blockAlign = numberOfChannels * bytesPerSample;
        const byteRate = sampleRate * blockAlign; // This will be 32000 bytes/sec = 256kbps
        const dataSize = length * blockAlign;
        const bufferSize = 44 + dataSize;
        
        const arrayBuffer = new ArrayBuffer(bufferSize);
        const view = new DataView(arrayBuffer);
        
        // Write WAV header
        const writeString = (offset, string) => {
            for (let i = 0; i < string.length; i++) {
                view.setUint8(offset + i, string.charCodeAt(i));
            }
        };
        
        // RIFF header
        writeString(0, 'RIFF');
        view.setUint32(4, bufferSize - 8, true);
        writeString(8, 'WAVE');
        
        // Format chunk
        writeString(12, 'fmt ');
        view.setUint32(16, 16, true); // Subchunk1Size
        view.setUint16(20, 1, true); // AudioFormat (PCM)
        view.setUint16(22, numberOfChannels, true);
        view.setUint32(24, sampleRate, true);
        view.setUint32(28, byteRate, true);
        view.setUint16(32, blockAlign, true);
        view.setUint16(34, bitsPerSample, true);
        
        // Data chunk
        writeString(36, 'data');
        view.setUint32(40, dataSize, true);
        
        // Write audio data (mono)
        let offset = 44;
        for (let i = 0; i < length; i++) {
            const sample = Math.max(-1, Math.min(1, resampledBuffer[i]));
            const intSample = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;
            view.setInt16(offset, intSample, true);
            offset += 2;
        }
        
        return new Blob([arrayBuffer], { type: 'audio/wav' });
    }
    
    resampleAndConvertToMono(buffer, targetSampleRate) {
        const originalSampleRate = buffer.sampleRate;
        const originalLength = buffer.length;
        const numberOfChannels = buffer.numberOfChannels;
        
        // Calculate new length after resampling
        const resampleRatio = targetSampleRate / originalSampleRate;
        const newLength = Math.floor(originalLength * resampleRatio);
        
        // Create output array
        const resampledData = new Float32Array(newLength);
        
        // Convert to mono first (mix all channels)
        const monoData = new Float32Array(originalLength);
        for (let i = 0; i < originalLength; i++) {
            let sum = 0;
            for (let channel = 0; channel < numberOfChannels; channel++) {
                sum += buffer.getChannelData(channel)[i];
            }
            monoData[i] = sum / numberOfChannels; // Average all channels
        }
        
        // Resample using linear interpolation
        for (let i = 0; i < newLength; i++) {
            const originalIndex = i / resampleRatio;
            const index = Math.floor(originalIndex);
            const fraction = originalIndex - index;
            
            if (index + 1 < originalLength) {
                // Linear interpolation between two samples
                resampledData[i] = monoData[index] * (1 - fraction) + monoData[index + 1] * fraction;
            } else {
                // Use the last sample if we're at the end
                resampledData[i] = monoData[index] || 0;
            }
        }
        
        return resampledData;
    }
    
    async submitForGrading(audioBlob) {
        try {
            // Show loading state
            this.showLoadingState();
            
            // Get current practice parameters
            const params = {
                topic_id: topicItemData.topic_id || '', // topicItemData is global variable
                id: topicItemData.id || '',
                item_type: topicItemData.item_type || 'question'
            };
            
            // Submit to pronunciation service
            const result = await this.pronunciationService.gradeRecording(audioBlob, params);
            
            // Display results
            this.displayResults(result);
            
        } catch (error) {
            console.error('Error submitting recording:', error);
            this.showError(error.message || 'Failed to submit recording for grading');
        } finally {
            this.hideLoadingState();
        }
    }
    
    displayResults(data) {
        // Extract real data from Azure pronunciation assessment response
        if (!data || !data.analysis) {
            this.showError('Invalid pronunciation analysis data received');
            return;
        }

        const analysis = data.analysis;
        const scores = analysis.scores || {};
        const itemData = data.item_data || {};
        
        // Extract scores with fallback values
        const overallScore = Math.round(analysis.overall_score || 0);
        const accuracy = Math.round(scores.accuracy || 0);
        const fluency = Math.round(scores.fluency || 0);
        const completeness = Math.round(scores.completeness || 0);
        const pronunciation = Math.round(scores.pronunciation || 0);
        
        // Extract text data
        const expectedText = data.expected_text || itemData.word || 'Unknown';
        const recognizedText = analysis.recognized_text || 'Not recognized';
        const feedback = analysis.feedback || 'No feedback available';
        
        // Extract additional information
        const detectedIssues = analysis.detected_issues || [];
        const suggestions = analysis.suggestions || [];
        
        // Build issues and suggestions HTML
        let issuesHtml = '';
        if (detectedIssues.length > 0) {
            issuesHtml = `
                <div class="detected-issues">
                    <h4>Detected Issues:</h4>
                    <ul>
                        ${detectedIssues.map(issue => `<li>${issue}</li>`).join('')}
                    </ul>
                </div>
            `;
        }
        
        let suggestionsHtml = '';
        if (suggestions.length > 0) {
            suggestionsHtml = `
                <div class="suggestions">
                    <h4>Suggestions for Improvement:</h4>
                    <ul>
                        ${suggestions.map(suggestion => `<li>${suggestion}</li>`).join('')}
                    </ul>
                </div>
            `;
        }
        
        // Update practice result section
        const practiceResult = document.querySelector('.practice-result');
        if (practiceResult) {
            practiceResult.innerHTML = `
                <div class="result-card">
                    <h3>Pronunciation Analysis</h3>
                    
                    <div class="text-comparison">
                        <div class="expected-text">
                            <span class="text-label">Expected:</span>
                            <span class="text-value">"${expectedText}"</span>
                        </div>
                        <div class="recognized-text">
                            <span class="text-label">You said:</span>
                            <span class="text-value">"${recognizedText}"</span>
                        </div>
                    </div>
                    
                    <div class="overall-score">
                        <div class="circular-progress ${this.getScoreClass(overallScore)}">
                            <div class="circular-progress-inner">
                                <div class="circular-progress-circle">
                                    <div class="circular-progress-mask full">
                                        <div class="circular-progress-fill" style="transform: rotate(${overallScore * 3.6}deg)"></div>
                                    </div>
                                    <div class="circular-progress-mask">
                                        <div class="circular-progress-fill" style="transform: rotate(${Math.min(180, overallScore * 3.6)}deg)"></div>
                                    </div>
                                    <div class="circular-progress-inside">
                                        <span class="circular-progress-percentage">${overallScore}<span class="percentage-sign">%</span></span>
                                        <span class="circular-progress-label">Overall Score</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="detailed-metrics">
                        <div class="metric">
                            <span class="metric-label">Accuracy:</span>
                            <div class="metric-bar">
                                <div class="metric-fill ${this.getScoreClass(accuracy)}" style="width: ${accuracy}%"></div>
                            </div>
                            <span class="metric-value">${accuracy}%</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">Fluency:</span>
                            <div class="metric-bar">
                                <div class="metric-fill ${this.getScoreClass(fluency)}" style="width: ${fluency}%"></div>
                            </div>
                            <span class="metric-value">${fluency}%</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">Completeness:</span>
                            <div class="metric-bar">
                                <div class="metric-fill ${this.getScoreClass(completeness)}" style="width: ${completeness}%"></div>
                            </div>
                            <span class="metric-value">${completeness}%</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">Pronunciation:</span>
                            <div class="metric-bar">
                                <div class="metric-fill ${this.getScoreClass(pronunciation)}" style="width: ${pronunciation}%"></div>
                            </div>
                            <span class="metric-value">${pronunciation}%</span>
                        </div>
                    </div>
                    
                    <div class="feedback-text">
                        <h4>Feedback:</h4>
                        <p>${feedback}</p>
                    </div>
                    
                    ${issuesHtml}
                    ${suggestionsHtml}
                    
                    <div class="result-actions">
                        <button class="btn btn-primary" onclick="pronunciationRecorder.resetRecording()">Try Again</button>
                        <button class="btn btn-secondary" onclick="window.history.back()">Back to Topics</button>
                    </div>
                </div>
            `;
        }
        
        console.log('Pronunciation results displayed:', {
            overallScore,
            scores: { accuracy, fluency, completeness, pronunciation },
            expectedText,
            recognizedText,
            feedback,
            detectedIssues,
            suggestions
        });
    }
    
    getScoreClass(score) {
        if (score >= 80) return 'score-excellent';
        if (score >= 60) return 'score-good';
        if (score >= 40) return 'score-fair';
        return 'score-poor';
    }
    
    showRecordingElements() {
        if (this.recordingStatus) {
            this.recordingStatus.style.display = 'block';
        }
        if (this.recordingDuration) {
            this.recordingDuration.style.display = 'flex';
        }
    }
    
    hideRecordingElements() {
        if (this.recordingStatus) {
            this.recordingStatus.style.display = 'none';
        }
        if (this.recordingDuration) {
            this.recordingDuration.style.display = 'none';
        }
    }
    
    updateButtonState() {
        if (this.recordBtn) {
            const recordText = this.recordBtn.querySelector('.record-text');
            if (this.isRecording) {
                this.recordBtn.classList.add('recording');
                if (recordText) recordText.textContent = 'Stop Recording';
            } else {
                this.recordBtn.classList.remove('recording');
                if (recordText) recordText.textContent = 'Record now';
            }
        }
    }
    
    startTimer() {
        this.timerInterval = setInterval(() => {
            if (this.startTime && this.durationText) {
                const elapsed = Date.now() - this.startTime;
                const minutes = Math.floor(elapsed / 60000);
                const seconds = Math.floor((elapsed % 60000) / 1000);
                
                const timeString = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                this.durationText.textContent = timeString;
            }
        }, 100);
    }
    
    stopTimer() {
        if (this.timerInterval) {
            clearInterval(this.timerInterval);
            this.timerInterval = null;
        }
    }
    
    showLoadingState() {
        if (this.recordBtn) {
            this.recordBtn.disabled = true;
            const recordText = this.recordBtn.querySelector('.record-text');
            if (recordText) recordText.textContent = 'Processing...';
        }
    }
    
    hideLoadingState() {
        if (this.recordBtn) {
            this.recordBtn.disabled = false;
            this.updateButtonState();
        }
    }
    
    showError(message) {
        console.error('PronunciationRecorder Error:', message);
        
        // You can implement a more sophisticated error display here
        const practiceResult = document.querySelector('.practice-result');
        if (practiceResult) {
            practiceResult.innerHTML = `
                <div class="error-message">
                    <h3>Error</h3>
                    <p>${message}</p>
                    <button class="btn btn-primary" onclick="pronunciationRecorder.resetRecording()">Try Again</button>
                </div>
            `;
        }
    }
    
    resetRecording() {
        // Reset the recording state
        this.isRecording = false;
        this.audioChunks = [];
        this.startTime = null;
        
        // Clear any existing intervals
        this.stopTimer();
        
        // Reset UI
        this.hideRecordingElements();
        this.updateButtonState();
        
        // Clear results
        const practiceResult = document.querySelector('.practice-result');
        if (practiceResult) {
            practiceResult.innerHTML = '';
        }
        
        // Reset duration text
        if (this.durationText) {
            this.durationText.textContent = '00:00';
        }
    }
    
    getNonce() {
        // Get WordPress nonce if available
        return window.toeic_ajax_nonce || '';
    }

    handleSuggestion(e) {
        const prompt = e.target.dataset.prompt;
        const subject = this.paragraphText;
        this.getSuggestion({prompt, subject})
    .then((data) => {
            this.showSuggestion(data);
        });
    }

    showSuggestion({title, suggestion}) {
        let template = `
            <div class="suggestion-item">
                <div class="suggestion-item__content">${suggestion}</div>
            </div>
        `

        const suggestionsResult = document.querySelector('.suggestion-result');
        if (suggestionsResult) {
            suggestionsResult.innerHTML += template;
        }
    }

    async getSuggestion({prompt, subject}) {
        try {
            const data = await this.pronunciationService.getSuggestions({prompt, subject});
            return {
                "suggestion": data.suggestion,
            };
        } catch (error) {
            console.error('Error fetching suggestions:', error);
        }
    }
}

export {PronunciationRecorder};
