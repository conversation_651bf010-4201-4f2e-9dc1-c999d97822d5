/**
 * TOEIC Practice Plugin - Frontend Main Entry Point
 * 
 * This file serves as the main entry point for the frontend JavaScript
 * functionality of the TOEIC Practice plugin.
 */

// Import components
import Sidebar from './components/Sidebar';
import Loader from './components/Loader';
import ajaxService, { AjaxService } from './services/AjaxService';
import testService, { TestService } from './services/TestService';
import TestDetailPage from './pages/test-detail-page';
import { PronunciationRecorder } from './pages/pronunciation-page';

// Initialize the application when DOM is fully loaded
document.addEventListener('DOMContentLoaded', () => {
    // Global loader instance for full-screen loading
    window.toeicLoader = new Loader({
        fullScreen: true
    });
    
    // Make services available globally
    window.toeicAjax = ajaxService;
    window.toeicTest = testService;

    // check if record button exists
    const recordBtn = document.getElementById('record-btn');
    if (recordBtn) {
        window.toeicPronunciation = new PronunciationRecorder({paragraphText: recordBtn.dataset.paragraphText});
    }

    // Initialize sidebar
    const sidebar = document.querySelector('.toeic-sidebar');
    if (sidebar) {
        const sidebar = new Sidebar(sidebar);
        sidebar.init();
    }
});

// Export components for potential reuse
export {
    Sidebar,
    Loader,
    ajaxService,
    AjaxService,
    testService,
    TestService,
    TestDetailPage,
};
