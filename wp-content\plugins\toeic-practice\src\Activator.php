<?php
/**
 * Plugin Activator
 *
 * @package ToeicPractice
 */

namespace ToeicPractice;

/**
 * Plugin Activator Class
 * 
 * Handles plugin activation tasks such as creating database tables,
 * setting default options, and flushing rewrite rules.
 */
class Activator {
    
    /**
     * Activate the plugin
     * 
     * This method is called when the plugin is activated.
     * It performs necessary setup tasks.
     */
    public static function activate() {
        // Check WordPress version compatibility
        self::checkWordPressVersion();
        
        // Check PHP version compatibility
        self::checkPhpVersion();
        
        // Create database tables
        self::createDatabaseTables();

        // Create user roles
        self::createUserRoles();

        // Set default options
        self::setDefaultOptions();
        
        // Create necessary directories
        self::createDirectories();
        
        // Flush rewrite rules
        self::flushRewriteRules();
        
        // Set activation timestamp
        update_option('toeic_practice_activated_time', current_time('timestamp'));
        
        // Set plugin version
        update_option('toeic_practice_version', TOEIC_PRACTICE_VERSION);
    }
    
    /**
     * Check WordPress version compatibility
     */
    private static function checkWordPressVersion() {
        global $wp_version;
        
        $required_wp_version = '5.0';
        
        if (version_compare($wp_version, $required_wp_version, '<')) {
            deactivate_plugins(TOEIC_PRACTICE_PLUGIN_BASENAME);
            wp_die(
                sprintf(
                    __('TOEIC Practice requires WordPress version %s or higher. You are running version %s.', 'toeic-practice'),
                    $required_wp_version,
                    $wp_version
                )
            );
        }
    }
    
    /**
     * Check PHP version compatibility
     */
    private static function checkPhpVersion() {
        $required_php_version = '7.4';
        
        if (version_compare(PHP_VERSION, $required_php_version, '<')) {
            deactivate_plugins(TOEIC_PRACTICE_PLUGIN_BASENAME);
            wp_die(
                sprintf(
                    __('TOEIC Practice requires PHP version %s or higher. You are running version %s.', 'toeic-practice'),
                    $required_php_version,
                    PHP_VERSION
                )
            );
        }
    }
    
    /**
     * Create database tables
     */
    private static function createDatabaseTables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // Table for storing user progress
        $table_progress = $wpdb->prefix . 'toeic_user_progress';
        $sql_progress = "CREATE TABLE $table_progress (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            user_id bigint(20) NOT NULL,
            test_id bigint(20) NOT NULL,
            section_id bigint(20) NOT NULL,
            completed_exercises int(11) DEFAULT 0,
            total_exercises int(11) DEFAULT 0,
            best_score int(11) DEFAULT 0,
            last_activity datetime DEFAULT CURRENT_TIMESTAMP,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY user_test_section (user_id, test_id, section_id),
            KEY user_id (user_id),
            KEY test_id (test_id),
            KEY section_id (section_id)
        ) $charset_collate;";
        
        // Table for storing TOEIC vocabulary
        $table_vocabulary = $wpdb->prefix . 'toeic_vocabulary';
        $sql_vocabulary = "CREATE TABLE $table_vocabulary (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            word varchar(255) NOT NULL,
            vi_translate text NOT NULL,
            audio_link varchar(255) DEFAULT '',
            category_id bigint(20) DEFAULT 0,
            example_sentence text,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY word (word(191)),
            KEY category_id (category_id)
        ) $charset_collate;";
        
        // Table for storing TOEIC questions
        $table_questions = $wpdb->prefix . 'toeic_questions';
        $sql_questions = "CREATE TABLE $table_questions (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            title varchar(255) NOT NULL,
            question_type varchar(50) NOT NULL,
            content longtext NOT NULL,
            correct_answer longtext NOT NULL,
            options longtext DEFAULT NULL,
            explanation text DEFAULT NULL,
            difficulty varchar(20) DEFAULT 'medium',
            category_id bigint(20) DEFAULT 0,
            section varchar(50) DEFAULT NULL,
            media_url varchar(255) DEFAULT NULL,
            media_type varchar(20) DEFAULT NULL,
            points int(11) DEFAULT 1,
            time_limit int(11) DEFAULT 0,
            tags text DEFAULT NULL,
            status varchar(20) DEFAULT 'published',
            author_id bigint(20) DEFAULT 0,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY question_type (question_type),
            KEY category_id (category_id),
            KEY section (section),
            KEY status (status),
            KEY author_id (author_id)
        ) $charset_collate;";
        
        // Table for storing question metadata
        $table_question_meta = $wpdb->prefix . 'toeic_question_meta';
        $sql_question_meta = "CREATE TABLE $table_question_meta (
            meta_id bigint(20) NOT NULL AUTO_INCREMENT,
            question_id bigint(20) NOT NULL,
            meta_key varchar(255) NOT NULL,
            meta_value longtext DEFAULT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (meta_id),
            KEY question_id (question_id),
            KEY meta_key (meta_key(191))
        ) $charset_collate;";
        
        // Tests
        $table_tests = $wpdb->prefix . 'toeic_tests';
        $sql_tests = "CREATE TABLE $table_tests (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            title varchar(255) NOT NULL,
            description text,
            instructions text,
            time_limit int(11) DEFAULT 0,
            status varchar(20) DEFAULT 'draft',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            created_by bigint(20),
            settings longtext,
            metadata longtext,
            PRIMARY KEY (id),
            KEY status (status),
            KEY created_by (created_by)
        ) $charset_collate;";
        
        // Sections
        $table_sections = $wpdb->prefix . 'toeic_sections';
        $sql_sections = "CREATE TABLE $table_sections (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            test_id bigint(20) NOT NULL,
            title varchar(255) NOT NULL,
            description text,
            instructions text,
            time_limit int(11) DEFAULT 0,
            sort_order int(11) DEFAULT 0,
            settings longtext,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY test_id (test_id)
        ) $charset_collate;";
        
        // Section-Question Relationships
        $table_section_questions = $wpdb->prefix . 'toeic_section_questions';
        $sql_section_questions = "CREATE TABLE $table_section_questions (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            section_id bigint(20) NOT NULL,
            question_id bigint(20) NOT NULL,
            sort_order int(11) DEFAULT 0,
            points int(11) DEFAULT 1,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY section_question (section_id, question_id),
            KEY section_id (section_id),
            KEY question_id (question_id)
        ) $charset_collate;";
        
        // Results
        $table_results = $wpdb->prefix . 'toeic_results';
        $sql_results = "CREATE TABLE $table_results (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            test_id bigint(20) NOT NULL,
            user_id bigint(20) NOT NULL,
            score decimal(10,2) DEFAULT 0,
            max_score decimal(10,2) DEFAULT 0,
            time_taken int(11) DEFAULT 0,
            status varchar(20) DEFAULT 'completed',
            started_at datetime,
            completed_at datetime,
            answers longtext,
            section_scores longtext,
            metadata longtext,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY test_id (test_id),
            KEY user_id (user_id),
            KEY test_user (test_id, user_id)
        ) $charset_collate;";
        
        // Pronunciation Topics
        $table_pronunciation_topics = $wpdb->prefix . 'toeic_pronunciation_topics';
        $sql_pronunciation_topics = "CREATE TABLE $table_pronunciation_topics (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            title varchar(255) NOT NULL,
            description text DEFAULT NULL,
            status varchar(20) DEFAULT 'draft',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY status (status)
        ) $charset_collate;";
        
        // Pronunciation Topic Items (Junction Table)
        $table_pronunciation_topic_items = $wpdb->prefix . 'toeic_pronunciation_topic_items';
        $sql_pronunciation_topic_items = "CREATE TABLE $table_pronunciation_topic_items (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            topic_id bigint(20) NOT NULL,
            item_type varchar(20) NOT NULL,
            item_id bigint(20) NOT NULL,
            sort_order int(11) DEFAULT 0,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY topic_item_unique (topic_id, item_type, item_id),
            KEY topic_id (topic_id),
            KEY item_type (item_type),
            KEY item_id (item_id)
        ) $charset_collate;";

        // Table for storing classes
        $table_class = $wpdb->prefix . 'class';
        $sql_class = "CREATE TABLE $table_class (
            class_id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            course_code varchar(20) NOT NULL,
            title varchar(100) NOT NULL,
            instructor_id bigint(20) UNSIGNED NOT NULL,
            schedule varchar(100) DEFAULT NULL COMMENT 'Format: \"Mon/Wed 10:00-11:30\"',
            location varchar(100) DEFAULT NULL COMMENT 'Room/building or online',
            max_seats int(11) DEFAULT 30,
            current_seats int(11) DEFAULT 0,
            start_date date DEFAULT NULL,
            end_date date DEFAULT NULL,
            status enum('draft','open','closed','completed') DEFAULT 'draft',
            created_at timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (class_id),
            KEY idx_wp_class_instructor (instructor_id),
            KEY idx_wp_class_status (status)
        ) $charset_collate;";

        // Table for storing class enrollments
        $table_class_enrollment = $wpdb->prefix . 'class_enrollment';
        $sql_class_enrollment = "CREATE TABLE $table_class_enrollment (
            enrollment_id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            class_id bigint(20) UNSIGNED NOT NULL,
            student_id bigint(20) UNSIGNED NOT NULL,
            enrollment_date timestamp DEFAULT CURRENT_TIMESTAMP,
            status enum('registered','attending','completed','withdrawn') DEFAULT 'registered',
            grade varchar(2) DEFAULT NULL,
            completion_date datetime DEFAULT NULL,
            PRIMARY KEY (enrollment_id),
            UNIQUE KEY unique_enrollment (class_id, student_id),
            KEY idx_wp_enrollment_class (class_id),
            KEY idx_wp_enrollment_student (student_id),
            KEY idx_wp_enrollment_status (status)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql_progress);
        dbDelta($sql_vocabulary);
        dbDelta($sql_questions);
        dbDelta($sql_question_meta);
        dbDelta($sql_tests);
        dbDelta($sql_sections);
        dbDelta($sql_section_questions);
        dbDelta($sql_results);
        dbDelta($sql_pronunciation_topics);
        dbDelta($sql_pronunciation_topic_items);
        dbDelta($sql_class);
        dbDelta($sql_class_enrollment);
    }

    /**
     * Create custom user roles
     */
    private static function createUserRoles() {
        // Remove existing roles if they exist (for clean reinstall)
        remove_role('student');
        remove_role('instructor');

        // Add Student role
        add_role(
            'student',
            __('Student', 'toeic-practice'),
            [
                'read' => true,
                'edit_posts' => false,
                'delete_posts' => false,
                'publish_posts' => false,
                'upload_files' => false,
                // Custom capabilities for students
                'take_toeic_tests' => true,
                'view_test_results' => true,
                'enroll_in_classes' => true,
                'view_class_materials' => true,
                'submit_assignments' => true,
                'view_own_progress' => true,
            ]
        );

        // Add Instructor role
        add_role(
            'instructor',
            __('Instructor', 'toeic-practice'),
            [
                'read' => true,
                'edit_posts' => true,
                'delete_posts' => true,
                'publish_posts' => true,
                'upload_files' => true,
                'edit_others_posts' => false,
                'delete_others_posts' => false,
                'edit_published_posts' => true,
                'delete_published_posts' => true,
                'edit_pages' => false,
                'delete_pages' => false,
                'publish_pages' => false,
                'manage_categories' => false,
                // Custom capabilities for instructors
                'create_toeic_tests' => true,
                'edit_toeic_tests' => true,
                'delete_toeic_tests' => true,
                'manage_classes' => true,
                'create_classes' => true,
                'edit_classes' => true,
                'delete_classes' => true,
                'view_student_progress' => true,
                'grade_assignments' => true,
                'manage_enrollments' => true,
                'view_class_analytics' => true,
                'export_student_data' => true,
            ]
        );

        // Add custom capabilities to administrator role
        $admin_role = get_role('administrator');
        if ($admin_role) {
            // Student capabilities
            $admin_role->add_cap('take_toeic_tests');
            $admin_role->add_cap('view_test_results');
            $admin_role->add_cap('enroll_in_classes');
            $admin_role->add_cap('view_class_materials');
            $admin_role->add_cap('submit_assignments');
            $admin_role->add_cap('view_own_progress');

            // Instructor capabilities
            $admin_role->add_cap('create_toeic_tests');
            $admin_role->add_cap('edit_toeic_tests');
            $admin_role->add_cap('delete_toeic_tests');
            $admin_role->add_cap('manage_classes');
            $admin_role->add_cap('create_classes');
            $admin_role->add_cap('edit_classes');
            $admin_role->add_cap('delete_classes');
            $admin_role->add_cap('view_student_progress');
            $admin_role->add_cap('grade_assignments');
            $admin_role->add_cap('manage_enrollments');
            $admin_role->add_cap('view_class_analytics');
            $admin_role->add_cap('export_student_data');
        }
    }

    /**
     * Set default plugin options
     */
    private static function setDefaultOptions() {
        $default_options = [
            'toeic_practice_enable_listening' => 1,
            'toeic_practice_enable_reading' => 1,
            'toeic_practice_time_limit' => 120, // minutes
            'toeic_practice_questions_per_test' => 200,
            'toeic_practice_show_explanations' => 1,
            'toeic_practice_allow_retakes' => 1,
            'toeic_practice_save_progress' => 1,
            'toeic_practice_difficulty_levels' => ['beginner', 'intermediate', 'advanced'],
        ];
        
        foreach ($default_options as $option_name => $option_value) {
            if (get_option($option_name) === false) {
                add_option($option_name, $option_value);
            }
        }
    }
    
    /**
     * Create necessary directories
     */
    private static function createDirectories() {
        $upload_dir = wp_upload_dir();
        $toeic_dir = $upload_dir['basedir'] . '/toeic-practice';
        
        if (!file_exists($toeic_dir)) {
            wp_mkdir_p($toeic_dir);
        }
        
        // Create subdirectories
        $subdirs = ['audio', 'images', 'exports', 'temp'];
        foreach ($subdirs as $subdir) {
            $dir_path = $toeic_dir . '/' . $subdir;
            if (!file_exists($dir_path)) {
                wp_mkdir_p($dir_path);
            }
        }
        
        // Create .htaccess file for security
        $htaccess_content = "Options -Indexes\n<Files *.php>\nOrder allow,deny\nDeny from all\n</Files>";
        file_put_contents($toeic_dir . '/.htaccess', $htaccess_content);
    }
    
    /**
     * Flush rewrite rules
     */
    private static function flushRewriteRules() {
        // Register post types and taxonomies first
        $postTypes = new PostTypes\PostTypeManager();
        $postTypes->register();
        
        $taxonomies = new Taxonomies\TaxonomyManager();
        $taxonomies->register();
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }
}
