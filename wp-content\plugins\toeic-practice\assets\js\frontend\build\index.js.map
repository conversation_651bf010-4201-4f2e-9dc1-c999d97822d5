{"version": 3, "file": "index.js", "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,O;;;;;;;;;;;;;;;;;;;;;;;ACVA;AACA;AACA;AACA;AACA;AACA;AALA,IAMMA,MAAM;EACR;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,SAAAA,OAAA,EAA0B;IAAA,IAAdC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IAAAG,eAAA,OAAAL,MAAA;IACpB,IAAI,CAACC,OAAO,GAAG;MACXK,WAAW,EAAEL,OAAO,CAACK,WAAW,IAAI,wBAAwB;MAC5DC,IAAI,EAAEN,OAAO,CAACM,IAAI,IAAI,YAAY;MAClCC,IAAI,EAAEP,OAAO,CAACO,IAAI,IAAI,QAAQ;MAC9BC,UAAU,EAAER,OAAO,CAACQ,UAAU,IAAI;IACtC,CAAC;IAED,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,SAAS,GAAG,KAAK;IAEtB,IAAI,CAACC,IAAI,CAAC,CAAC;EACf;;EAEA;AACJ;AACA;EAFI,OAAAC,YAAA,CAAAd,MAAA;IAAAe,GAAA;IAAAC,KAAA,EAGA,SAAAH,IAAIA,CAAA,EAAG;MACH;MACA,IAAI,CAACH,SAAS,GAAGO,QAAQ,CAACC,cAAc,CAAC,IAAI,CAACjB,OAAO,CAACK,WAAW,CAAC;;MAElE;MACA,IAAI,CAAC,IAAI,CAACI,SAAS,EAAE;QACjB,IAAI,CAACA,SAAS,GAAGO,QAAQ,CAACE,aAAa,CAAC,KAAK,CAAC;QAC9C,IAAI,CAACT,SAAS,CAACU,EAAE,GAAG,IAAI,CAACnB,OAAO,CAACK,WAAW;QAC5C,IAAI,CAACI,SAAS,CAACW,SAAS,GAAG,wBAAwB;QAEnD,IAAI,IAAI,CAACpB,OAAO,CAACQ,UAAU,EAAE;UACzB,IAAI,CAACC,SAAS,CAACY,SAAS,CAACC,GAAG,CAAC,YAAY,CAAC;QAC9C;QAEAN,QAAQ,CAACO,IAAI,CAACC,WAAW,CAAC,IAAI,CAACf,SAAS,CAAC;MAC7C;;MAEA;MACA,IAAI,CAACgB,YAAY,CAAC,CAAC;IACvB;;IAEA;AACJ;AACA;EAFI;IAAAX,GAAA;IAAAC,KAAA,EAGA,SAAAU,YAAYA,CAAA,EAAG;MACX,IAAI,CAACf,MAAM,GAAGM,QAAQ,CAACE,aAAa,CAAC,KAAK,CAAC;MAC3C,IAAI,CAACR,MAAM,CAACU,SAAS,mBAAAM,MAAA,CAAmB,IAAI,CAAC1B,OAAO,CAACO,IAAI,CAAE;;MAE3D;MACA,IAAMoB,OAAO,GAAGX,QAAQ,CAACE,aAAa,CAAC,KAAK,CAAC;MAC7CS,OAAO,CAACP,SAAS,GAAG,eAAe;MACnC,IAAI,CAACV,MAAM,CAACc,WAAW,CAACG,OAAO,CAAC;;MAEhC;MACA,IAAI,IAAI,CAAC3B,OAAO,CAACM,IAAI,EAAE;QACnB,IAAMsB,WAAW,GAAGZ,QAAQ,CAACE,aAAa,CAAC,KAAK,CAAC;QACjDU,WAAW,CAACR,SAAS,GAAG,mBAAmB;QAC3CQ,WAAW,CAACC,WAAW,GAAG,IAAI,CAAC7B,OAAO,CAACM,IAAI;QAC3C,IAAI,CAACI,MAAM,CAACc,WAAW,CAACI,WAAW,CAAC;MACxC;;MAEA;MACA,IAAI,CAACnB,SAAS,CAACe,WAAW,CAAC,IAAI,CAACd,MAAM,CAAC;MACvC,IAAI,CAACoB,IAAI,CAAC,CAAC;IACf;;IAEA;AACJ;AACA;AACA;AACA;EAJI;IAAAhB,GAAA;IAAAC,KAAA,EAKA,SAAAgB,IAAIA,CAAA,EAAc;MAAA,IAAbzB,IAAI,GAAAL,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;MACZ,IAAIK,IAAI,EAAE;QACN,IAAI,CAAC0B,UAAU,CAAC1B,IAAI,CAAC;MACzB;MAEA,IAAI,CAACG,SAAS,CAACY,SAAS,CAACC,GAAG,CAAC,MAAM,CAAC;MACpC,IAAI,CAACX,SAAS,GAAG,IAAI;MAErB,OAAO,IAAI;IACf;;IAEA;AACJ;AACA;EAFI;IAAAG,GAAA;IAAAC,KAAA,EAGA,SAAAe,IAAIA,CAAA,EAAG;MACH,IAAI,CAACrB,SAAS,CAACwB,KAAK,CAACC,OAAO,GAAG,MAAM;MACrC,IAAI,CAACvB,SAAS,GAAG,KAAK;MAEtB,OAAO,IAAI;IACf;;IAEA;AACJ;AACA;AACA;AACA;EAJI;IAAAG,GAAA;IAAAC,KAAA,EAKA,SAAAiB,UAAUA,CAAC1B,IAAI,EAAE;MACb,IAAMsB,WAAW,GAAG,IAAI,CAAClB,MAAM,CAACyB,aAAa,CAAC,oBAAoB,CAAC;MAEnE,IAAIP,WAAW,EAAE;QACbA,WAAW,CAACC,WAAW,GAAGvB,IAAI;MAClC,CAAC,MAAM,IAAIA,IAAI,EAAE;QACb;QACA,IAAM8B,cAAc,GAAGpB,QAAQ,CAACE,aAAa,CAAC,KAAK,CAAC;QACpDkB,cAAc,CAAChB,SAAS,GAAG,mBAAmB;QAC9CgB,cAAc,CAACP,WAAW,GAAGvB,IAAI;QACjC,IAAI,CAACI,MAAM,CAACc,WAAW,CAACY,cAAc,CAAC;MAC3C;MAEA,OAAO,IAAI;IACf;;IAEA;AACJ;AACA;AACA;AACA;EAJI;IAAAtB,GAAA;IAAAC,KAAA,EAKA,SAAAsB,MAAMA,CAAA,EAAc;MAAA,IAAb/B,IAAI,GAAAL,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;MACd,IAAI,IAAI,CAACU,SAAS,EAAE;QAChB,IAAI,CAACmB,IAAI,CAAC,CAAC;MACf,CAAC,MAAM;QACH,IAAI,CAACC,IAAI,CAACzB,IAAI,CAAC;MACnB;MAEA,OAAO,IAAI;IACf;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;EANI;IAAAQ,GAAA;IAAAC,KAAA,EAOA,SAAAuB,OAAOA,CAACC,QAAQ,EAAe;MAAA,IAAAC,KAAA;MAAA,IAAblC,IAAI,GAAAL,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;MACzB,OAAO,IAAIwC,OAAO,CAAC,UAAAC,OAAO,EAAI;QAC1BF,KAAI,CAACT,IAAI,CAACzB,IAAI,CAAC;QAEfqC,UAAU,CAAC,YAAM;UACbH,KAAI,CAACV,IAAI,CAAC,CAAC;UACXY,OAAO,CAAC,CAAC;QACb,CAAC,EAAEH,QAAQ,CAAC;MAChB,CAAC,CAAC;IACN;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;EANI;IAAAzB,GAAA;IAAAC,KAAA,EAOA,SAAO6B,UAAUA,CAACC,OAAO,EAAgB;MAAA,IAAd7C,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MACnC;MACA,IAAI,OAAO4C,OAAO,KAAK,QAAQ,EAAE;QAC7BA,OAAO,GAAG7B,QAAQ,CAACmB,aAAa,CAACU,OAAO,CAAC;MAC7C;MAEA,IAAI,CAACA,OAAO,EAAE;QACVC,OAAO,CAACC,KAAK,CAAC,yCAAyC,CAAC;QACxD,OAAO,IAAI;MACf;;MAEA;MACA,IAAMC,QAAQ,GAAG,eAAe,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;;MAE1E;MACA,IAAMC,aAAa,GAAGC,MAAM,CAACC,gBAAgB,CAACV,OAAO,CAAC;MACtD,IAAIQ,aAAa,CAACG,QAAQ,KAAK,QAAQ,EAAE;QACrCX,OAAO,CAACZ,KAAK,CAACuB,QAAQ,GAAG,UAAU;MACvC;;MAEA;MACA,IAAM/C,SAAS,GAAGO,QAAQ,CAACE,aAAa,CAAC,KAAK,CAAC;MAC/CT,SAAS,CAACU,EAAE,GAAG6B,QAAQ;MACvBvC,SAAS,CAACW,SAAS,GAAG,wBAAwB;MAC9CyB,OAAO,CAACrB,WAAW,CAACf,SAAS,CAAC;;MAE9B;MACA,OAAO,IAAIV,MAAM,CAAA0D,aAAA,CAAAA,aAAA,KACVzD,OAAO;QACVK,WAAW,EAAE2C,QAAQ;QACrBxC,UAAU,EAAE;MAAK,EACpB,CAAC;IACN;EAAC;AAAA;AAGL,iEAAeT,MAAM,E;;;;;;;;;;;;;;;;;;;;AC1MrB;AACA;AACA;AACA;AACA;AACA;AALA,IAMM2D,OAAO;EACT;AACJ;AACA;AACA;AACA;EACI,SAAAA,QAAYb,OAAO,EAAE;IAAAzC,eAAA,OAAAsD,OAAA;IACjB,IAAI,CAACb,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACc,SAAS,GAAG,IAAI,CAACd,OAAO,CAACe,gBAAgB,CAAC,kBAAkB,CAAC;IAClE,IAAI,CAACC,aAAa,GAAG,IAAI,CAAChB,OAAO,CAACV,aAAa,CAAC,wBAAwB,CAAC;EAC7E;;EAEA;AACJ;AACA;EAFI,OAAAtB,YAAA,CAAA6C,OAAA;IAAA5C,GAAA;IAAAC,KAAA,EAGA,SAAAH,IAAIA,CAAA,EAAG;MACH,IAAI,CAACkD,mBAAmB,CAAC,CAAC;MAC1B,IAAI,CAACC,oBAAoB,CAAC,CAAC;IAC/B;;IAEA;AACJ;AACA;EAFI;IAAAjD,GAAA;IAAAC,KAAA,EAGA,SAAA+C,mBAAmBA,CAAA,EAAG;MAAA,IAAAtB,KAAA;MAClB,IAAI,CAACmB,SAAS,CAACK,OAAO,CAAC,UAAAC,IAAI,EAAI;QAC3BA,IAAI,CAACC,gBAAgB,CAAC,OAAO,EAAE1B,KAAI,CAAC2B,mBAAmB,CAACC,IAAI,CAAC5B,KAAI,CAAC,CAAC;MACvE,CAAC,CAAC;MAEF,IAAI,CAACK,OAAO,CAACqB,gBAAgB,CAAC,OAAO,EAAE,UAACG,CAAC,EAAK;QAC1CA,CAAC,CAACC,eAAe,CAAC,CAAC;MACvB,CAAC,CAAC;MACF,IAAI,CAACT,aAAa,CAACK,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACK,mBAAmB,CAACH,IAAI,CAAC,IAAI,CAAC,CAAC;;MAEjF;MACApD,QAAQ,CAACkD,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACM,mBAAmB,CAACJ,IAAI,CAAC,IAAI,CAAC,CAAC;IAC3E;;IAEA;AACJ;AACA;EAFI;IAAAtD,GAAA;IAAAC,KAAA,EAGA,SAAAyD,mBAAmBA,CAAA,EAAG;MAClB,IAAI,CAAC3B,OAAO,CAACxB,SAAS,CAACoD,MAAM,CAAC,MAAM,CAAC;IACzC;;IAEA;AACJ;AACA;EAFI;IAAA3D,GAAA;IAAAC,KAAA,EAGA,SAAAwD,mBAAmBA,CAAA,EAAG;MAClB,IAAI,CAAC1B,OAAO,CAACxB,SAAS,CAACgB,MAAM,CAAC,MAAM,CAAC;IACzC;;IAEA;AACJ;AACA;AACA;AACA;EAJI;IAAAvB,GAAA;IAAAC,KAAA,EAKA,SAAAoD,mBAAmBA,CAACO,KAAK,EAAE;MACvB;MACA,IAAI,CAACf,SAAS,CAACK,OAAO,CAAC,UAAAC,IAAI,EAAI;QAC3BA,IAAI,CAAC5C,SAAS,CAACoD,MAAM,CAAC,QAAQ,CAAC;MACnC,CAAC,CAAC;;MAEF;MACAC,KAAK,CAACC,aAAa,CAACtD,SAAS,CAACC,GAAG,CAAC,QAAQ,CAAC;IAC/C;;IAEA;AACJ;AACA;EAFI;IAAAR,GAAA;IAAAC,KAAA,EAGA,SAAAgD,oBAAoBA,CAAA,EAAG;MACnB;MACA,IAAMa,WAAW,GAAGtB,MAAM,CAACuB,QAAQ,CAACC,QAAQ;;MAE5C;MACA,IAAI,CAACnB,SAAS,CAACK,OAAO,CAAC,UAAAC,IAAI,EAAI;QAC3B,IAAMc,IAAI,GAAGd,IAAI,CAAC9B,aAAa,CAAC,GAAG,CAAC;QACpC,IAAI4C,IAAI,IAAIA,IAAI,CAACC,YAAY,CAAC,MAAM,CAAC,KAAKJ,WAAW,EAAE;UACnDX,IAAI,CAAC5C,SAAS,CAACC,GAAG,CAAC,QAAQ,CAAC;QAChC;MACJ,CAAC,CAAC;IACN;EAAC;AAAA;AAGL,iEAAeoC,OAAO,E;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzFtB;AACA;AACA;AACA;AACA;;AAEkD;AAAA,IAE5CwB,gBAAgB,0BAAAC,iBAAA;EAAA,SAAAD,iBAAA;IAAA9E,eAAA,OAAA8E,gBAAA;IAAA,OAAAE,UAAA,OAAAF,gBAAA,EAAAjF,SAAA;EAAA;EAAAoF,SAAA,CAAAH,gBAAA,EAAAC,iBAAA;EAAA,OAAAtE,YAAA,CAAAqE,gBAAA;IAAApE,GAAA;IAAAC,KAAA;IAClB;AACJ;AACA;IACI,SAAAuE,MAAMA,CAAA,EAAG;MAAA,IAAA9C,KAAA;MACL,IAAI,CAAC+C,cAAc,CAAC,CAAC;;MAErB;MACA,IAAIC,KAAK,GAAG,EAAE;MACd,IAAI;QACAA,KAAK,GAAG,OAAO,IAAI,CAACC,QAAQ,CAACD,KAAK,KAAK,QAAQ,GAC3CE,IAAI,CAACC,KAAK,CAAC,IAAI,CAACF,QAAQ,CAACD,KAAK,CAAC,GAAG,IAAI,CAACC,QAAQ,CAACD,KAAK;MAC7D,CAAC,CAAC,OAAOnB,CAAC,EAAE;QACRvB,OAAO,CAACC,KAAK,CAAC,+BAA+B,EAAEsB,CAAC,CAAC;QACjD;MACJ;;MAEA;MACA,IAAI,CAACmB,KAAK,IAAI,CAACA,KAAK,CAACtF,MAAM,EAAE;QACzB4C,OAAO,CAACC,KAAK,CAAC,uCAAuC,EAAE,IAAI,CAAC0C,QAAQ,CAACtE,EAAE,CAAC;QACxE;MACJ;;MAEA;MACA,IAAMyE,iBAAiB,GAAG5E,QAAQ,CAACE,aAAa,CAAC,KAAK,CAAC;MACvD0E,iBAAiB,CAACxE,SAAS,GAAG,0BAA0B;;MAExD;MACA,IAAMyE,WAAW,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC,IAAI,CAAC,CAAC;;MAE9C;MACA,IAAMC,UAAU,GAAG/E,QAAQ,CAACE,aAAa,CAAC,KAAK,CAAC;MAChD6E,UAAU,CAAC3E,SAAS,GAAG,2CAA2C;;MAElE;MACA,IAAM4E,WAAW,GAAGhF,QAAQ,CAACE,aAAa,CAAC,KAAK,CAAC;MACjD8E,WAAW,CAAC5E,SAAS,GAAG,4CAA4C;;MAEpE;MACA,IAAM6E,UAAU,GAAGjF,QAAQ,CAACE,aAAa,CAAC,KAAK,CAAC;MAChD+E,UAAU,CAAC7E,SAAS,GAAG,uBAAuB;MAC9C6E,UAAU,CAACpE,WAAW,GAAG,SAAS;MAClCkE,UAAU,CAACvE,WAAW,CAACyE,UAAU,CAAC;MAElC,IAAMC,WAAW,GAAGlF,QAAQ,CAACE,aAAa,CAAC,KAAK,CAAC;MACjDgF,WAAW,CAAC9E,SAAS,GAAG,uBAAuB;MAC/C8E,WAAW,CAACrE,WAAW,GAAG,YAAY;MACtCmE,WAAW,CAACxE,WAAW,CAAC0E,WAAW,CAAC;;MAEpC;MACA,IAAI,CAACC,YAAY,GAAG,IAAI;MACxB,IAAI,CAACC,aAAa,GAAG,IAAI;;MAEzB;MACA,IAAI,CAACC,UAAU,GAAGb,KAAK,CAACtF,MAAM;MAC9B,IAAI,CAACoG,WAAW,GAAG,CAAC;;MAEpB;MACAd,KAAK,CAACxB,OAAO,CAAC,UAACC,IAAI,EAAEsC,KAAK,EAAK;QAC3B;QACA,IAAMC,QAAQ,GAAGxF,QAAQ,CAACE,aAAa,CAAC,KAAK,CAAC;QAC9CsF,QAAQ,CAACpF,SAAS,GAAG,qBAAqB;QAC1CoF,QAAQ,CAACC,OAAO,CAACC,MAAM,GAAGzC,IAAI,CAAC9C,EAAE;QAEjC,IAAMwF,aAAa,GAAG3F,QAAQ,CAACE,aAAa,CAAC,KAAK,CAAC;QACnDyF,aAAa,CAACvF,SAAS,GAAG,+BAA+B;;QAEzD;QACA,IAAMwF,QAAQ,GAAG5F,QAAQ,CAACE,aAAa,CAAC,MAAM,CAAC;QAC/C0F,QAAQ,CAACxF,SAAS,GAAG,qBAAqB;QAC1CwF,QAAQ,CAAC/E,WAAW,GAAGoC,IAAI,CAAC4C,MAAM;QAClCF,aAAa,CAACnF,WAAW,CAACoF,QAAQ,CAAC;;QAEnC;QACA,IAAI3C,IAAI,CAAC6C,UAAU,EAAE;UACjB,IAAMC,WAAW,GAAG/F,QAAQ,CAACE,aAAa,CAAC,QAAQ,CAAC;UACpD6F,WAAW,CAAC3F,SAAS,GAAG,0BAA0B;UAClD2F,WAAW,CAACC,SAAS,GAAG,osBAAosB;UAE5tBD,WAAW,CAAC7C,gBAAgB,CAAC,OAAO,EAAE,UAACG,CAAC,EAAK;YACzCA,CAAC,CAACC,eAAe,CAAC,CAAC,CAAC,CAAC;YACrB,IAAM2C,KAAK,GAAG,IAAIC,KAAK,CAACjD,IAAI,CAAC6C,UAAU,CAAC;YACxCG,KAAK,CAACE,IAAI,CAAC,CAAC,SAAM,CAAC,UAAA9C,CAAC,EAAI;cACpBvB,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAEsB,CAAC,CAAC;YAC5C,CAAC,CAAC;UACN,CAAC,CAAC;UAEFsC,aAAa,CAACnF,WAAW,CAACuF,WAAW,CAAC;QAC1C;QAEAP,QAAQ,CAAChF,WAAW,CAACmF,aAAa,CAAC;;QAEnC;QACAH,QAAQ,CAACtC,gBAAgB,CAAC,OAAO,EAAE,YAAM;UACrC,IAAI1B,KAAI,CAAC4E,YAAY,EAAE;;UAEvB;UACA5E,KAAI,CAAC6E,mBAAmB,CAACb,QAAQ,CAAC;;UAElC;UACAhE,KAAI,CAAC8E,aAAa,CAAC,CAAC;QACxB,CAAC,CAAC;QAEFvB,UAAU,CAACvE,WAAW,CAACgF,QAAQ,CAAC;;QAEhC;QACA,IAAMe,SAAS,GAAGvG,QAAQ,CAACE,aAAa,CAAC,KAAK,CAAC;QAC/CqG,SAAS,CAACnG,SAAS,GAAG,qBAAqB;QAC3CmG,SAAS,CAACd,OAAO,CAACC,MAAM,GAAGzC,IAAI,CAAC9C,EAAE;QAClCoG,SAAS,CAAC1F,WAAW,GAAGoC,IAAI,CAACuD,QAAQ;;QAErC;QACAD,SAAS,CAACrD,gBAAgB,CAAC,OAAO,EAAE,YAAM;UACtC,IAAI1B,KAAI,CAAC4E,YAAY,EAAE;UAEvBtE,OAAO,CAAC2E,GAAG,CAAC,oBAAoB,CAAC;;UAEjC;UACAjF,KAAI,CAACkF,oBAAoB,CAACH,SAAS,CAAC;;UAEpC;UACA/E,KAAI,CAAC8E,aAAa,CAAC,CAAC;QACxB,CAAC,CAAC;QAEFtB,WAAW,CAACxE,WAAW,CAAC+F,SAAS,CAAC;MACtC,CAAC,CAAC;;MAEF;MACA3B,iBAAiB,CAACpE,WAAW,CAACuE,UAAU,CAAC;MACzCH,iBAAiB,CAACpE,WAAW,CAACwE,WAAW,CAAC;;MAE1C;MACA,IAAI,CAACvF,SAAS,CAACe,WAAW,CAACoE,iBAAiB,CAAC;;MAE7C;MACA,IAAM+B,YAAY,GAAG3G,QAAQ,CAACE,aAAa,CAAC,QAAQ,CAAC;MACrDyG,YAAY,CAACvG,SAAS,GAAG,kBAAkB;MAC3CuG,YAAY,CAAC9F,WAAW,GAAG,gBAAgB;MAC3C8F,YAAY,CAACC,QAAQ,GAAG,IAAI;MAC5BD,YAAY,CAACzD,gBAAgB,CAAC,OAAO,EAAE,YAAM;QACzC;QACA,IAAI,OAAO1B,KAAI,CAACxC,OAAO,CAAC6H,QAAQ,KAAK,UAAU,EAAE;UAC7CrF,KAAI,CAACxC,OAAO,CAAC6H,QAAQ,CAACrF,KAAI,CAACiD,QAAQ,CAACtE,EAAE,CAAC;QAC3C;MACJ,CAAC,CAAC;MACF,IAAI,CAACV,SAAS,CAACe,WAAW,CAACmG,YAAY,CAAC;MACxC,IAAI,CAACA,YAAY,GAAGA,YAAY;;MAEhC;MACA,IAAI,CAACG,SAAS,CAAC,CAAC;;MAEhB;MACA,IAAI,IAAI,CAACV,YAAY,EAAE;QACnB,IAAI,CAACW,kBAAkB,CAACvC,KAAK,EAAEK,WAAW,CAAC;MAC/C,CAAC,MAAM;QACH;QACA,IAAI,CAACmC,mBAAmB,CAACxC,KAAK,EAAEK,WAAW,CAAC;MAChD;IACJ;;IAEA;AACJ;AACA;EAFI;IAAA/E,GAAA;IAAAC,KAAA,EAGA,SAAA+G,SAASA,CAAA,EAAG;MACR;MACA,IAAI9G,QAAQ,CAACC,cAAc,CAAC,uBAAuB,CAAC,EAAE;MAEtD,IAAMgH,OAAO,GAAGjH,QAAQ,CAACE,aAAa,CAAC,OAAO,CAAC;MAC/C+G,OAAO,CAAC9G,EAAE,GAAG,uBAAuB;MACpC8G,OAAO,CAACpG,WAAW,kmIA+HlB;MAEDb,QAAQ,CAACkH,IAAI,CAAC1G,WAAW,CAACyG,OAAO,CAAC;IACtC;;IAEA;AACJ;AACA;AACA;AACA;EAJI;IAAAnH,GAAA;IAAAC,KAAA,EAKA,SAAAsG,mBAAmBA,CAACpD,IAAI,EAAE;MACtB;MACA,IAAIA,IAAI,CAAC5C,SAAS,CAAC8G,QAAQ,CAAC,QAAQ,CAAC,IAAIlE,IAAI,CAAC5C,SAAS,CAAC8G,QAAQ,CAAC,UAAU,CAAC,EAAE;QAC1E;MACJ;;MAEA;MACA,IAAI,IAAI,CAAChC,YAAY,KAAKlC,IAAI,EAAE;QAC5BA,IAAI,CAAC5C,SAAS,CAACoD,MAAM,CAAC,UAAU,CAAC;QACjC,IAAI,CAAC0B,YAAY,GAAG,IAAI;QACxB;MACJ;;MAEA;MACA,IAAI,IAAI,CAACA,YAAY,EAAE;QACnB,IAAI,CAACA,YAAY,CAAC9E,SAAS,CAACoD,MAAM,CAAC,UAAU,CAAC;MAClD;;MAEA;MACAR,IAAI,CAAC5C,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC;MAC9B,IAAI,CAAC6E,YAAY,GAAGlC,IAAI;IAC5B;;IAEA;AACJ;AACA;AACA;AACA;EAJI;IAAAnD,GAAA;IAAAC,KAAA,EAKA,SAAA2G,oBAAoBA,CAACzD,IAAI,EAAE;MACvB;MACA,IAAIA,IAAI,CAAC5C,SAAS,CAAC8G,QAAQ,CAAC,QAAQ,CAAC,IAAIlE,IAAI,CAAC5C,SAAS,CAAC8G,QAAQ,CAAC,UAAU,CAAC,EAAE;QAC1E;MACJ;;MAEA;MACA,IAAI,IAAI,CAAC/B,aAAa,KAAKnC,IAAI,EAAE;QAC7BA,IAAI,CAAC5C,SAAS,CAACoD,MAAM,CAAC,UAAU,CAAC;QACjC,IAAI,CAAC2B,aAAa,GAAG,IAAI;QACzB;MACJ;;MAEA;MACA,IAAI,IAAI,CAACA,aAAa,EAAE;QACpB,IAAI,CAACA,aAAa,CAAC/E,SAAS,CAACoD,MAAM,CAAC,UAAU,CAAC;MACnD;;MAEA;MACAR,IAAI,CAAC5C,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC;MAC9B,IAAI,CAAC8E,aAAa,GAAGnC,IAAI;IAC7B;;IAEA;AACJ;AACA;EAFI;IAAAnD,GAAA;IAAAC,KAAA,EAGA,SAAAuG,aAAaA,CAAA,EAAG;MACZxE,OAAO,CAAC2E,GAAG,CAAC,uBAAuB,CAAC;MACpC,IAAI,CAAC,IAAI,CAACtB,YAAY,IAAI,CAAC,IAAI,CAACC,aAAa,EAAE;MAE/C,IAAMgC,MAAM,GAAG,IAAI,CAACjC,YAAY,CAACM,OAAO,CAACC,MAAM;MAC/C,IAAM2B,OAAO,GAAG,IAAI,CAACjC,aAAa,CAACK,OAAO,CAACC,MAAM;;MAEjD;MACA,IAAM4B,MAAM,GAAG,IAAI,CAACxC,aAAa,CAAC,CAAC,IAAI,CAAC,CAAC;MACzCwC,MAAM,CAACF,MAAM,CAAC,GAAGC,OAAO;MACxB,IAAI,CAACE,aAAa,CAACD,MAAM,CAAC;;MAE1B;MACA,IAAI,CAACnC,YAAY,CAAC9E,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC;MAC3C,IAAI,CAAC8E,aAAa,CAAC/E,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC;;MAE5C;MACA,IAAI,CAAC6E,YAAY,CAAC9E,SAAS,CAACC,GAAG,CAAC,QAAQ,CAAC;MACzC,IAAI,CAAC8E,aAAa,CAAC/E,SAAS,CAACC,GAAG,CAAC,QAAQ,CAAC;MAC1C,IAAI,CAAC6E,YAAY,CAAC9E,SAAS,CAACoD,MAAM,CAAC,UAAU,CAAC;MAC9C,IAAI,CAAC2B,aAAa,CAAC/E,SAAS,CAACoD,MAAM,CAAC,UAAU,CAAC;;MAE/C;MACA,IAAI,CAAC0B,YAAY,CAAC9E,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC;MAC3C,IAAI,CAAC8E,aAAa,CAAC/E,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC;;MAE5C;MACA,IAAI,CAAC6E,YAAY,GAAG,IAAI;MACxB,IAAI,CAACC,aAAa,GAAG,IAAI;;MAEzB;MACA,IAAI,CAACoC,cAAc,CAAC,CAAC;IACzB;;IAEA;AACJ;AACA;AACA;AACA;EAJI;IAAA1H,GAAA;IAAAC,KAAA,EAKA,SAAAwH,aAAaA,CAACD,MAAM,EAAE;MAClB,IAAI,CAACG,UAAU,GAAGH,MAAM;IAC5B;;IAEA;AACJ;AACA;AACA;AACA;AACA;EALI;IAAAxH,GAAA;IAAAC,KAAA,EAMA,SAAAgH,kBAAkBA,CAACvC,KAAK,EAAEK,WAAW,EAAE;MACnC,IAAM6C,SAAS,GAAG,IAAI,CAACjI,SAAS,CAACmD,gBAAgB,CAAC,2CAA2C,CAAC;MAC9F,IAAM+E,UAAU,GAAG,IAAI,CAAClI,SAAS,CAACmD,gBAAgB,CAAC,4CAA4C,CAAC;MAEhG4B,KAAK,CAACxB,OAAO,CAAC,UAAAC,IAAI,EAAI;QAClB,IAAM2E,QAAQ,GAAGC,KAAK,CAACC,IAAI,CAACJ,SAAS,CAAC,CAACK,IAAI,CAAC,UAAAC,IAAI;UAAA,OAAIA,IAAI,CAACvC,OAAO,CAACC,MAAM,KAAKzC,IAAI,CAAC9C,EAAE;QAAA,EAAC;QACpF,IAAM8H,SAAS,GAAGJ,KAAK,CAACC,IAAI,CAACH,UAAU,CAAC,CAACI,IAAI,CAAC,UAAAC,IAAI;UAAA,OAAIA,IAAI,CAACvC,OAAO,CAACC,MAAM,KAAKzC,IAAI,CAAC9C,EAAE;QAAA,EAAC;QAEtF,IAAIyH,QAAQ,IAAIK,SAAS,EAAE;UACvB,IAAMR,UAAU,GAAG5C,WAAW,CAAC5B,IAAI,CAAC9C,EAAE,CAAC;UAEvC,IAAIsH,UAAU,KAAKxE,IAAI,CAAC9C,EAAE,EAAE;YACxB;YACAyH,QAAQ,CAACvH,SAAS,CAACC,GAAG,CAAC,SAAS,CAAC;YACjC2H,SAAS,CAAC5H,SAAS,CAACC,GAAG,CAAC,SAAS,CAAC;UACtC,CAAC,MAAM,IAAImH,UAAU,EAAE;YACnB;YACAG,QAAQ,CAACvH,SAAS,CAACC,GAAG,CAAC,WAAW,CAAC;;YAEnC;YACA,IAAM4H,cAAc,GAAGL,KAAK,CAACC,IAAI,CAACH,UAAU,CAAC,CAACI,IAAI,CAAC,UAAAC,IAAI;cAAA,OAAIA,IAAI,CAACvC,OAAO,CAACC,MAAM,KAAK+B,UAAU;YAAA,EAAC;YAC9F,IAAIS,cAAc,EAAE;cAChBA,cAAc,CAAC7H,SAAS,CAACC,GAAG,CAAC,WAAW,CAAC;YAC7C;UACJ;QACJ;MACJ,CAAC,CAAC;IACN;;IAEA;AACJ;AACA;AACA;AACA;AACA;EALI;IAAAR,GAAA;IAAAC,KAAA,EAMA,SAAAiH,mBAAmBA,CAACxC,KAAK,EAAEK,WAAW,EAAE;MACpC,IAAM6C,SAAS,GAAG,IAAI,CAACjI,SAAS,CAACmD,gBAAgB,CAAC,2CAA2C,CAAC;MAC9F,IAAM+E,UAAU,GAAG,IAAI,CAAClI,SAAS,CAACmD,gBAAgB,CAAC,4CAA4C,CAAC;MAEhG,IAAI0C,WAAW,GAAG,CAAC;MAEnBd,KAAK,CAACxB,OAAO,CAAC,UAAAC,IAAI,EAAI;QAClB,IAAM2E,QAAQ,GAAGC,KAAK,CAACC,IAAI,CAACJ,SAAS,CAAC,CAACK,IAAI,CAAC,UAAAC,IAAI;UAAA,OAAIA,IAAI,CAACvC,OAAO,CAACC,MAAM,KAAKzC,IAAI,CAAC9C,EAAE;QAAA,EAAC;QACpF,IAAMsH,UAAU,GAAG5C,WAAW,CAAC5B,IAAI,CAAC9C,EAAE,CAAC;QAEvC,IAAIyH,QAAQ,IAAIH,UAAU,EAAE;UACxB,IAAMQ,SAAS,GAAGJ,KAAK,CAACC,IAAI,CAACH,UAAU,CAAC,CAACI,IAAI,CAAC,UAAAC,IAAI;YAAA,OAAIA,IAAI,CAACvC,OAAO,CAACC,MAAM,KAAK+B,UAAU;UAAA,EAAC;UAEzF,IAAIQ,SAAS,EAAE;YACX;YACAL,QAAQ,CAACvH,SAAS,CAACC,GAAG,CAAC,QAAQ,CAAC;YAChC2H,SAAS,CAAC5H,SAAS,CAACC,GAAG,CAAC,QAAQ,CAAC;YACjCsH,QAAQ,CAACvH,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC;YAClC2H,SAAS,CAAC5H,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC;YACnCgF,WAAW,EAAE;UACjB;QACJ;MACJ,CAAC,CAAC;;MAEF;MACA,IAAI,CAACA,WAAW,GAAGA,WAAW;;MAE9B;MACA,IAAI,CAACkC,cAAc,CAAC,CAAC;IACzB;;IAEA;AACJ;AACA;EAFI;IAAA1H,GAAA;IAAAC,KAAA,EAGA,SAAAyH,cAAcA,CAAA,EAAG;MACb,IAAI,IAAI,CAAClC,WAAW,IAAI,IAAI,CAACD,UAAU,EAAE;QACrC;QACA,IAAI,IAAI,CAACsB,YAAY,EAAE;UACnB,IAAI,CAACA,YAAY,CAACtG,SAAS,CAACC,GAAG,CAAC,QAAQ,CAAC;UACzC,IAAI,CAACqG,YAAY,CAACC,QAAQ,GAAG,KAAK;QACtC;MACJ,CAAC,MAAM;QACH;QACA,IAAI,IAAI,CAACD,YAAY,EAAE;UACnB,IAAI,CAACA,YAAY,CAACC,QAAQ,GAAG,IAAI;QACrC;MACJ;;MAEA;MACA,IAAI,CAACtB,WAAW,GAAG,IAAI,CAAC7F,SAAS,CAACmD,gBAAgB,CAAC,6BAA6B,CAAC,CAAC1D,MAAM,GAAG,CAAC;IAChG;;IAEA;AACJ;AACA;AACA;AACA;AACA;EALI;IAAAY,GAAA;IAAAC,KAAA,EAMA,SAAAoI,gBAAgBA,CAAC3D,KAAK,EAAE;MACpB;MACA,IAAM4D,SAAS,GAAG5D,KAAK,CAAC6D,GAAG,CAAC,UAAApF,IAAI;QAAA,OAAK;UACjC9C,EAAE,EAAE8C,IAAI,CAAC9C,EAAE;UACXqG,QAAQ,EAAEvD,IAAI,CAACuD;QACnB,CAAC;MAAA,CAAC,CAAC;;MAEH;MACA;MACA,IAAI,CAAC,IAAI,CAACJ,YAAY,EAAE;QACpB,KAAK,IAAIkC,CAAC,GAAGF,SAAS,CAAClJ,MAAM,GAAG,CAAC,EAAEoJ,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;UAC3C,IAAMC,CAAC,GAAGtG,IAAI,CAACuG,KAAK,CAACvG,IAAI,CAACC,MAAM,CAAC,CAAC,IAAIoG,CAAC,GAAG,CAAC,CAAC,CAAC;UAAC,IAAAG,IAAA,GACf,CAACL,SAAS,CAACG,CAAC,CAAC,EAAEH,SAAS,CAACE,CAAC,CAAC,CAAC;UAA1DF,SAAS,CAACE,CAAC,CAAC,GAAAG,IAAA;UAAEL,SAAS,CAACG,CAAC,CAAC,GAAAE,IAAA;QAC/B;MACJ;MAEA,OAAOL,SAAS;IACpB;EAAC;AAAA,EAvgB0BnE,yDAAgB;AA0gB/C,iEAAeC,gBAAgB,E;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClhB/B;AACA;AACA;AACA;AACA;;AAEkD;AAAA,IAE5CwE,sBAAsB,0BAAAvE,iBAAA;EAAA,SAAAuE,uBAAA;IAAAtJ,eAAA,OAAAsJ,sBAAA;IAAA,OAAAtE,UAAA,OAAAsE,sBAAA,EAAAzJ,SAAA;EAAA;EAAAoF,SAAA,CAAAqE,sBAAA,EAAAvE,iBAAA;EAAA,OAAAtE,YAAA,CAAA6I,sBAAA;IAAA5I,GAAA;IAAAC,KAAA;IACxB;AACJ;AACA;IACI,SAAAuE,MAAMA,CAAA,EAAG;MAAA,IAAA9C,KAAA;MACL,IAAI,CAAC+C,cAAc,CAAC,CAAC;;MAErB;MACA,IAAIvF,OAAO,GAAG,EAAE;MAChB,IAAI;QACAA,OAAO,GAAG,OAAO,IAAI,CAACyF,QAAQ,CAACzF,OAAO,KAAK,QAAQ,GAC/C0F,IAAI,CAACC,KAAK,CAAC,IAAI,CAACF,QAAQ,CAACzF,OAAO,CAAC,GAAG,IAAI,CAACyF,QAAQ,CAACzF,OAAO;MACjE,CAAC,CAAC,OAAOqE,CAAC,EAAE;QACRvB,OAAO,CAACC,KAAK,CAAC,iCAAiC,EAAEsB,CAAC,CAAC;QACnD;MACJ;;MAEA;MACA,IAAI,CAACrE,OAAO,IAAI,CAACA,OAAO,CAACE,MAAM,EAAE;QAC7B4C,OAAO,CAACC,KAAK,CAAC,gDAAgD,EAAE,IAAI,CAAC0C,QAAQ,CAACtE,EAAE,CAAC;QACjF;MACJ;;MAEA;MACAnB,OAAO,CAACgE,OAAO,CAAC,UAAC2F,MAAM,EAAEpD,KAAK,EAAK;QAC/B,IAAMqD,aAAa,GAAGpH,KAAI,CAACqH,mBAAmB,CAACF,MAAM,EAAEpD,KAAK,CAAC;QAC7D/D,KAAI,CAAC/B,SAAS,CAACe,WAAW,CAACoI,aAAa,CAAC;MAC7C,CAAC,CAAC;IACN;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;EANI;IAAA9I,GAAA;IAAAC,KAAA,EAOA,SAAA8I,mBAAmBA,CAACF,MAAM,EAAEpD,KAAK,EAAE;MAAA,IAAAuD,MAAA;MAC/B,IAAMF,aAAa,GAAG5I,QAAQ,CAACE,aAAa,CAAC,KAAK,CAAC;MACnD0I,aAAa,CAACxI,SAAS,GAAG,uBAAuB;MAEjD,IAAM2I,OAAO,eAAArI,MAAA,CAAe,IAAI,CAAC+D,QAAQ,CAACtE,EAAE,cAAAO,MAAA,CAAW6E,KAAK,CAAE;MAC9D,IAAMyD,SAAS,GAAG,IAAI,CAAClE,aAAa,CAAC,CAAC,KAAK6D,MAAM,CAAC5I,KAAK;MAEvD6I,aAAa,CAAC5C,SAAS,oEAAAtF,MAAA,CAENqI,OAAO,8CAAArI,MAAA,CACI,IAAI,CAAC+D,QAAQ,CAACtE,EAAE,sCAAAO,MAAA,CACxBiI,MAAM,CAAC5I,KAAK,8BAAAW,MAAA,CACnBsI,SAAS,GAAG,SAAS,GAAG,EAAE,2BAAAtI,MAAA,CAC1B,IAAI,CAAC0F,YAAY,GAAG,UAAU,GAAG,EAAE,gDAAA1F,MAAA,CAE9BqI,OAAO,SAAArI,MAAA,CAAKiI,MAAM,CAACrJ,IAAI,uBACxC;;MAED;MACA,IAAI,IAAI,CAAC8G,YAAY,IAAI,IAAI,CAAC3B,QAAQ,CAACwE,cAAc,EAAE;QACnD,IAAIN,MAAM,CAAC5I,KAAK,KAAK,IAAI,CAAC0E,QAAQ,CAACwE,cAAc,EAAE;UAC/CL,aAAa,CAACvI,SAAS,CAACC,GAAG,CAAC,SAAS,CAAC;QAC1C,CAAC,MAAM,IAAI0I,SAAS,EAAE;UAClBJ,aAAa,CAACvI,SAAS,CAACC,GAAG,CAAC,WAAW,CAAC;QAC5C;MACJ;;MAEA;MACA,IAAI,CAAC,IAAI,CAAC8F,YAAY,EAAE;QACpB,IAAM8C,KAAK,GAAGN,aAAa,CAACzH,aAAa,CAAC,OAAO,CAAC;QAClD,IAAI+H,KAAK,IAAI,OAAO,IAAI,CAAClK,OAAO,CAACmK,gBAAgB,KAAK,UAAU,EAAE;UAC9DD,KAAK,CAAChG,gBAAgB,CAAC,QAAQ,EAAE,YAAM;YACnC4F,MAAI,CAAC9J,OAAO,CAACmK,gBAAgB,CAACL,MAAI,CAACrE,QAAQ,CAACtE,EAAE,EAAEwI,MAAM,CAAC5I,KAAK,CAAC;UACjE,CAAC,CAAC;QACN;MACJ;MAEA,OAAO6I,aAAa;IACxB;EAAC;AAAA,EA3EgC3E,yDAAgB;AA8ErD,iEAAeyE,sBAAsB,E;;;;;;;;;;;;;;;;;;;;ACtFrC;AACA;AACA;AACA;AACA;AACA;AALA,IAOMzE,gBAAgB;EAClB;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,SAAAA,iBAAYQ,QAAQ,EAAEhF,SAAS,EAAgB;IAAA,IAAdT,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IAAAG,eAAA,OAAA6E,gBAAA;IACzC,IAAI,iBAAAA,gBAAA,QAAAmF,WAAA,eAAenF,gBAAgB,EAAE;MACjC,MAAM,IAAIoF,KAAK,CAAC,2EAA2E,CAAC;IAChG;IAEA,IAAI,CAAC5E,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAAChF,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACT,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACoH,YAAY,GAAGpH,OAAO,CAACoH,YAAY,IAAI,KAAK;IACjD,IAAI,CAACvB,WAAW,GAAG7F,OAAO,CAAC6F,WAAW,IAAI,CAAC,CAAC;EAChD;;EAEA;AACJ;AACA;AACA;EAHI,OAAAhF,YAAA,CAAAoE,gBAAA;IAAAnE,GAAA;IAAAC,KAAA,EAIA,SAAAuE,MAAMA,CAAA,EAAG;MACL,MAAM,IAAI+E,KAAK,CAAC,iDAAiD,CAAC;IACtE;;IAEA;AACJ;AACA;AACA;AACA;EAJI;IAAAvJ,GAAA;IAAAC,KAAA,EAKA,SAAA+E,aAAaA,CAAA,EAAG;MACZ,OAAO,IAAI,CAACD,WAAW,CAAC,IAAI,CAACJ,QAAQ,CAACtE,EAAE,CAAC,IAAI,IAAI;IACrD;;IAEA;AACJ;AACA;AACA;AACA;EAJI;IAAAL,GAAA;IAAAC,KAAA,EAKA,SAAAuJ,UAAUA,CAAA,EAAG;MACT,OAAO,IAAI,CAAC7E,QAAQ,CAACtE,EAAE,IAAI,IAAI,CAAC0E,WAAW;IAC/C;;IAEA;AACJ;AACA;EAFI;IAAA/E,GAAA;IAAAC,KAAA,EAGA,SAAAwE,cAAcA,CAAA,EAAG;MACb,IAAI,IAAI,CAAC9E,SAAS,EAAE;QAChB,IAAI,CAACA,SAAS,CAACuG,SAAS,GAAG,EAAE;MACjC;IACJ;;IAEA;AACJ;AACA;AACA;AACA;EAJI;IAAAlG,GAAA;IAAAC,KAAA,EAKA,SAAAwJ,aAAaA,CAAA,EAAG;MACZ,IAAMC,OAAO,GAAGxJ,QAAQ,CAACE,aAAa,CAAC,KAAK,CAAC;MAC7CsJ,OAAO,CAACpJ,SAAS,GAAG,wBAAwB;MAC5CoJ,OAAO,CAAC/D,OAAO,CAACgE,UAAU,GAAG,IAAI,CAAChF,QAAQ,CAACtE,EAAE;MAC7CqJ,OAAO,CAAC/D,OAAO,CAACiE,YAAY,GAAG,IAAI,CAACjF,QAAQ,CAACkF,IAAI;MACjD,OAAOH,OAAO;IAClB;EAAC;AAAA;AAGL,iEAAevF,gBAAgB,E;;;;;;;;;;;;;;;;;;;;;;;AC5E/B;AACA;AACA;AACA;AACA;;AAE8D;AACV;AACF;AAAA,IAE5C4F,uBAAuB;EAAA,SAAAA,wBAAA;IAAAzK,eAAA,OAAAyK,uBAAA;EAAA;EAAA,OAAAhK,YAAA,CAAAgK,uBAAA;IAAA/J,GAAA;IAAAC,KAAA;IACzB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI,SAAO+J,cAAcA,CAACrF,QAAQ,EAAEhF,SAAS,EAAgB;MAAA,IAAdT,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MACnD,IAAI,CAACwF,QAAQ,IAAI,CAACA,QAAQ,CAACsF,aAAa,EAAE;QACtC,MAAM,IAAIV,KAAK,CAAC,4CAA4C,CAAC;MACjE;;MAEA;MACA,QAAQ5E,QAAQ,CAACsF,aAAa,CAACC,WAAW,CAAC,CAAC;QACxC,KAAK,iBAAiB;UAClB,OAAO,IAAItB,+DAAsB,CAACjE,QAAQ,EAAEhF,SAAS,EAAET,OAAO,CAAC;QAEnE,KAAK,YAAY;QACjB,KAAK,cAAc;QACnB,KAAK,eAAe;UAChB,OAAO,IAAI4K,0DAAiB,CAACnF,QAAQ,EAAEhF,SAAS,EAAET,OAAO,CAAC;QAE9D,KAAK,UAAU;UACX,OAAO,IAAIkF,yDAAgB,CAACO,QAAQ,EAAEhF,SAAS,EAAET,OAAO,CAAC;QAE7D;UACI,MAAM,IAAIqK,KAAK,6CAAA3I,MAAA,CAA6C+D,QAAQ,CAACsF,aAAa,CAAE,CAAC;MAC7F;IACJ;;IAEA;AACJ;AACA;AACA;AACA;AACA;EALI;IAAAjK,GAAA;IAAAC,KAAA,EAMA,SAAOkK,kBAAkBA,CAACP,YAAY,EAAE;MACpC,IAAI,CAACA,YAAY,EAAE,OAAO,KAAK;MAE/B,IAAMQ,cAAc,GAAG,CACnB,iBAAiB,EACjB,YAAY,EACZ,cAAc,EACd,eAAe,EACf,UAAU,CACb;MAED,OAAOA,cAAc,CAACC,QAAQ,CAACT,YAAY,CAACM,WAAW,CAAC,CAAC,CAAC;IAC9D;EAAC;AAAA;AAGL,iEAAeH,uBAAuB,E;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChEtC;AACA;AACA;AACA;AACA;;AAEkD;AAAA,IAE5CD,iBAAiB,0BAAAzF,iBAAA;EAAA,SAAAyF,kBAAA;IAAAxK,eAAA,OAAAwK,iBAAA;IAAA,OAAAxF,UAAA,OAAAwF,iBAAA,EAAA3K,SAAA;EAAA;EAAAoF,SAAA,CAAAuF,iBAAA,EAAAzF,iBAAA;EAAA,OAAAtE,YAAA,CAAA+J,iBAAA;IAAA9J,GAAA;IAAAC,KAAA;IACnB;AACJ;AACA;IACI,SAAAuE,MAAMA,CAAA,EAAG;MAAA,IAAA9C,KAAA;MACL,IAAI,CAAC+C,cAAc,CAAC,CAAC;;MAErB;MACA,IAAMiF,OAAO,GAAG,IAAI,CAACD,aAAa,CAAC,CAAC;;MAEpC;MACA,IAAMa,cAAc,GAAGpK,QAAQ,CAACE,aAAa,CAAC,KAAK,CAAC;MACpDkK,cAAc,CAAChK,SAAS,GAAG,gCAAgC;MAE3D,IAAM2I,OAAO,eAAArI,MAAA,CAAe,IAAI,CAAC+D,QAAQ,CAACtE,EAAE,WAAQ;MACpD,IAAMsH,UAAU,GAAG,IAAI,CAAC3C,aAAa,CAAC,CAAC,IAAI,EAAE;MAE7CsF,cAAc,CAACpE,SAAS,mEAAAtF,MAAA,CAEPqI,OAAO,8FAAArI,MAAA,CAEJ+G,UAAU,2CAAA/G,MAAA,CACJ,IAAI,CAAC+D,QAAQ,CAAC4F,WAAW,IAAI,0BAA0B,6BAAA3J,MAAA,CACpE,IAAI,CAAC0F,YAAY,GAAG,UAAU,GAAG,EAAE,8BAE/C;;MAED;MACA,IAAI,IAAI,CAACA,YAAY,IAAI,IAAI,CAAC3B,QAAQ,CAACwE,cAAc,EAAE;QACnD,IAAMqB,UAAU,GAAGtK,QAAQ,CAACE,aAAa,CAAC,KAAK,CAAC;QAChDoK,UAAU,CAAClK,SAAS,GAAG,4BAA4B;QAEnD,IAAMmK,SAAS,GAAG9C,UAAU,CAACuC,WAAW,CAAC,CAAC,KAAK,IAAI,CAACvF,QAAQ,CAACwE,cAAc,CAACe,WAAW,CAAC,CAAC;QACzFM,UAAU,CAACjK,SAAS,CAACC,GAAG,CAACiK,SAAS,GAAG,SAAS,GAAG,WAAW,CAAC;QAE7DD,UAAU,CAACtE,SAAS,4HAAAtF,MAAA,CAEuB,IAAI,CAAC+D,QAAQ,CAACwE,cAAc,2CAEtE;QAEDmB,cAAc,CAAC5J,WAAW,CAAC8J,UAAU,CAAC;MAC1C;MAEAd,OAAO,CAAChJ,WAAW,CAAC4J,cAAc,CAAC;MACnC,IAAI,CAAC3K,SAAS,CAACe,WAAW,CAACgJ,OAAO,CAAC;;MAEnC;MACA,IAAI,CAAC,IAAI,CAACpD,YAAY,EAAE;QACpB,IAAM8C,KAAK,GAAGM,OAAO,CAACrI,aAAa,CAAC,OAAO,CAAC;QAC5C,IAAI+H,KAAK,IAAI,OAAO,IAAI,CAAClK,OAAO,CAACmK,gBAAgB,KAAK,UAAU,EAAE;UAC9DD,KAAK,CAAChG,gBAAgB,CAAC,QAAQ,EAAE,UAACG,CAAC,EAAK;YACpC7B,KAAI,CAACxC,OAAO,CAACmK,gBAAgB,CAAC3H,KAAI,CAACiD,QAAQ,CAACtE,EAAE,EAAEkD,CAAC,CAACmH,MAAM,CAACzK,KAAK,CAAC;UACnE,CAAC,CAAC;;UAEF;UACAmJ,KAAK,CAAChG,gBAAgB,CAAC,OAAO,EAAE,UAACG,CAAC,EAAK;YACnC7B,KAAI,CAACxC,OAAO,CAACmK,gBAAgB,CAAC3H,KAAI,CAACiD,QAAQ,CAACtE,EAAE,EAAEkD,CAAC,CAACmH,MAAM,CAACzK,KAAK,CAAC;UACnE,CAAC,CAAC;QACN;MACJ;IACJ;EAAC;AAAA,EA7D2BkE,yDAAgB;AAgEhD,iEAAe2F,iBAAiB,E;;;;;;;;;;;;;;;;;;;;;;;ACxEhC;AACA;AACA;AACA;AACA;;AAEkD;AACY;AACV;AACF;AACc;;;;;;;;;;;;;;;;;0BCThE,uKAAAvG,CAAA,EAAAoH,CAAA,EAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,WAAA,8BAAAzC,EAAAoC,CAAA,EAAAE,CAAA,EAAAE,CAAA,EAAAxC,CAAA,QAAA0C,CAAA,GAAAJ,CAAA,IAAAA,CAAA,CAAAK,SAAA,YAAAC,SAAA,GAAAN,CAAA,GAAAM,SAAA,EAAAC,CAAA,GAAAC,MAAA,CAAAC,MAAA,CAAAL,CAAA,CAAAC,SAAA,UAAAK,mBAAA,CAAAH,CAAA,uBAAAT,CAAA,EAAAE,CAAA,EAAAE,CAAA,QAAAxC,CAAA,EAAA0C,CAAA,EAAAG,CAAA,EAAAI,CAAA,MAAAC,CAAA,GAAAV,CAAA,QAAAW,CAAA,OAAAC,CAAA,KAAAF,CAAA,KAAAZ,CAAA,KAAAe,CAAA,EAAAtI,CAAA,EAAAuI,CAAA,EAAAC,CAAA,EAAAN,CAAA,EAAAM,CAAA,CAAAzI,IAAA,CAAAC,CAAA,MAAAwI,CAAA,WAAAA,EAAApB,CAAA,EAAAC,CAAA,WAAApC,CAAA,GAAAmC,CAAA,EAAAO,CAAA,MAAAG,CAAA,GAAA9H,CAAA,EAAAqI,CAAA,CAAAd,CAAA,GAAAF,CAAA,EAAAkB,CAAA,gBAAAC,EAAAnB,CAAA,EAAAE,CAAA,SAAAI,CAAA,GAAAN,CAAA,EAAAS,CAAA,GAAAP,CAAA,EAAAH,CAAA,OAAAgB,CAAA,IAAAF,CAAA,KAAAT,CAAA,IAAAL,CAAA,GAAAe,CAAA,CAAAtM,MAAA,EAAAuL,CAAA,UAAAK,CAAA,EAAAxC,CAAA,GAAAkD,CAAA,CAAAf,CAAA,GAAAoB,CAAA,GAAAH,CAAA,CAAAF,CAAA,EAAAM,CAAA,GAAAxD,CAAA,KAAAoC,CAAA,QAAAI,CAAA,GAAAgB,CAAA,KAAAlB,CAAA,MAAAO,CAAA,GAAA7C,CAAA,EAAA0C,CAAA,GAAA1C,CAAA,YAAA0C,CAAA,WAAA1C,CAAA,MAAAA,CAAA,MAAAjF,CAAA,IAAAiF,CAAA,OAAAuD,CAAA,MAAAf,CAAA,GAAAJ,CAAA,QAAAmB,CAAA,GAAAvD,CAAA,QAAA0C,CAAA,MAAAU,CAAA,CAAAC,CAAA,GAAAf,CAAA,EAAAc,CAAA,CAAAd,CAAA,GAAAtC,CAAA,OAAAuD,CAAA,GAAAC,CAAA,KAAAhB,CAAA,GAAAJ,CAAA,QAAApC,CAAA,MAAAsC,CAAA,IAAAA,CAAA,GAAAkB,CAAA,MAAAxD,CAAA,MAAAoC,CAAA,EAAApC,CAAA,MAAAsC,CAAA,EAAAc,CAAA,CAAAd,CAAA,GAAAkB,CAAA,EAAAd,CAAA,cAAAF,CAAA,IAAAJ,CAAA,aAAAkB,CAAA,QAAAH,CAAA,OAAAb,CAAA,qBAAAE,CAAA,EAAAU,CAAA,EAAAM,CAAA,QAAAP,CAAA,YAAAQ,SAAA,uCAAAN,CAAA,UAAAD,CAAA,IAAAK,CAAA,CAAAL,CAAA,EAAAM,CAAA,GAAAd,CAAA,GAAAQ,CAAA,EAAAL,CAAA,GAAAW,CAAA,GAAArB,CAAA,GAAAO,CAAA,OAAA3H,CAAA,GAAA8H,CAAA,MAAAM,CAAA,KAAAnD,CAAA,KAAA0C,CAAA,GAAAA,CAAA,QAAAA,CAAA,SAAAU,CAAA,CAAAd,CAAA,QAAAiB,CAAA,CAAAb,CAAA,EAAAG,CAAA,KAAAO,CAAA,CAAAd,CAAA,GAAAO,CAAA,GAAAO,CAAA,CAAAC,CAAA,GAAAR,CAAA,aAAAI,CAAA,MAAAjD,CAAA,QAAA0C,CAAA,KAAAF,CAAA,YAAAL,CAAA,GAAAnC,CAAA,CAAAwC,CAAA,WAAAL,CAAA,GAAAA,CAAA,CAAAuB,IAAA,CAAA1D,CAAA,EAAA6C,CAAA,UAAAY,SAAA,2CAAAtB,CAAA,CAAAwB,IAAA,SAAAxB,CAAA,EAAAU,CAAA,GAAAV,CAAA,CAAA1K,KAAA,EAAAiL,CAAA,SAAAA,CAAA,oBAAAA,CAAA,KAAAP,CAAA,GAAAnC,CAAA,eAAAmC,CAAA,CAAAuB,IAAA,CAAA1D,CAAA,GAAA0C,CAAA,SAAAG,CAAA,GAAAY,SAAA,uCAAAjB,CAAA,gBAAAE,CAAA,OAAA1C,CAAA,GAAAjF,CAAA,cAAAoH,CAAA,IAAAgB,CAAA,GAAAC,CAAA,CAAAd,CAAA,QAAAO,CAAA,GAAAT,CAAA,CAAAsB,IAAA,CAAApB,CAAA,EAAAc,CAAA,OAAAE,CAAA,kBAAAnB,CAAA,IAAAnC,CAAA,GAAAjF,CAAA,EAAA2H,CAAA,MAAAG,CAAA,GAAAV,CAAA,cAAAc,CAAA,mBAAAxL,KAAA,EAAA0K,CAAA,EAAAwB,IAAA,EAAAR,CAAA,SAAAf,CAAA,EAAAI,CAAA,EAAAxC,CAAA,QAAA6C,CAAA,QAAAS,CAAA,gBAAAV,UAAA,cAAAgB,kBAAA,cAAAC,2BAAA,KAAA1B,CAAA,GAAAW,MAAA,CAAAgB,cAAA,MAAApB,CAAA,MAAAJ,CAAA,IAAAH,CAAA,CAAAA,CAAA,IAAAG,CAAA,SAAAU,mBAAA,CAAAb,CAAA,OAAAG,CAAA,iCAAAH,CAAA,GAAAU,CAAA,GAAAgB,0BAAA,CAAAlB,SAAA,GAAAC,SAAA,CAAAD,SAAA,GAAAG,MAAA,CAAAC,MAAA,CAAAL,CAAA,YAAAO,EAAAlI,CAAA,WAAA+H,MAAA,CAAAiB,cAAA,GAAAjB,MAAA,CAAAiB,cAAA,CAAAhJ,CAAA,EAAA8I,0BAAA,KAAA9I,CAAA,CAAAiJ,SAAA,GAAAH,0BAAA,EAAAb,mBAAA,CAAAjI,CAAA,EAAAyH,CAAA,yBAAAzH,CAAA,CAAA4H,SAAA,GAAAG,MAAA,CAAAC,MAAA,CAAAF,CAAA,GAAA9H,CAAA,WAAA6I,iBAAA,CAAAjB,SAAA,GAAAkB,0BAAA,EAAAb,mBAAA,CAAAH,CAAA,iBAAAgB,0BAAA,GAAAb,mBAAA,CAAAa,0BAAA,iBAAAD,iBAAA,GAAAA,iBAAA,CAAAK,WAAA,wBAAAjB,mBAAA,CAAAa,0BAAA,EAAArB,CAAA,wBAAAQ,mBAAA,CAAAH,CAAA,GAAAG,mBAAA,CAAAH,CAAA,EAAAL,CAAA,gBAAAQ,mBAAA,CAAAH,CAAA,EAAAP,CAAA,iCAAAU,mBAAA,CAAAH,CAAA,8DAAAqB,YAAA,YAAAA,aAAA,aAAAC,CAAA,EAAAnE,CAAA,EAAAoE,CAAA,EAAAnB,CAAA;AAAA,SAAAD,oBAAAjI,CAAA,EAAAqH,CAAA,EAAAE,CAAA,EAAAH,CAAA,QAAAnC,CAAA,GAAA8C,MAAA,CAAAuB,cAAA,QAAArE,CAAA,uBAAAjF,CAAA,IAAAiF,CAAA,QAAAgD,mBAAA,YAAAsB,mBAAAvJ,CAAA,EAAAqH,CAAA,EAAAE,CAAA,EAAAH,CAAA,QAAAC,CAAA,EAAApC,CAAA,GAAAA,CAAA,CAAAjF,CAAA,EAAAqH,CAAA,IAAA3K,KAAA,EAAA6K,CAAA,EAAAiC,UAAA,GAAApC,CAAA,EAAAqC,YAAA,GAAArC,CAAA,EAAAsC,QAAA,GAAAtC,CAAA,MAAApH,CAAA,CAAAqH,CAAA,IAAAE,CAAA,YAAAE,CAAA,YAAAA,EAAAJ,CAAA,EAAAE,CAAA,IAAAU,mBAAA,CAAAjI,CAAA,EAAAqH,CAAA,YAAArH,CAAA,gBAAA2J,OAAA,CAAAtC,CAAA,EAAAE,CAAA,EAAAvH,CAAA,UAAAyH,CAAA,aAAAA,CAAA,cAAAA,CAAA,oBAAAQ,mBAAA,CAAAjI,CAAA,EAAAqH,CAAA,EAAAE,CAAA,EAAAH,CAAA;AAAA,SAAAwC,mBAAArC,CAAA,EAAAH,CAAA,EAAApH,CAAA,EAAAqH,CAAA,EAAAI,CAAA,EAAAc,CAAA,EAAAZ,CAAA,cAAA1C,CAAA,GAAAsC,CAAA,CAAAgB,CAAA,EAAAZ,CAAA,GAAAG,CAAA,GAAA7C,CAAA,CAAAvI,KAAA,WAAA6K,CAAA,gBAAAvH,CAAA,CAAAuH,CAAA,KAAAtC,CAAA,CAAA2D,IAAA,GAAAxB,CAAA,CAAAU,CAAA,IAAA1J,OAAA,CAAAC,OAAA,CAAAyJ,CAAA,EAAA+B,IAAA,CAAAxC,CAAA,EAAAI,CAAA;AAAA,SAAAqC,kBAAAvC,CAAA,6BAAAH,CAAA,SAAApH,CAAA,GAAApE,SAAA,aAAAwC,OAAA,WAAAiJ,CAAA,EAAAI,CAAA,QAAAc,CAAA,GAAAhB,CAAA,CAAAwC,KAAA,CAAA3C,CAAA,EAAApH,CAAA,YAAAgK,MAAAzC,CAAA,IAAAqC,kBAAA,CAAArB,CAAA,EAAAlB,CAAA,EAAAI,CAAA,EAAAuC,KAAA,EAAAC,MAAA,UAAA1C,CAAA,cAAA0C,OAAA1C,CAAA,IAAAqC,kBAAA,CAAArB,CAAA,EAAAlB,CAAA,EAAAI,CAAA,EAAAuC,KAAA,EAAAC,MAAA,WAAA1C,CAAA,KAAAyC,KAAA;AAAA,SAAAjO,gBAAAwM,CAAA,EAAAhB,CAAA,UAAAgB,CAAA,YAAAhB,CAAA,aAAAmB,SAAA;AAAA,SAAAwB,kBAAAlK,CAAA,EAAAqH,CAAA,aAAAD,CAAA,MAAAA,CAAA,GAAAC,CAAA,CAAAxL,MAAA,EAAAuL,CAAA,UAAAK,CAAA,GAAAJ,CAAA,CAAAD,CAAA,GAAAK,CAAA,CAAA+B,UAAA,GAAA/B,CAAA,CAAA+B,UAAA,QAAA/B,CAAA,CAAAgC,YAAA,kBAAAhC,CAAA,KAAAA,CAAA,CAAAiC,QAAA,QAAA3B,MAAA,CAAAuB,cAAA,CAAAtJ,CAAA,EAAAmK,cAAA,CAAA1C,CAAA,CAAAhL,GAAA,GAAAgL,CAAA;AAAA,SAAAjL,aAAAwD,CAAA,EAAAqH,CAAA,EAAAD,CAAA,WAAAC,CAAA,IAAA6C,iBAAA,CAAAlK,CAAA,CAAA4H,SAAA,EAAAP,CAAA,GAAAD,CAAA,IAAA8C,iBAAA,CAAAlK,CAAA,EAAAoH,CAAA,GAAAW,MAAA,CAAAuB,cAAA,CAAAtJ,CAAA,iBAAA0J,QAAA,SAAA1J,CAAA;AAAA,SAAAmK,eAAA/C,CAAA,QAAAnC,CAAA,GAAAmF,YAAA,CAAAhD,CAAA,gCAAAiD,OAAA,CAAApF,CAAA,IAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAmF,aAAAhD,CAAA,EAAAC,CAAA,oBAAAgD,OAAA,CAAAjD,CAAA,MAAAA,CAAA,SAAAA,CAAA,MAAApH,CAAA,GAAAoH,CAAA,CAAAE,MAAA,CAAAgD,WAAA,kBAAAtK,CAAA,QAAAiF,CAAA,GAAAjF,CAAA,CAAA2I,IAAA,CAAAvB,CAAA,EAAAC,CAAA,gCAAAgD,OAAA,CAAApF,CAAA,UAAAA,CAAA,YAAAyD,SAAA,yEAAArB,CAAA,GAAAkD,MAAA,GAAAC,MAAA,EAAApD,CAAA;AADA;;AAEoE;AAAA,IAE9DsD,qBAAqB;EACvB,SAAAA,sBAAAtF,IAAA,EAA6B;IAAA,IAAhBuF,aAAa,GAAAvF,IAAA,CAAbuF,aAAa;IAAA5O,eAAA,OAAA2O,qBAAA;IACtB,IAAI,CAACE,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,WAAW,GAAG,EAAE;IACrB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACL,aAAa,GAAGA,aAAa;;IAElC;IACA,IAAI,CAACM,oBAAoB,GAAG,IAAIR,sEAAoB,CAAC,CAAC;;IAEtD;IACA,IAAI,CAACS,SAAS,GAAGvO,QAAQ,CAACC,cAAc,CAAC,YAAY,CAAC;IACtD,IAAI,CAACuO,eAAe,GAAGxO,QAAQ,CAACmB,aAAa,CAAC,mBAAmB,CAAC;IAClE,IAAI,CAACsN,iBAAiB,GAAGzO,QAAQ,CAACmB,aAAa,CAAC,qBAAqB,CAAC;IACtE,IAAI,CAACuN,YAAY,GAAG1O,QAAQ,CAACmB,aAAa,CAAC,0BAA0B,CAAC;IACtE,IAAI,CAACwN,cAAc,GAAG3O,QAAQ,CAAC4C,gBAAgB,CAAC,iBAAiB,CAAC;IAElE,IAAI,CAAChD,IAAI,CAAC,CAAC;EACf;EAAC,OAAAC,YAAA,CAAAkO,qBAAA;IAAAjO,GAAA;IAAAC,KAAA,EAED,SAAAH,IAAIA,CAAA,EAAG;MAAA,IAAA4B,KAAA;MACH;MACA,IAAI,CAAC,IAAI,CAAC+M,SAAS,EAAE;QACjBzM,OAAO,CAAC8M,IAAI,CAAC,yBAAyB,CAAC;QACvC;MACJ;MAEA9M,OAAO,CAAC2E,GAAG,CAAC,qBAAqB,CAAC;;MAElC;MACA,IAAI,CAACoI,qBAAqB,CAAC,CAAC;;MAE5B;MACA,IAAI,CAACN,SAAS,CAACrL,gBAAgB,CAAC,OAAO,EAAE;QAAA,OAAM1B,KAAI,CAACsN,eAAe,CAAC,CAAC;MAAA,EAAC;MACtE,IAAI,CAACH,cAAc,CAAC3L,OAAO,CAAC,UAAA+L,MAAM;QAAA,OAAIA,MAAM,CAAC7L,gBAAgB,CAAC,OAAO,EAAE,UAACG,CAAC;UAAA,OAAK7B,KAAI,CAACwN,gBAAgB,CAAC3L,CAAC,CAAC;QAAA,EAAC;MAAA,EAAC;;MAExG;MACA,IAAI,CAAC4L,0BAA0B,CAAC,CAAC;IACrC;EAAC;IAAAnP,GAAA;IAAAC,KAAA;MAAA,IAAAmP,2BAAA,GAAA/B,iBAAA,cAAAX,YAAA,GAAAE,CAAA,CAED,SAAAyC,QAAA;QAAA,IAAAC,MAAA,EAAAC,EAAA;QAAA,OAAA7C,YAAA,GAAAC,CAAA,WAAA6C,QAAA;UAAA,kBAAAA,QAAA,CAAA9D,CAAA,GAAA8D,QAAA,CAAA1E,CAAA;YAAA;cAAA0E,QAAA,CAAA9D,CAAA;cAAA8D,QAAA,CAAA1E,CAAA;cAAA,OAE6B2E,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;gBAAExJ,KAAK,EAAE;cAAK,CAAC,CAAC;YAAA;cAAnEmJ,MAAM,GAAAE,QAAA,CAAA3D,CAAA;cACZyD,MAAM,CAACM,SAAS,CAAC,CAAC,CAAC1M,OAAO,CAAC,UAAA2M,KAAK;gBAAA,OAAIA,KAAK,CAACC,IAAI,CAAC,CAAC;cAAA,EAAC;cACjD9N,OAAO,CAAC2E,GAAG,CAAC,2BAA2B,CAAC;cAAC6I,QAAA,CAAA1E,CAAA;cAAA;YAAA;cAAA0E,QAAA,CAAA9D,CAAA;cAAA6D,EAAA,GAAAC,QAAA,CAAA3D,CAAA;cAEzC7J,OAAO,CAACC,KAAK,CAAC,2BAA2B,EAAAsN,EAAO,CAAC;cACjD,IAAI,CAACQ,SAAS,CAAC,mFAAmF,CAAC;YAAC;cAAA,OAAAP,QAAA,CAAA1D,CAAA;UAAA;QAAA,GAAAuD,OAAA;MAAA,CAE3G;MAAA,SATKF,0BAA0BA,CAAA;QAAA,OAAAC,2BAAA,CAAA9B,KAAA,OAAAnO,SAAA;MAAA;MAAA,OAA1BgQ,0BAA0B;IAAA;EAAA;IAAAnP,GAAA;IAAAC,KAAA;MAAA,IAAA+P,gBAAA,GAAA3C,iBAAA,cAAAX,YAAA,GAAAE,CAAA,CAWhC,SAAAqD,SAAA;QAAA,OAAAvD,YAAA,GAAAC,CAAA,WAAAuD,SAAA;UAAA,kBAAAA,SAAA,CAAApF,CAAA;YAAA;cAAA,KACQ,IAAI,CAACqD,WAAW;gBAAA+B,SAAA,CAAApF,CAAA;gBAAA;cAAA;cAChB,IAAI,CAACqF,aAAa,CAAC,CAAC;cAACD,SAAA,CAAApF,CAAA;cAAA;YAAA;cAAAoF,SAAA,CAAApF,CAAA;cAAA,OAEf,IAAI,CAACsF,cAAc,CAAC,CAAC;YAAA;cAAA,OAAAF,SAAA,CAAApE,CAAA;UAAA;QAAA,GAAAmE,QAAA;MAAA,CAElC;MAAA,SANKjB,eAAeA,CAAA;QAAA,OAAAgB,gBAAA,CAAA1C,KAAA,OAAAnO,SAAA;MAAA;MAAA,OAAf6P,eAAe;IAAA;EAAA;IAAAhP,GAAA;IAAAC,KAAA;MAAA,IAAAoQ,eAAA,GAAAhD,iBAAA,cAAAX,YAAA,GAAAE,CAAA,CAQrB,SAAA0D,SAAA;QAAA,IAAAtH,MAAA;QAAA,IAAAsG,MAAA,EAAAiB,QAAA,EAAAC,GAAA;QAAA,OAAA9D,YAAA,GAAAC,CAAA,WAAA8D,SAAA;UAAA,kBAAAA,SAAA,CAAA/E,CAAA,GAAA+E,SAAA,CAAA3F,CAAA;YAAA;cAAA2F,SAAA,CAAA/E,CAAA;cAAA+E,SAAA,CAAA3F,CAAA;cAAA,OAG6B2E,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;gBACrDxJ,KAAK,EAAE;kBACHuK,gBAAgB,EAAE,IAAI;kBACtBC,gBAAgB,EAAE,IAAI;kBACtBC,UAAU,EAAE,KAAK;kBAAE;kBACnBC,YAAY,EAAE,CAAC,CAAK;gBACxB;cACJ,CAAC,CAAC;YAAA;cAPIvB,MAAM,GAAAmB,SAAA,CAAA5E,CAAA;cASZ;cACI0E,QAAQ,GAAG,wBAAwB;cACvC,IAAI,CAACO,aAAa,CAACC,eAAe,CAACR,QAAQ,CAAC,EAAE;gBAC1CA,QAAQ,GAAG,YAAY;gBACvB,IAAI,CAACO,aAAa,CAACC,eAAe,CAACR,QAAQ,CAAC,EAAE;kBAC1CA,QAAQ,GAAG,WAAW;kBACtB,IAAI,CAACO,aAAa,CAACC,eAAe,CAACR,QAAQ,CAAC,EAAE;oBAC1CA,QAAQ,GAAG,EAAE,CAAC,CAAC;kBACnB;gBACJ;cACJ;cAEA,IAAI,CAACnC,aAAa,GAAG,IAAI0C,aAAa,CAACxB,MAAM,EAAE;gBAC3CiB,QAAQ,EAAEA;cACd,CAAC,CAAC;;cAEF;cACA,IAAI,CAACS,cAAc,GAAGT,QAAQ;;cAE9B;cACA,IAAI,CAAClC,WAAW,GAAG,EAAE;;cAErB;cACA,IAAI,CAACD,aAAa,CAAC6C,eAAe,GAAG,UAACrN,KAAK,EAAK;gBAC5C,IAAIA,KAAK,CAACsN,IAAI,CAACzR,IAAI,GAAG,CAAC,EAAE;kBACrBuJ,MAAI,CAACqF,WAAW,CAAC8C,IAAI,CAACvN,KAAK,CAACsN,IAAI,CAAC;gBACrC;cACJ,CAAC;cAED,IAAI,CAAC9C,aAAa,CAACgD,MAAM,GAAG,YAAM;gBAC9BpI,MAAI,CAACqI,gBAAgB,CAAC,CAAC;cAC3B,CAAC;;cAED;cACA,IAAI,CAACjD,aAAa,CAACkD,KAAK,CAAC,CAAC;cAC1B,IAAI,CAACnD,WAAW,GAAG,IAAI;cACvB,IAAI,CAACG,SAAS,GAAGiD,IAAI,CAACC,GAAG,CAAC,CAAC;;cAE3B;cACA,IAAI,CAACC,qBAAqB,CAAC,CAAC;cAC5B,IAAI,CAACC,iBAAiB,CAAC,CAAC;cACxB,IAAI,CAACC,UAAU,CAAC,CAAC;cAAClB,SAAA,CAAA3F,CAAA;cAAA;YAAA;cAAA2F,SAAA,CAAA/E,CAAA;cAAA8E,GAAA,GAAAC,SAAA,CAAA5E,CAAA;cAElB7J,OAAO,CAACC,KAAK,CAAC,2BAA2B,EAAAuO,GAAO,CAAC;cACjD,IAAI,CAACT,SAAS,CAAC,sEAAsE,CAAC;YAAC;cAAA,OAAAU,SAAA,CAAA3E,CAAA;UAAA;QAAA,GAAAwE,QAAA;MAAA,CAE9F;MAAA,SA1DKF,cAAcA,CAAA;QAAA,OAAAC,eAAA,CAAA/C,KAAA,OAAAnO,SAAA;MAAA;MAAA,OAAdiR,cAAc;IAAA;EAAA;IAAApQ,GAAA;IAAAC,KAAA,EA4DpB,SAAAkQ,aAAaA,CAAA,EAAG;MACZ,IAAI,IAAI,CAAC/B,aAAa,IAAI,IAAI,CAACD,WAAW,EAAE;QACxC,IAAI,CAACC,aAAa,CAAC0B,IAAI,CAAC,CAAC;QACzB,IAAI,CAAC3B,WAAW,GAAG,KAAK;;QAExB;QACA,IAAI,IAAI,CAACC,aAAa,CAACkB,MAAM,EAAE;UAC3B,IAAI,CAAClB,aAAa,CAACkB,MAAM,CAACM,SAAS,CAAC,CAAC,CAAC1M,OAAO,CAAC,UAAA2M,KAAK;YAAA,OAAIA,KAAK,CAACC,IAAI,CAAC,CAAC;UAAA,EAAC;QACxE;;QAEA;QACA,IAAI,CAACf,qBAAqB,CAAC,CAAC;QAC5B,IAAI,CAAC2C,iBAAiB,CAAC,CAAC;QACxB,IAAI,CAACE,SAAS,CAAC,CAAC;QAEhB5P,OAAO,CAAC2E,GAAG,CAAC,mBAAmB,CAAC;MACpC;IACJ;EAAC;IAAA3G,GAAA;IAAAC,KAAA,EAED,SAAAoR,gBAAgBA,CAAA,EAAG;MAAA,IAAAQ,MAAA;MACf,IAAI,IAAI,CAACxD,WAAW,CAACjP,MAAM,KAAK,CAAC,EAAE;QAC/B,IAAI,CAAC2Q,SAAS,CAAC,wBAAwB,CAAC;QACxC;MACJ;;MAEA;MACA,IAAM+B,SAAS,GAAG,IAAIC,IAAI,CAAC,IAAI,CAAC1D,WAAW,EAAE;QACzCxE,IAAI,EAAE,IAAI,CAACmH,cAAc,IAAI;MACjC,CAAC,CAAC;;MAEF;MACA,IAAI,CAACgB,YAAY,CAACF,SAAS,CAAC,CAAC1E,IAAI,CAAC,UAAA6E,OAAO,EAAI;QACzCJ,MAAI,CAACK,gBAAgB,CAACD,OAAO,CAAC;MAClC,CAAC,CAAC,SAAM,CAAC,UAAAhQ,KAAK,EAAI;QACdD,OAAO,CAAC8M,IAAI,CAAC,+CAA+C,EAAE7M,KAAK,CAAC;QACpE;QACA4P,MAAI,CAACK,gBAAgB,CAACJ,SAAS,CAAC;MACpC,CAAC,CAAC;IACN;EAAC;IAAA9R,GAAA;IAAAC,KAAA;MAAA,IAAAkS,aAAA,GAAA9E,iBAAA,cAAAX,YAAA,GAAAE,CAAA,CAED,SAAAwF,SAAmBN,SAAS;QAAA,IAAAO,MAAA;QAAA,OAAA3F,YAAA,GAAAC,CAAA,WAAA2F,SAAA;UAAA,kBAAAA,SAAA,CAAAxH,CAAA;YAAA;cAAA,OAAAwH,SAAA,CAAAxG,CAAA,IACjB,IAAInK,OAAO,CAAC,UAACC,OAAO,EAAE2Q,MAAM,EAAK;gBACpC,IAAMC,YAAY,GAAG,KAAKhQ,MAAM,CAACiQ,YAAY,IAAIjQ,MAAM,CAACkQ,kBAAkB,EAAE,CAAC;gBAC7E,IAAMC,UAAU,GAAG,IAAIC,UAAU,CAAC,CAAC;gBAEnCD,UAAU,CAACE,MAAM;kBAAA,IAAAC,KAAA,GAAAzF,iBAAA,cAAAX,YAAA,GAAAE,CAAA,CAAG,SAAAmG,SAAOxP,CAAC;oBAAA,IAAAyP,WAAA,EAAAC,WAAA,EAAAC,aAAA,EAAAjB,OAAA,EAAAkB,GAAA;oBAAA,OAAAzG,YAAA,GAAAC,CAAA,WAAAyG,SAAA;sBAAA,kBAAAA,SAAA,CAAA1H,CAAA,GAAA0H,SAAA,CAAAtI,CAAA;wBAAA;0BAAAsI,SAAA,CAAA1H,CAAA;0BAEpB;0BACMsH,WAAW,GAAGzP,CAAC,CAACmH,MAAM,CAAC2I,MAAM;0BAAAD,SAAA,CAAAtI,CAAA;0BAAA,OACT0H,YAAY,CAACc,eAAe,CAACN,WAAW,CAAC;wBAAA;0BAA7DC,WAAW,GAAAG,SAAA,CAAAvH,CAAA;0BAEjB;0BACMqH,aAAa,GAAGb,MAAI,CAACkB,WAAW,CAACN,WAAW,EAAET,YAAY,CAAC,EAEjE;0BACMP,OAAO,GAAGI,MAAI,CAACmB,gBAAgB,CAACN,aAAa,CAAC;0BACpDtR,OAAO,CAACqQ,OAAO,CAAC;0BAACmB,SAAA,CAAAtI,CAAA;0BAAA;wBAAA;0BAAAsI,SAAA,CAAA1H,CAAA;0BAAAyH,GAAA,GAAAC,SAAA,CAAAvH,CAAA;0BAEjB0G,MAAM,CAAAY,GAAM,CAAC;wBAAC;0BAAA,OAAAC,SAAA,CAAAtH,CAAA;sBAAA;oBAAA,GAAAiH,QAAA;kBAAA,CAErB;kBAAA,iBAAAU,GAAA;oBAAA,OAAAX,KAAA,CAAAxF,KAAA,OAAAnO,SAAA;kBAAA;gBAAA;gBAEDwT,UAAU,CAACe,OAAO,GAAG;kBAAA,OAAMnB,MAAM,CAAC,IAAIhJ,KAAK,CAAC,2BAA2B,CAAC,CAAC;gBAAA;gBACzEoJ,UAAU,CAACgB,iBAAiB,CAAC7B,SAAS,CAAC;cAC3C,CAAC,CAAC;UAAA;QAAA,GAAAM,QAAA;MAAA,CACL;MAAA,SAzBKJ,YAAYA,CAAA4B,EAAA;QAAA,OAAAzB,aAAA,CAAA7E,KAAA,OAAAnO,SAAA;MAAA;MAAA,OAAZ6S,YAAY;IAAA;IA2BlB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EAPI;IAAAhS,GAAA;IAAAC,KAAA,EAQA,SAAAsT,WAAWA,CAACN,WAAW,EAAET,YAAY,EAA2C;MAAA,IAAzCqB,gBAAgB,GAAA1U,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;MAAA,IAAE2U,QAAQ,GAAA3U,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,GAAG;MAC1E,IAAMyR,UAAU,GAAGqC,WAAW,CAACrC,UAAU;MACzC,IAAMmD,gBAAgB,GAAGd,WAAW,CAACc,gBAAgB;MACrD,IAAM3U,MAAM,GAAG6T,WAAW,CAAC7T,MAAM;;MAEjC;MACA,IAAM4U,aAAa,GAAG7R,IAAI,CAACuG,KAAK,CAAEoL,QAAQ,GAAG,IAAI,GAAIlD,UAAU,CAAC;;MAEhE;MACA,IAAMqD,SAAS,GAAGhB,WAAW,CAACiB,cAAc,CAAC,CAAC,CAAC;;MAE/C;MACA,IAAIC,UAAU,GAAG,CAAC;MAClB,KAAK,IAAI3L,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpJ,MAAM,EAAEoJ,CAAC,EAAE,EAAE;QAC7B,IAAIrG,IAAI,CAACiS,GAAG,CAACH,SAAS,CAACzL,CAAC,CAAC,CAAC,GAAGqL,gBAAgB,EAAE;UAC3CM,UAAU,GAAGhS,IAAI,CAACkS,GAAG,CAAC,CAAC,EAAE7L,CAAC,GAAGwL,aAAa,CAAC;UAC3C;QACJ;MACJ;;MAEA;MACA,IAAIM,QAAQ,GAAGlV,MAAM,GAAG,CAAC;MACzB,KAAK,IAAIoJ,EAAC,GAAGpJ,MAAM,GAAG,CAAC,EAAEoJ,EAAC,IAAI,CAAC,EAAEA,EAAC,EAAE,EAAE;QAClC,IAAIrG,IAAI,CAACiS,GAAG,CAACH,SAAS,CAACzL,EAAC,CAAC,CAAC,GAAGqL,gBAAgB,EAAE;UAC3CS,QAAQ,GAAGnS,IAAI,CAACoS,GAAG,CAACnV,MAAM,GAAG,CAAC,EAAEoJ,EAAC,GAAGwL,aAAa,CAAC;UAClD;QACJ;MACJ;;MAEA;MACA,IAAIG,UAAU,IAAIG,QAAQ,EAAE;QACxBtS,OAAO,CAAC8M,IAAI,CAAC,uDAAuD,CAAC;QACrE,IAAM0F,aAAa,GAAGrS,IAAI,CAACuG,KAAK,CAACkI,UAAU,GAAG,GAAG,CAAC,CAAC,CAAC;QACpD,IAAM6D,aAAa,GAAGjC,YAAY,CAACkC,YAAY,CAC3CX,gBAAgB,EAChBS,aAAa,EACb5D,UACJ,CAAC;QACD,OAAO6D,aAAa;MACxB;;MAEA;MACA,IAAME,SAAS,GAAGL,QAAQ,GAAGH,UAAU,GAAG,CAAC;;MAE3C;MACA,IAAMjB,aAAa,GAAGV,YAAY,CAACkC,YAAY,CAC3CX,gBAAgB,EAChBY,SAAS,EACT/D,UACJ,CAAC;;MAED;MACA,KAAK,IAAIgE,OAAO,GAAG,CAAC,EAAEA,OAAO,GAAGb,gBAAgB,EAAEa,OAAO,EAAE,EAAE;QACzD,IAAMC,YAAY,GAAG5B,WAAW,CAACiB,cAAc,CAACU,OAAO,CAAC;QACxD,IAAME,WAAW,GAAG5B,aAAa,CAACgB,cAAc,CAACU,OAAO,CAAC;QAEzD,KAAK,IAAIpM,GAAC,GAAG,CAAC,EAAEA,GAAC,GAAGmM,SAAS,EAAEnM,GAAC,EAAE,EAAE;UAChCsM,WAAW,CAACtM,GAAC,CAAC,GAAGqM,YAAY,CAACV,UAAU,GAAG3L,GAAC,CAAC;QACjD;MACJ;;MAEA;MACA,IAAMuM,gBAAgB,GAAG,CAAC3V,MAAM,GAAGwR,UAAU,GAAG,IAAI,EAAEoE,OAAO,CAAC,CAAC,CAAC;MAChE,IAAMC,eAAe,GAAG,CAACN,SAAS,GAAG/D,UAAU,GAAG,IAAI,EAAEoE,OAAO,CAAC,CAAC,CAAC;MAClE,IAAME,YAAY,GAAG,CAACf,UAAU,GAAGvD,UAAU,GAAG,IAAI,EAAEoE,OAAO,CAAC,CAAC,CAAC;MAChE,IAAMG,UAAU,GAAG,CAAC,CAAC/V,MAAM,GAAGkV,QAAQ,GAAG,CAAC,IAAI1D,UAAU,GAAG,IAAI,EAAEoE,OAAO,CAAC,CAAC,CAAC;MAE3EhT,OAAO,CAAC2E,GAAG,mBAAA/F,MAAA,CAAmBmU,gBAAgB,gBAAAnU,MAAA,CAAQqU,eAAe,kBAAArU,MAAA,CAAesU,YAAY,qBAAAtU,MAAA,CAAkBuU,UAAU,iBAAc,CAAC;MAE3I,OAAOjC,aAAa;IACxB;EAAC;IAAAlT,GAAA;IAAAC,KAAA,EAED,SAAAuT,gBAAgBA,CAAC4B,MAAM,EAAE;MACrB;MACA,IAAMC,gBAAgB,GAAG,KAAK,CAAC,CAAC;MAChC,IAAMC,cAAc,GAAG,CAAC,CAAC,CAAO;MAChC,IAAMC,aAAa,GAAG,EAAE;;MAExB;MACA,IAAMC,eAAe,GAAG,IAAI,CAACC,wBAAwB,CAACL,MAAM,EAAEC,gBAAgB,CAAC;MAE/E,IAAMjW,MAAM,GAAGoW,eAAe,CAACpW,MAAM;MACrC,IAAM2U,gBAAgB,GAAGuB,cAAc;MACvC,IAAM1E,UAAU,GAAGyE,gBAAgB;MACnC,IAAMK,cAAc,GAAGH,aAAa,GAAG,CAAC;MACxC,IAAMI,UAAU,GAAG5B,gBAAgB,GAAG2B,cAAc;MACpD,IAAME,QAAQ,GAAGhF,UAAU,GAAG+E,UAAU,CAAC,CAAC;MAC1C,IAAME,QAAQ,GAAGzW,MAAM,GAAGuW,UAAU;MACpC,IAAMG,UAAU,GAAG,EAAE,GAAGD,QAAQ;MAEhC,IAAM7C,WAAW,GAAG,IAAI+C,WAAW,CAACD,UAAU,CAAC;MAC/C,IAAME,IAAI,GAAG,IAAIC,QAAQ,CAACjD,WAAW,CAAC;;MAEtC;MACA,IAAMkD,WAAW,GAAG,SAAdA,WAAWA,CAAIC,MAAM,EAAEC,MAAM,EAAK;QACpC,KAAK,IAAI5N,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4N,MAAM,CAAChX,MAAM,EAAEoJ,CAAC,EAAE,EAAE;UACpCwN,IAAI,CAACK,QAAQ,CAACF,MAAM,GAAG3N,CAAC,EAAE4N,MAAM,CAACE,UAAU,CAAC9N,CAAC,CAAC,CAAC;QACnD;MACJ,CAAC;;MAED;MACA0N,WAAW,CAAC,CAAC,EAAE,MAAM,CAAC;MACtBF,IAAI,CAACO,SAAS,CAAC,CAAC,EAAET,UAAU,GAAG,CAAC,EAAE,IAAI,CAAC;MACvCI,WAAW,CAAC,CAAC,EAAE,MAAM,CAAC;;MAEtB;MACAA,WAAW,CAAC,EAAE,EAAE,MAAM,CAAC;MACvBF,IAAI,CAACO,SAAS,CAAC,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC;MAC9BP,IAAI,CAACQ,SAAS,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;MAC7BR,IAAI,CAACQ,SAAS,CAAC,EAAE,EAAEzC,gBAAgB,EAAE,IAAI,CAAC;MAC1CiC,IAAI,CAACO,SAAS,CAAC,EAAE,EAAE3F,UAAU,EAAE,IAAI,CAAC;MACpCoF,IAAI,CAACO,SAAS,CAAC,EAAE,EAAEX,QAAQ,EAAE,IAAI,CAAC;MAClCI,IAAI,CAACQ,SAAS,CAAC,EAAE,EAAEb,UAAU,EAAE,IAAI,CAAC;MACpCK,IAAI,CAACQ,SAAS,CAAC,EAAE,EAAEjB,aAAa,EAAE,IAAI,CAAC;;MAEvC;MACAW,WAAW,CAAC,EAAE,EAAE,MAAM,CAAC;MACvBF,IAAI,CAACO,SAAS,CAAC,EAAE,EAAEV,QAAQ,EAAE,IAAI,CAAC;;MAElC;MACA,IAAIM,MAAM,GAAG,EAAE;MACf,KAAK,IAAI3N,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpJ,MAAM,EAAEoJ,CAAC,EAAE,EAAE;QAC7B,IAAMiO,MAAM,GAAGtU,IAAI,CAACkS,GAAG,CAAC,CAAC,CAAC,EAAElS,IAAI,CAACoS,GAAG,CAAC,CAAC,EAAEiB,eAAe,CAAChN,CAAC,CAAC,CAAC,CAAC;QAC5D,IAAMkO,SAAS,GAAGD,MAAM,GAAG,CAAC,GAAGA,MAAM,GAAG,MAAM,GAAGA,MAAM,GAAG,MAAM;QAChET,IAAI,CAACW,QAAQ,CAACR,MAAM,EAAEO,SAAS,EAAE,IAAI,CAAC;QACtCP,MAAM,IAAI,CAAC;MACf;MAEA,OAAO,IAAIpE,IAAI,CAAC,CAACiB,WAAW,CAAC,EAAE;QAAEnJ,IAAI,EAAE;MAAY,CAAC,CAAC;IACzD;EAAC;IAAA7J,GAAA;IAAAC,KAAA,EAED,SAAAwV,wBAAwBA,CAACL,MAAM,EAAEC,gBAAgB,EAAE;MAC/C,IAAMuB,kBAAkB,GAAGxB,MAAM,CAACxE,UAAU;MAC5C,IAAMiG,cAAc,GAAGzB,MAAM,CAAChW,MAAM;MACpC,IAAM2U,gBAAgB,GAAGqB,MAAM,CAACrB,gBAAgB;;MAEhD;MACA,IAAM+C,aAAa,GAAGzB,gBAAgB,GAAGuB,kBAAkB;MAC3D,IAAMjC,SAAS,GAAGxS,IAAI,CAACuG,KAAK,CAACmO,cAAc,GAAGC,aAAa,CAAC;;MAE5D;MACA,IAAMC,aAAa,GAAG,IAAIC,YAAY,CAACrC,SAAS,CAAC;;MAEjD;MACA,IAAMsC,QAAQ,GAAG,IAAID,YAAY,CAACH,cAAc,CAAC;MACjD,KAAK,IAAIrO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqO,cAAc,EAAErO,CAAC,EAAE,EAAE;QACrC,IAAI0O,GAAG,GAAG,CAAC;QACX,KAAK,IAAItC,OAAO,GAAG,CAAC,EAAEA,OAAO,GAAGb,gBAAgB,EAAEa,OAAO,EAAE,EAAE;UACzDsC,GAAG,IAAI9B,MAAM,CAAClB,cAAc,CAACU,OAAO,CAAC,CAACpM,CAAC,CAAC;QAC5C;QACAyO,QAAQ,CAACzO,CAAC,CAAC,GAAG0O,GAAG,GAAGnD,gBAAgB,CAAC,CAAC;MAC1C;;MAEA;MACA,KAAK,IAAIvL,GAAC,GAAG,CAAC,EAAEA,GAAC,GAAGmM,SAAS,EAAEnM,GAAC,EAAE,EAAE;QAChC,IAAM2O,aAAa,GAAG3O,GAAC,GAAGsO,aAAa;QACvC,IAAMrR,KAAK,GAAGtD,IAAI,CAACuG,KAAK,CAACyO,aAAa,CAAC;QACvC,IAAMC,QAAQ,GAAGD,aAAa,GAAG1R,KAAK;QAEtC,IAAIA,KAAK,GAAG,CAAC,GAAGoR,cAAc,EAAE;UAC5B;UACAE,aAAa,CAACvO,GAAC,CAAC,GAAGyO,QAAQ,CAACxR,KAAK,CAAC,IAAI,CAAC,GAAG2R,QAAQ,CAAC,GAAGH,QAAQ,CAACxR,KAAK,GAAG,CAAC,CAAC,GAAG2R,QAAQ;QACxF,CAAC,MAAM;UACH;UACAL,aAAa,CAACvO,GAAC,CAAC,GAAGyO,QAAQ,CAACxR,KAAK,CAAC,IAAI,CAAC;QAC3C;MACJ;MAEA,OAAOsR,aAAa;IACxB;EAAC;IAAA/W,GAAA;IAAAC,KAAA;MAAA,IAAAoX,iBAAA,GAAAhK,iBAAA,cAAAX,YAAA,GAAAE,CAAA,CAED,SAAA0K,SAAuBxF,SAAS;QAAA,IAAAyF,MAAA,EAAAlE,MAAA,EAAAmE,GAAA;QAAA,OAAA9K,YAAA,GAAAC,CAAA,WAAA8K,SAAA;UAAA,kBAAAA,SAAA,CAAA/L,CAAA,GAAA+L,SAAA,CAAA3M,CAAA;YAAA;cAAA2M,SAAA,CAAA/L,CAAA;cAExB;cACA,IAAI,CAACgM,gBAAgB,CAAC,CAAC;;cAEvB;cACMH,MAAM,GAAG;gBACXI,QAAQ,EAAEC,aAAa,CAACD,QAAQ,IAAI,EAAE;gBAAE;gBACxCtX,EAAE,EAAEuX,aAAa,CAACvX,EAAE,IAAI,EAAE;gBAC1BwX,SAAS,EAAED,aAAa,CAACC,SAAS,IAAI;cAC1C,CAAC,EAED;cAAAJ,SAAA,CAAA3M,CAAA;cAAA,OACqB,IAAI,CAAC0D,oBAAoB,CAACsJ,cAAc,CAAChG,SAAS,EAAEyF,MAAM,CAAC;YAAA;cAA1ElE,MAAM,GAAAoE,SAAA,CAAA5L,CAAA;cAEZ;cACA,IAAI,CAACkM,cAAc,CAAC1E,MAAM,CAAC;cAACoE,SAAA,CAAA3M,CAAA;cAAA;YAAA;cAAA2M,SAAA,CAAA/L,CAAA;cAAA8L,GAAA,GAAAC,SAAA,CAAA5L,CAAA;cAG5B7J,OAAO,CAACC,KAAK,CAAC,6BAA6B,EAAAuV,GAAO,CAAC;cACnD,IAAI,CAACzH,SAAS,CAACyH,GAAA,CAAMQ,OAAO,IAAI,wCAAwC,CAAC;YAAC;cAAAP,SAAA,CAAA/L,CAAA;cAE1E,IAAI,CAACuM,gBAAgB,CAAC,CAAC;cAAC,OAAAR,SAAA,CAAAhM,CAAA;YAAA;cAAA,OAAAgM,SAAA,CAAA3L,CAAA;UAAA;QAAA,GAAAwL,QAAA;MAAA,CAE/B;MAAA,SAxBKpF,gBAAgBA,CAAAgG,GAAA;QAAA,OAAAb,iBAAA,CAAA/J,KAAA,OAAAnO,SAAA;MAAA;MAAA,OAAhB+S,gBAAgB;IAAA;EAAA;IAAAlS,GAAA;IAAAC,KAAA,EA0BtB,SAAA8X,cAAcA,CAAC7G,IAAI,EAAE;MACjB;MACA,IAAI,CAACA,IAAI,IAAI,CAACA,IAAI,CAACiH,QAAQ,EAAE;QACzB,IAAI,CAACpI,SAAS,CAAC,8CAA8C,CAAC;QAC9D;MACJ;MAEA,IAAMoI,QAAQ,GAAGjH,IAAI,CAACiH,QAAQ;MAC9B,IAAMC,MAAM,GAAGD,QAAQ,CAACC,MAAM,IAAI,CAAC,CAAC;MACpC,IAAMC,QAAQ,GAAGnH,IAAI,CAACoH,SAAS,IAAI,CAAC,CAAC;;MAErC;MACA,IAAMC,YAAY,GAAGpW,IAAI,CAACqW,KAAK,CAACL,QAAQ,CAACM,aAAa,IAAI,CAAC,CAAC;MAC5D,IAAMC,QAAQ,GAAGvW,IAAI,CAACqW,KAAK,CAACJ,MAAM,CAACM,QAAQ,IAAI,CAAC,CAAC;MACjD,IAAMC,OAAO,GAAGxW,IAAI,CAACqW,KAAK,CAACJ,MAAM,CAACO,OAAO,IAAI,CAAC,CAAC;MAC/C,IAAMC,YAAY,GAAGzW,IAAI,CAACqW,KAAK,CAACJ,MAAM,CAACQ,YAAY,IAAI,CAAC,CAAC;MACzD,IAAMC,aAAa,GAAG1W,IAAI,CAACqW,KAAK,CAACJ,MAAM,CAACS,aAAa,IAAI,CAAC,CAAC;;MAE3D;MACA,IAAMC,YAAY,GAAG5H,IAAI,CAAC6H,aAAa,IAAIV,QAAQ,CAACW,IAAI,IAAI,SAAS;MACrE,IAAMC,cAAc,GAAGd,QAAQ,CAACe,eAAe,IAAI,gBAAgB;MACnE,IAAMC,QAAQ,GAAGhB,QAAQ,CAACgB,QAAQ,IAAI,uBAAuB;;MAE7D;MACA,IAAMC,cAAc,GAAGjB,QAAQ,CAACkB,eAAe,IAAI,EAAE;MACrD,IAAMC,WAAW,GAAGnB,QAAQ,CAACmB,WAAW,IAAI,EAAE;;MAE9C;MACA,IAAIC,UAAU,GAAG,EAAE;MACnB,IAAIH,cAAc,CAACha,MAAM,GAAG,CAAC,EAAE;QAC3Bma,UAAU,0JAAA3Y,MAAA,CAIIwY,cAAc,CAAC7Q,GAAG,CAAC,UAAAiR,KAAK;UAAA,cAAA5Y,MAAA,CAAW4Y,KAAK;QAAA,CAAO,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC,sEAGtE;MACL;MAEA,IAAIC,eAAe,GAAG,EAAE;MACxB,IAAIJ,WAAW,CAACla,MAAM,GAAG,CAAC,EAAE;QACxBsa,eAAe,kKAAA9Y,MAAA,CAID0Y,WAAW,CAAC/Q,GAAG,CAAC,UAAAoR,UAAU;UAAA,cAAA/Y,MAAA,CAAW+Y,UAAU;QAAA,CAAO,CAAC,CAACF,IAAI,CAAC,EAAE,CAAC,sEAG7E;MACL;;MAEA;MACA,IAAMG,cAAc,GAAG1Z,QAAQ,CAACmB,aAAa,CAAC,kBAAkB,CAAC;MACjE,IAAIuY,cAAc,EAAE;QAChBA,cAAc,CAAC1T,SAAS,8WAAAtF,MAAA,CAOoBkY,YAAY,4OAAAlY,MAAA,CAIZqY,cAAc,6MAAArY,MAAA,CAKd,IAAI,CAACiZ,aAAa,CAACtB,YAAY,CAAC,mVAAA3X,MAAA,CAIe2X,YAAY,GAAG,GAAG,qPAAA3X,MAAA,CAGlBuB,IAAI,CAACoS,GAAG,CAAC,GAAG,EAAEgE,YAAY,GAAG,GAAG,CAAC,oOAAA3X,MAAA,CAGnD2X,YAAY,yoBAAA3X,MAAA,CAYvC,IAAI,CAACiZ,aAAa,CAACnB,QAAQ,CAAC,wBAAA9X,MAAA,CAAmB8X,QAAQ,+GAAA9X,MAAA,CAExD8X,QAAQ,2RAAA9X,MAAA,CAKP,IAAI,CAACiZ,aAAa,CAAClB,OAAO,CAAC,wBAAA/X,MAAA,CAAmB+X,OAAO,+GAAA/X,MAAA,CAEtD+X,OAAO,gSAAA/X,MAAA,CAKN,IAAI,CAACiZ,aAAa,CAACjB,YAAY,CAAC,wBAAAhY,MAAA,CAAmBgY,YAAY,+GAAAhY,MAAA,CAEhEgY,YAAY,iSAAAhY,MAAA,CAKX,IAAI,CAACiZ,aAAa,CAAChB,aAAa,CAAC,wBAAAjY,MAAA,CAAmBiY,aAAa,+GAAAjY,MAAA,CAElEiY,aAAa,4NAAAjY,MAAA,CAMzCuY,QAAQ,kFAAAvY,MAAA,CAGf2Y,UAAU,4BAAA3Y,MAAA,CACV8Y,eAAe,yYAOxB;MACL;MAEA1X,OAAO,CAAC2E,GAAG,CAAC,kCAAkC,EAAE;QAC5C4R,YAAY,EAAZA,YAAY;QACZH,MAAM,EAAE;UAAEM,QAAQ,EAARA,QAAQ;UAAEC,OAAO,EAAPA,OAAO;UAAEC,YAAY,EAAZA,YAAY;UAAEC,aAAa,EAAbA;QAAc,CAAC;QAC1DC,YAAY,EAAZA,YAAY;QACZG,cAAc,EAAdA,cAAc;QACdE,QAAQ,EAARA,QAAQ;QACRC,cAAc,EAAdA,cAAc;QACdE,WAAW,EAAXA;MACJ,CAAC,CAAC;IACN;EAAC;IAAAtZ,GAAA;IAAAC,KAAA,EAED,SAAA4Z,aAAaA,CAACC,KAAK,EAAE;MACjB,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,iBAAiB;MACzC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,YAAY;MACpC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,YAAY;MACpC,OAAO,YAAY;IACvB;EAAC;IAAA9Z,GAAA;IAAAC,KAAA,EAED,SAAAwR,qBAAqBA,CAAA,EAAG;MACpB,IAAI,IAAI,CAAC/C,eAAe,EAAE;QACtB,IAAI,CAACA,eAAe,CAACvN,KAAK,CAACC,OAAO,GAAG,OAAO;MAChD;MACA,IAAI,IAAI,CAACuN,iBAAiB,EAAE;QACxB,IAAI,CAACA,iBAAiB,CAACxN,KAAK,CAACC,OAAO,GAAG,MAAM;MACjD;IACJ;EAAC;IAAApB,GAAA;IAAAC,KAAA,EAED,SAAA8O,qBAAqBA,CAAA,EAAG;MACpB,IAAI,IAAI,CAACL,eAAe,EAAE;QACtB,IAAI,CAACA,eAAe,CAACvN,KAAK,CAACC,OAAO,GAAG,MAAM;MAC/C;MACA,IAAI,IAAI,CAACuN,iBAAiB,EAAE;QACxB,IAAI,CAACA,iBAAiB,CAACxN,KAAK,CAACC,OAAO,GAAG,MAAM;MACjD;IACJ;EAAC;IAAApB,GAAA;IAAAC,KAAA,EAED,SAAAyR,iBAAiBA,CAAA,EAAG;MAChB,IAAI,IAAI,CAACjD,SAAS,EAAE;QAChB,IAAMsL,UAAU,GAAG,IAAI,CAACtL,SAAS,CAACpN,aAAa,CAAC,cAAc,CAAC;QAC/D,IAAI,IAAI,CAAC8M,WAAW,EAAE;UAClB,IAAI,CAACM,SAAS,CAAClO,SAAS,CAACC,GAAG,CAAC,WAAW,CAAC;UACzC,IAAIuZ,UAAU,EAAEA,UAAU,CAAChZ,WAAW,GAAG,gBAAgB;QAC7D,CAAC,MAAM;UACH,IAAI,CAAC0N,SAAS,CAAClO,SAAS,CAACoD,MAAM,CAAC,WAAW,CAAC;UAC5C,IAAIoW,UAAU,EAAEA,UAAU,CAAChZ,WAAW,GAAG,YAAY;QACzD;MACJ;IACJ;EAAC;IAAAf,GAAA;IAAAC,KAAA,EAED,SAAA0R,UAAUA,CAAA,EAAG;MAAA,IAAAqI,MAAA;MACT,IAAI,CAACzL,aAAa,GAAG0L,WAAW,CAAC,YAAM;QACnC,IAAID,MAAI,CAAC1L,SAAS,IAAI0L,MAAI,CAACpL,YAAY,EAAE;UACrC,IAAMsL,OAAO,GAAG3I,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGwI,MAAI,CAAC1L,SAAS;UAC3C,IAAM6L,OAAO,GAAGhY,IAAI,CAACuG,KAAK,CAACwR,OAAO,GAAG,KAAK,CAAC;UAC3C,IAAME,OAAO,GAAGjY,IAAI,CAACuG,KAAK,CAAEwR,OAAO,GAAG,KAAK,GAAI,IAAI,CAAC;UAEpD,IAAMG,UAAU,MAAAzZ,MAAA,CAAMuZ,OAAO,CAAC9X,QAAQ,CAAC,CAAC,CAACiY,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,OAAA1Z,MAAA,CAAIwZ,OAAO,CAAC/X,QAAQ,CAAC,CAAC,CAACiY,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAE;UAClGN,MAAI,CAACpL,YAAY,CAAC7N,WAAW,GAAGsZ,UAAU;QAC9C;MACJ,CAAC,EAAE,GAAG,CAAC;IACX;EAAC;IAAAra,GAAA;IAAAC,KAAA,EAED,SAAA2R,SAASA,CAAA,EAAG;MACR,IAAI,IAAI,CAACrD,aAAa,EAAE;QACpBgM,aAAa,CAAC,IAAI,CAAChM,aAAa,CAAC;QACjC,IAAI,CAACA,aAAa,GAAG,IAAI;MAC7B;IACJ;EAAC;IAAAvO,GAAA;IAAAC,KAAA,EAED,SAAAyX,gBAAgBA,CAAA,EAAG;MACf,IAAI,IAAI,CAACjJ,SAAS,EAAE;QAChB,IAAI,CAACA,SAAS,CAAC3H,QAAQ,GAAG,IAAI;QAC9B,IAAMiT,UAAU,GAAG,IAAI,CAACtL,SAAS,CAACpN,aAAa,CAAC,cAAc,CAAC;QAC/D,IAAI0Y,UAAU,EAAEA,UAAU,CAAChZ,WAAW,GAAG,eAAe;MAC5D;IACJ;EAAC;IAAAf,GAAA;IAAAC,KAAA,EAED,SAAAgY,gBAAgBA,CAAA,EAAG;MACf,IAAI,IAAI,CAACxJ,SAAS,EAAE;QAChB,IAAI,CAACA,SAAS,CAAC3H,QAAQ,GAAG,KAAK;QAC/B,IAAI,CAAC4K,iBAAiB,CAAC,CAAC;MAC5B;IACJ;EAAC;IAAA1R,GAAA;IAAAC,KAAA,EAED,SAAA8P,SAASA,CAACiI,OAAO,EAAE;MACfhW,OAAO,CAACC,KAAK,CAAC,8BAA8B,EAAE+V,OAAO,CAAC;;MAEtD;MACA,IAAM4B,cAAc,GAAG1Z,QAAQ,CAACmB,aAAa,CAAC,kBAAkB,CAAC;MACjE,IAAIuY,cAAc,EAAE;QAChBA,cAAc,CAAC1T,SAAS,kHAAAtF,MAAA,CAGXoX,OAAO,4KAGnB;MACL;IACJ;EAAC;IAAAhY,GAAA;IAAAC,KAAA,EAED,SAAAua,cAAcA,CAAA,EAAG;MACb;MACA,IAAI,CAACrM,WAAW,GAAG,KAAK;MACxB,IAAI,CAACE,WAAW,GAAG,EAAE;MACrB,IAAI,CAACC,SAAS,GAAG,IAAI;;MAErB;MACA,IAAI,CAACsD,SAAS,CAAC,CAAC;;MAEhB;MACA,IAAI,CAAC7C,qBAAqB,CAAC,CAAC;MAC5B,IAAI,CAAC2C,iBAAiB,CAAC,CAAC;;MAExB;MACA,IAAMkI,cAAc,GAAG1Z,QAAQ,CAACmB,aAAa,CAAC,kBAAkB,CAAC;MACjE,IAAIuY,cAAc,EAAE;QAChBA,cAAc,CAAC1T,SAAS,GAAG,EAAE;MACjC;;MAEA;MACA,IAAI,IAAI,CAAC0I,YAAY,EAAE;QACnB,IAAI,CAACA,YAAY,CAAC7N,WAAW,GAAG,OAAO;MAC3C;IACJ;EAAC;IAAAf,GAAA;IAAAC,KAAA,EAED,SAAAwa,QAAQA,CAAA,EAAG;MACP;MACA,OAAOjY,MAAM,CAACkY,gBAAgB,IAAI,EAAE;IACxC;EAAC;IAAA1a,GAAA;IAAAC,KAAA,EAED,SAAAiP,gBAAgBA,CAAC3L,CAAC,EAAE;MAAA,IAAAoX,MAAA;MAChB,IAAM5U,MAAM,GAAGxC,CAAC,CAACmH,MAAM,CAAC/E,OAAO,CAACI,MAAM;MACtC,IAAM6U,OAAO,GAAG,IAAI,CAAC1M,aAAa;MAClC,IAAI,CAAC2M,aAAa,CAAC;QAAC9U,MAAM,EAANA,MAAM;QAAE6U,OAAO,EAAPA;MAAO,CAAC,CAAC,CACxCxN,IAAI,CAAC,UAAC8D,IAAI,EAAK;QACRyJ,MAAI,CAACG,cAAc,CAAC5J,IAAI,CAAC;MAC7B,CAAC,CAAC;IACN;EAAC;IAAAlR,GAAA;IAAAC,KAAA,EAED,SAAA6a,cAAcA,CAAAC,KAAA,EAAsB;MAAA,IAApBC,KAAK,GAAAD,KAAA,CAALC,KAAK;QAAErB,UAAU,GAAAoB,KAAA,CAAVpB,UAAU;MAC7B,IAAIsB,QAAQ,6GAAAra,MAAA,CAEoC+Y,UAAU,yCAEzD;MAED,IAAMuB,iBAAiB,GAAGhb,QAAQ,CAACmB,aAAa,CAAC,oBAAoB,CAAC;MACtE,IAAI6Z,iBAAiB,EAAE;QACnBA,iBAAiB,CAAChV,SAAS,IAAI+U,QAAQ;MAC3C;IACJ;EAAC;IAAAjb,GAAA;IAAAC,KAAA;MAAA,IAAAkb,cAAA,GAAA9N,iBAAA,cAAAX,YAAA,GAAAE,CAAA,CAED,SAAAwO,SAAAC,KAAA;QAAA,IAAAtV,MAAA,EAAA6U,OAAA,EAAA1J,IAAA,EAAAoK,GAAA;QAAA,OAAA5O,YAAA,GAAAC,CAAA,WAAA4O,SAAA;UAAA,kBAAAA,SAAA,CAAA7P,CAAA,GAAA6P,SAAA,CAAAzQ,CAAA;YAAA;cAAqB/E,MAAM,GAAAsV,KAAA,CAANtV,MAAM,EAAE6U,OAAO,GAAAS,KAAA,CAAPT,OAAO;cAAAW,SAAA,CAAA7P,CAAA;cAAA6P,SAAA,CAAAzQ,CAAA;cAAA,OAET,IAAI,CAAC0D,oBAAoB,CAACgN,cAAc,CAAC;gBAACzV,MAAM,EAANA,MAAM;gBAAE6U,OAAO,EAAPA;cAAO,CAAC,CAAC;YAAA;cAAxE1J,IAAI,GAAAqK,SAAA,CAAA1P,CAAA;cAAA,OAAA0P,SAAA,CAAAzP,CAAA,IACH;gBACH,YAAY,EAAEoF,IAAI,CAACyI;cACvB,CAAC;YAAA;cAAA4B,SAAA,CAAA7P,CAAA;cAAA4P,GAAA,GAAAC,SAAA,CAAA1P,CAAA;cAED7J,OAAO,CAACC,KAAK,CAAC,6BAA6B,EAAAqZ,GAAO,CAAC;YAAC;cAAA,OAAAC,SAAA,CAAAzP,CAAA;UAAA;QAAA,GAAAsP,QAAA;MAAA,CAE3D;MAAA,SATKP,aAAaA,CAAAY,GAAA;QAAA,OAAAN,cAAA,CAAA7N,KAAA,OAAAnO,SAAA;MAAA;MAAA,OAAb0b,aAAa;IAAA;EAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;AC7qBvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAE0C;AACQ;AACgB;AAAA,IAE5Dc,cAAc;EAChB;AACJ;AACA;AACA;AACA;EACI,SAAAA,eAAY5Z,OAAO,EAAE;IAAAzC,eAAA,OAAAqc,cAAA;IACjB;IACA,IAAI,CAAC5Z,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC6Z,QAAQ,GAAGpZ,MAAM,CAACqZ,aAAa,IAAI,CAAC,CAAC;IAC1C,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACH,QAAQ,CAACG,QAAQ,IAAI,EAAE;IAC5C,IAAI,CAACC,oBAAoB,GAAG,CAAC;IAC7B,IAAI,CAACjX,WAAW,GAAG,CAAC,CAAC;IACrB,IAAI,CAACkX,YAAY,GAAG,KAAK;IACzB,IAAI,CAAC3V,YAAY,GAAG,KAAK;;IAEzB;IACA,IAAI,CAAC4V,QAAQ,GAAGhc,QAAQ,CAACC,cAAc,CAAC,iBAAiB,CAAC;IAC1D,IAAI,CAACgc,YAAY,GAAGjc,QAAQ,CAACC,cAAc,CAAC,qBAAqB,CAAC;IAClE,IAAI,CAACic,YAAY,GAAGlc,QAAQ,CAACC,cAAc,CAAC,qBAAqB,CAAC;IAClE,IAAI,CAACkc,YAAY,GAAGnc,QAAQ,CAACC,cAAc,CAAC,qBAAqB,CAAC;;IAElE;IACA,IAAI,CAACmc,iBAAiB,GAAGpc,QAAQ,CAACC,cAAc,CAAC,0BAA0B,CAAC;IAC5E,IAAI,CAACoc,WAAW,GAAGrc,QAAQ,CAACC,cAAc,CAAC,oBAAoB,CAAC;IAChE,IAAI,CAACqc,YAAY,GAAGtc,QAAQ,CAACC,cAAc,CAAC,qBAAqB,CAAC;IAClE,IAAI,CAACsc,mBAAmB,GAAGvc,QAAQ,CAACC,cAAc,CAAC,4BAA4B,CAAC;IAChF,IAAI,CAACuc,cAAc,GAAGxc,QAAQ,CAACC,cAAc,CAAC,uBAAuB,CAAC;IACtE,IAAI,CAACwc,YAAY,GAAGzc,QAAQ,CAACC,cAAc,CAAC,qBAAqB,CAAC;IAClE,IAAI,CAACyc,eAAe,GAAG1c,QAAQ,CAACC,cAAc,CAAC,wBAAwB,CAAC;;IAExE;IACA,IAAI,CAAC0c,UAAU,GAAG3c,QAAQ,CAACC,cAAc,CAAC,gBAAgB,CAAC;IAC3D,IAAI,CAAC2c,UAAU,GAAG5c,QAAQ,CAACC,cAAc,CAAC,gBAAgB,CAAC;IAC3D,IAAI,CAAC0G,YAAY,GAAG3G,QAAQ,CAACC,cAAc,CAAC,kBAAkB,CAAC;IAC/D,IAAI,CAAC4c,kBAAkB,GAAG7c,QAAQ,CAACC,cAAc,CAAC,2BAA2B,CAAC;IAC9E,IAAI,CAAC6c,sBAAsB,GAAG9c,QAAQ,CAACC,cAAc,CAAC,wBAAwB,CAAC;IAC/E,IAAI,CAAC8c,qBAAqB,GAAG/c,QAAQ,CAACC,cAAc,CAAC,uBAAuB,CAAC;;IAE7E;IACA,IAAI,CAAC+c,gBAAgB,GAAGhd,QAAQ,CAACC,cAAc,CAAC,yBAAyB,CAAC;IAC1E,IAAI,CAACgd,cAAc,GAAGjd,QAAQ,CAACC,cAAc,CAAC,uBAAuB,CAAC;IACtE,IAAI,CAACid,YAAY,GAAGld,QAAQ,CAACC,cAAc,CAAC,kBAAkB,CAAC;;IAE/D;IACA,IAAI,CAACkd,SAAS,GAAG,IAAI,CAACzB,QAAQ,CAAC0B,UAAU,IAAI,IAAI,CAAC,CAAC;IACnD,IAAI,CAACC,aAAa,GAAG,IAAI,CAACF,SAAS;IACnC,IAAI,CAAC9O,aAAa,GAAG,IAAI;;IAEzB;IACA,IAAI,CAAC3O,MAAM,GAAGX,0DAAM,CAAC6C,UAAU,CAAC,IAAI,CAACC,OAAO,EAAE;MAC1CvC,IAAI,EAAE;IACV,CAAC,CAAC;EACN;;EAEA;AACJ;AACA;EAFI,OAAAO,YAAA,CAAA4b,cAAA;IAAA3b,GAAA;IAAAC,KAAA,EAGA,SAAAH,IAAIA,CAAA,EAAG;MACH;MACA,IAAI,CAAC0d,aAAa,CAAC,CAAC;;MAEpB;MACA,IAAI,CAACxa,mBAAmB,CAAC,CAAC;IAC9B;;IAEA;AACJ;AACA;EAFI;IAAAhD,GAAA;IAAAC,KAAA,EAGA,SAAAud,aAAaA,CAAA,EAAG;MAAA,IAAA9b,KAAA;MACZ,IAAI,CAAC,IAAI,CAACka,QAAQ,CAACvb,EAAE,EAAE;QACnB2B,OAAO,CAACC,KAAK,CAAC,qBAAqB,CAAC;QACpC;MACJ;MAEA,IAAI,CAACrC,MAAM,CAACqB,IAAI,CAAC,2BAA2B,CAAC;MAE7Cya,6DAAW,CAAC+B,gBAAgB,CAAC,IAAI,CAAC7B,QAAQ,CAACvb,EAAE,CAAC,CACzC+M,IAAI,CAAC,UAAA8D,IAAI,EAAI;QACVxP,KAAI,CAACoa,SAAS,GAAG5K,IAAI,CAAC4K,SAAS,IAAI,EAAE;QACrCpa,KAAI,CAACgc,wBAAwB,CAAC,CAAC;QAC/Bhc,KAAI,CAACic,SAAS,CAAC,CAAC;MACpB,CAAC,CAAC,SACI,CAAC,UAAA1b,KAAK,EAAI;QACZD,OAAO,CAACC,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD2b,KAAK,CAAC,kDAAkD,CAAC;MAC7D,CAAC,CAAC,WACM,CAAC,YAAM;QACXlc,KAAI,CAAC9B,MAAM,CAACoB,IAAI,CAAC,CAAC;MACtB,CAAC,CAAC;IACV;;IAEA;AACJ;AACA;EAFI;IAAAhB,GAAA;IAAAC,KAAA,EAGA,SAAA+C,mBAAmBA,CAAA,EAAG;MAAA,IAAAgG,MAAA;MAClB;MACA,IAAI,IAAI,CAAC6T,UAAU,EAAE;QACjB,IAAI,CAACA,UAAU,CAACzZ,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACya,oBAAoB,CAACva,IAAI,CAAC,IAAI,CAAC,CAAC;MACnF;MAEA,IAAI,IAAI,CAACwZ,UAAU,EAAE;QACjB,IAAI,CAACA,UAAU,CAAC1Z,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC0a,gBAAgB,CAACxa,IAAI,CAAC,IAAI,CAAC,CAAC;MAC/E;MAEA,IAAI,IAAI,CAACuD,YAAY,EAAE;QACnB,IAAI,CAACA,YAAY,CAACzD,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC2a,iBAAiB,CAACza,IAAI,CAAC,IAAI,CAAC,CAAC;MAClF;MAEA,IAAI,IAAI,CAAC8Z,YAAY,EAAE;QACnB,IAAI,CAACA,YAAY,CAACha,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC4a,eAAe,CAAC1a,IAAI,CAAC,IAAI,CAAC,CAAC;MAChF;;MAEA;MACAd,MAAM,CAACY,gBAAgB,CAAC,cAAc,EAAE,UAACQ,KAAK,EAAK;QAC/C,IAAIoF,MAAI,CAACiT,YAAY,IAAI,CAACjT,MAAI,CAAC1C,YAAY,EAAE;UACzC,IAAM0R,OAAO,GAAG,kEAAkE;UAClFpU,KAAK,CAACqa,WAAW,GAAGjG,OAAO;UAC3B,OAAOA,OAAO;QAClB;MACJ,CAAC,CAAC;IACN;;IAEA;AACJ;AACA;EAFI;IAAAhY,GAAA;IAAAC,KAAA,EAGA,SAAA0d,SAASA,CAAA,EAAG;MACR,IAAI,CAAC,IAAI,CAAC7B,SAAS,CAAC1c,MAAM,EAAE;QACxBwe,KAAK,CAAC,uCAAuC,CAAC;QAC9C;MACJ;MAEA,IAAI,CAAC3B,YAAY,GAAG,IAAI;MACxB,IAAI,CAACD,oBAAoB,GAAG,CAAC;MAC7B,IAAI,CAACjX,WAAW,GAAG,CAAC,CAAC;;MAErB;MACA,IAAI,CAAC4M,UAAU,CAAC,CAAC;;MAEjB;MACA,IAAI,CAACuM,mBAAmB,CAAC,CAAC;IAC9B;;IAEA;AACJ;AACA;EAFI;IAAAle,GAAA;IAAAC,KAAA,EAGA,SAAA0R,UAAUA,CAAA,EAAG;MAAA,IAAAE,MAAA;MACT;MACA,IAAI,CAAC0L,aAAa,GAAG,IAAI,CAACF,SAAS;MACnC,IAAI,CAACc,kBAAkB,CAAC,CAAC;;MAEzB;MACA,IAAI,IAAI,CAAC5P,aAAa,EAAE;QACpBgM,aAAa,CAAC,IAAI,CAAChM,aAAa,CAAC;MACrC;;MAEA;MACA,IAAMD,SAAS,GAAGiD,IAAI,CAACC,GAAG,CAAC,CAAC;MAC5B,IAAI,CAACjD,aAAa,GAAG0L,WAAW,CAAC,YAAM;QACnC,IAAMmE,cAAc,GAAGjc,IAAI,CAACuG,KAAK,CAAC,CAAC6I,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGlD,SAAS,IAAI,IAAI,CAAC;QAClEuD,MAAI,CAAC0L,aAAa,GAAGpb,IAAI,CAACkS,GAAG,CAAC,CAAC,EAAExC,MAAI,CAACwL,SAAS,GAAGe,cAAc,CAAC;QAEjEvM,MAAI,CAACsM,kBAAkB,CAAC,CAAC;;QAEzB;QACA,IAAItM,MAAI,CAAC0L,aAAa,IAAI,CAAC,EAAE;UACzBhD,aAAa,CAAC1I,MAAI,CAACtD,aAAa,CAAC;UACjCsD,MAAI,CAACwM,UAAU,CAAC,CAAC;QACrB;MACJ,CAAC,EAAE,IAAI,CAAC;IACZ;;IAEA;AACJ;AACA;EAFI;IAAAre,GAAA;IAAAC,KAAA,EAGA,SAAAke,kBAAkBA,CAAA,EAAG;MACjB;MACA,IAAMhE,OAAO,GAAGhY,IAAI,CAACuG,KAAK,CAAC,IAAI,CAAC6U,aAAa,GAAG,EAAE,CAAC;MACnD,IAAMnD,OAAO,GAAG,IAAI,CAACmD,aAAa,GAAG,EAAE;;MAEvC;MACA,IAAI,IAAI,CAACnB,YAAY,EAAE;QACnB,IAAI,CAACA,YAAY,CAACrb,WAAW,GAAGoZ,OAAO,CAAC9X,QAAQ,CAAC,CAAC,CAACiY,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MACvE;MAEA,IAAI,IAAI,CAAC+B,YAAY,EAAE;QACnB,IAAI,CAACA,YAAY,CAACtb,WAAW,GAAGqZ,OAAO,CAAC/X,QAAQ,CAAC,CAAC,CAACiY,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MACvE;;MAEA;MACA,IAAI,IAAI,CAAC4B,QAAQ,EAAE;QACf,IAAMoC,gBAAgB,GAAI,IAAI,CAACf,aAAa,GAAG,IAAI,CAACF,SAAS,GAAI,GAAG;QACpE,IAAI,CAACnB,QAAQ,CAAC/a,KAAK,CAACod,KAAK,MAAA3d,MAAA,CAAM0d,gBAAgB,MAAG;;QAElD;QACA,IAAIA,gBAAgB,GAAG,EAAE,EAAE;UACvB,IAAI,CAACpC,QAAQ,CAAC/a,KAAK,CAACqd,eAAe,GAAG,SAAS,CAAC,CAAC;QACrD,CAAC,MAAM,IAAIF,gBAAgB,GAAG,EAAE,EAAE;UAC9B,IAAI,CAACpC,QAAQ,CAAC/a,KAAK,CAACqd,eAAe,GAAG,SAAS,CAAC,CAAC;QACrD,CAAC,MAAM;UACH,IAAI,CAACtC,QAAQ,CAAC/a,KAAK,CAACqd,eAAe,GAAG,SAAS,CAAC,CAAC;QACrD;MACJ;IACJ;;IAEA;AACJ;AACA;EAFI;IAAAxe,GAAA;IAAAC,KAAA,EAGA,SAAAyd,wBAAwBA,CAAA,EAAG;MAAA,IAAArL,MAAA;MACvB,IAAI,CAAC,IAAI,CAAC0K,kBAAkB,IAAI,CAAC,IAAI,CAACjB,SAAS,CAAC1c,MAAM,EAAE;MAExD,IAAI,CAAC2d,kBAAkB,CAAC7W,SAAS,GAAG,EAAE;MAEtC,IAAI,CAAC4V,SAAS,CAAC5Y,OAAO,CAAC,UAACub,CAAC,EAAEhZ,KAAK,EAAK;QACjC,IAAMiZ,SAAS,GAAGxe,QAAQ,CAACE,aAAa,CAAC,KAAK,CAAC;QAC/Cse,SAAS,CAACpe,SAAS,GAAG,0BAA0B;QAChDoe,SAAS,CAAC/Y,OAAO,CAACF,KAAK,GAAGA,KAAK;QAC/BiZ,SAAS,CAACtb,gBAAgB,CAAC,OAAO,EAAE;UAAA,OAAMiP,MAAI,CAACsM,YAAY,CAAClZ,KAAK,CAAC;QAAA,EAAC;QAEnE4M,MAAI,CAAC0K,kBAAkB,CAACrc,WAAW,CAACge,SAAS,CAAC;MAClD,CAAC,CAAC;IACN;;IAEA;AACJ;AACA;EAFI;IAAA1e,GAAA;IAAAC,KAAA,EAGA,SAAA2e,wBAAwBA,CAAA,EAAG;MAAA,IAAA5E,MAAA;MACvB,IAAI,CAAC,IAAI,CAAC+C,kBAAkB,EAAE;MAE9B,IAAM8B,UAAU,GAAG,IAAI,CAAC9B,kBAAkB,CAACja,gBAAgB,CAAC,2BAA2B,CAAC;MAExF+b,UAAU,CAAC3b,OAAO,CAAC,UAACwb,SAAS,EAAEjZ,KAAK,EAAK;QAAA,IAAAqZ,qBAAA;QACrC;QACAJ,SAAS,CAACne,SAAS,CAACoD,MAAM,CAAC,SAAS,EAAE,UAAU,EAAE,YAAY,CAAC;;QAE/D;QACA,IAAI8B,KAAK,KAAKuU,MAAI,CAACgC,oBAAoB,EAAE;UACrC0C,SAAS,CAACne,SAAS,CAACC,GAAG,CAAC,SAAS,CAAC;QACtC;QAEA,IAAMmJ,UAAU,IAAAmV,qBAAA,GAAG9E,MAAI,CAAC8B,SAAS,CAACrW,KAAK,CAAC,cAAAqZ,qBAAA,uBAArBA,qBAAA,CAAuBze,EAAE;QAC5C,IAAIsJ,UAAU,IAAIqQ,MAAI,CAACjV,WAAW,CAAC4E,UAAU,CAAC,EAAE;UAC5C+U,SAAS,CAACne,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC;QACvC,CAAC,MAAM;UACHke,SAAS,CAACne,SAAS,CAACC,GAAG,CAAC,YAAY,CAAC;QACzC;MACJ,CAAC,CAAC;IACN;;IAEA;AACJ;AACA;EAFI;IAAAR,GAAA;IAAAC,KAAA,EAGA,SAAAie,mBAAmBA,CAAA,EAAG;MAClB,IAAI,CAAC,IAAI,CAACpC,SAAS,CAAC1c,MAAM,EAAE;MAE5B,IAAMuF,QAAQ,GAAG,IAAI,CAACmX,SAAS,CAAC,IAAI,CAACE,oBAAoB,CAAC;MAC1D,IAAI,CAACrX,QAAQ,EAAE;;MAEf;MACA,IAAI,IAAI,CAACqY,sBAAsB,EAAE;QAC7B,IAAI,CAACA,sBAAsB,CAACjc,WAAW,GAAG,CAAC,IAAI,CAACib,oBAAoB,GAAG,CAAC,EAAE3Z,QAAQ,CAAC,CAAC;MACxF;MAEA,IAAI,IAAI,CAAC4a,qBAAqB,EAAE;QAC5B,IAAI,CAACA,qBAAqB,CAAClc,WAAW,GAAG,IAAI,CAAC+a,SAAS,CAAC1c,MAAM,CAACiD,QAAQ,CAAC,CAAC;MAC7E;;MAEA;MACA,IAAM0c,OAAO,GAAG,IAAI,CAAChD,QAAQ,CAAC9T,IAAI,CAAC,UAAA+W,CAAC;QAAA,OAAIA,CAAC,CAAC3e,EAAE,KAAKsE,QAAQ,CAACsa,UAAU;MAAA,EAAC;;MAErE;MACA,IAAIF,OAAO,EAAE;QACT,IAAI,IAAI,CAACvC,YAAY,EAAE;UACnB,IAAI,CAACA,YAAY,CAACzb,WAAW,GAAGge,OAAO,CAAC/D,KAAK,IAAI,EAAE;QACvD;QAEA,IAAI,IAAI,CAACyB,mBAAmB,EAAE;UAC1B,IAAI,CAACA,mBAAmB,CAACvW,SAAS,GAAG6Y,OAAO,CAACG,YAAY,IAAI,EAAE;QACnE;MACJ;;MAEA;MACA,IAAI,IAAI,CAACxC,cAAc,EAAE;QACrB,IAAI,CAACA,cAAc,CAAC3b,WAAW,eAAAH,MAAA,CAAe,IAAI,CAACob,oBAAoB,GAAG,CAAC,CAAE;MACjF;MAEA,IAAI,IAAI,CAACW,YAAY,EAAE;QACnB,IAAI,CAACA,YAAY,CAACzW,SAAS,GAAGvB,QAAQ,CAACwa,OAAO,IAAI,EAAE;MACxD;;MAEA;MACA,IAAI,CAACC,qBAAqB,CAACza,QAAQ,CAAC;;MAEpC;MACA,IAAI,CAAC0a,uBAAuB,CAAC,CAAC;;MAE9B;MACA,IAAI,CAACT,wBAAwB,CAAC,CAAC;IACnC;;IAEA;AACJ;AACA;AACA;AACA;EAJI;IAAA5e,GAAA;IAAAC,KAAA,EAKA,SAAAmf,qBAAqBA,CAACza,QAAQ,EAAE;MAC5B,IAAI,CAAC,IAAI,CAACiY,eAAe,IAAI,CAACjY,QAAQ,EAAE;MAExC,IAAI,CAACiY,eAAe,CAAC1W,SAAS,GAAG,EAAE;MAEnC,IAAI;QACA;QACA,IAAMoZ,QAAQ,GAAGvV,0EAAuB,CAACC,cAAc,CACnDrF,QAAQ,EACR,IAAI,CAACiY,eAAe,EACpB;UACItW,YAAY,EAAE,IAAI,CAACA,YAAY;UAC/BvB,WAAW,EAAE,IAAI,CAACA,WAAW;UAC7BsE,gBAAgB,EAAE,IAAI,CAACkW,UAAU,CAACjc,IAAI,CAAC,IAAI;QAC/C,CACJ,CAAC;;QAED;QACAgc,QAAQ,CAAC9a,MAAM,CAAC,CAAC;MACrB,CAAC,CAAC,OAAOvC,KAAK,EAAE;QACZD,OAAO,CAACC,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;;QAEjD;QACA,IAAI,CAACud,sBAAsB,CAAC7a,QAAQ,CAAC;MACzC;IACJ;;IAEA;AACJ;AACA;AACA;AACA;EAJI;IAAA3E,GAAA;IAAAC,KAAA,EAKA,SAAAuf,sBAAsBA,CAAC7a,QAAQ,EAAE;MAC7B,IAAM8a,eAAe,GAAGvf,QAAQ,CAACE,aAAa,CAAC,KAAK,CAAC;MACrDqf,eAAe,CAACnf,SAAS,GAAG,yBAAyB;MACrDmf,eAAe,CAACvZ,SAAS,kGAAAtF,MAAA,CAEK+D,QAAQ,CAACkF,IAAI,IAAI,SAAS,wEAEvD;MAED,IAAI,CAAC+S,eAAe,CAAClc,WAAW,CAAC+e,eAAe,CAAC;IACrD;;IAEA;AACJ;AACA;AACA;AACA;AACA;EALI;IAAAzf,GAAA;IAAAC,KAAA,EAMA,SAAAsf,UAAUA,CAAC5V,UAAU,EAAEnC,MAAM,EAAE;MAC3B,IAAI,CAACzC,WAAW,CAAC4E,UAAU,CAAC,GAAGnC,MAAM;MACrC,IAAI,CAACoX,wBAAwB,CAAC,CAAC;IACnC;;IAEA;AACJ;AACA;EAFI;IAAA5e,GAAA;IAAAC,KAAA,EAGA,SAAAof,uBAAuBA,CAAA,EAAG;MACtB;MACA,IAAI,IAAI,CAACxC,UAAU,EAAE;QACjB,IAAI,CAACA,UAAU,CAAC/V,QAAQ,GAAG,IAAI,CAACkV,oBAAoB,KAAK,CAAC;MAC9D;;MAEA;MACA,IAAI,IAAI,CAACc,UAAU,EAAE;QACjB,IAAM4C,cAAc,GAAG,IAAI,CAAC1D,oBAAoB,KAAK,IAAI,CAACF,SAAS,CAAC1c,MAAM,GAAG,CAAC;QAC9E,IAAI,CAAC0d,UAAU,CAAC3b,KAAK,CAACC,OAAO,GAAGse,cAAc,GAAG,MAAM,GAAG,cAAc;MAC5E;;MAEA;MACA;IACJ;;IAEA;AACJ;AACA;AACA;AACA;EAJI;IAAA1f,GAAA;IAAAC,KAAA,EAKA,SAAA0e,YAAYA,CAAClZ,KAAK,EAAE;MAChB,IAAIA,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAI,IAAI,CAACqW,SAAS,CAAC1c,MAAM,EAAE;MAEjD,IAAI,CAAC4c,oBAAoB,GAAGvW,KAAK;MACjC,IAAI,CAACyY,mBAAmB,CAAC,CAAC;IAC9B;;IAEA;AACJ;AACA;EAFI;IAAAle,GAAA;IAAAC,KAAA,EAGA,SAAA4d,oBAAoBA,CAAA,EAAG;MACnB,IAAI,IAAI,CAAC7B,oBAAoB,GAAG,CAAC,EAAE;QAC/B,IAAI,CAACA,oBAAoB,EAAE;QAC3B,IAAI,CAACkC,mBAAmB,CAAC,CAAC;MAC9B;IACJ;;IAEA;AACJ;AACA;EAFI;IAAAle,GAAA;IAAAC,KAAA,EAGA,SAAA6d,gBAAgBA,CAAA,EAAG;MACf,IAAI,IAAI,CAAC9B,oBAAoB,GAAG,IAAI,CAACF,SAAS,CAAC1c,MAAM,GAAG,CAAC,EAAE;QACvD,IAAI,CAAC4c,oBAAoB,EAAE;QAC3B,IAAI,CAACkC,mBAAmB,CAAC,CAAC;MAC9B;IACJ;;IAEA;AACJ;AACA;EAFI;IAAAle,GAAA;IAAAC,KAAA,EAGA,SAAA8d,iBAAiBA,CAAA,EAAG;MAChB;MACA,IAAM4B,aAAa,GAAGrU,MAAM,CAACsU,IAAI,CAAC,IAAI,CAAC7a,WAAW,CAAC,CAAC3F,MAAM;MAC1D,IAAMygB,eAAe,GAAG,IAAI,CAAC/D,SAAS,CAAC1c,MAAM,GAAGugB,aAAa;MAE7D,IAAI3H,OAAO,GAAG,4CAA4C;MAE1D,IAAI6H,eAAe,GAAG,CAAC,EAAE;QACrB7H,OAAO,eAAApX,MAAA,CAAeif,eAAe,0BAAAjf,MAAA,CAAuBif,eAAe,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,iDAA8C;MAC5I;MAEA,IAAIC,OAAO,CAAC9H,OAAO,CAAC,EAAE;QAClB,IAAI,CAACqG,UAAU,CAAC,CAAC;MACrB;IACJ;;IAEA;AACJ;AACA;EAFI;IAAAre,GAAA;IAAAC,KAAA,EAGA,SAAAoe,UAAUA,CAAA,EAAG;MAAA,IAAA1D,MAAA;MACT;MACA,IAAI,IAAI,CAACpM,aAAa,EAAE;QACpBgM,aAAa,CAAC,IAAI,CAAChM,aAAa,CAAC;MACrC;MAEA,IAAI,CAAC0N,YAAY,GAAG,KAAK;;MAEzB;MACA,IAAI,CAACrc,MAAM,CAACqB,IAAI,CAAC,yBAAyB,CAAC;;MAE3C;MACA,IAAMiQ,IAAI,GAAG;QACT6O,OAAO,EAAE,IAAI,CAACnE,QAAQ,CAACvb,EAAE;QACzB2f,UAAU,EAAE,IAAI,CAAC3C,SAAS,GAAG,IAAI,CAACE,aAAa;QAC/C0C,OAAO,EAAE,IAAI,CAAClb;MAClB,CAAC;;MAED;MACA2W,6DAAW,CAAC2C,UAAU,CAAC,IAAI,CAACzC,QAAQ,CAACvb,EAAE,EAAE,IAAI,CAAC0E,WAAW,EAAE,IAAI,CAACsY,SAAS,GAAG,IAAI,CAACE,aAAa,CAAC,CAC1FnQ,IAAI,CAAC,UAAA1G,QAAQ,EAAI;QACdiU,MAAI,CAACuF,WAAW,CAACxZ,QAAQ,CAAC;MAC9B,CAAC,CAAC,SACI,CAAC,UAAAzE,KAAK,EAAI;QACZD,OAAO,CAACC,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C2b,KAAK,CAAC,+CAA+C,CAAC;MAC1D,CAAC,CAAC,WACM,CAAC,YAAM;QACXjD,MAAI,CAAC/a,MAAM,CAACoB,IAAI,CAAC,CAAC;MACtB,CAAC,CAAC;IACV;;IAEA;AACJ;AACA;AACA;AACA;EAJI;IAAAhB,GAAA;IAAAC,KAAA,EAKA,SAAAigB,WAAWA,CAACC,OAAO,EAAE;MACjB;MACA,IAAI,IAAI,CAAC7D,iBAAiB,EAAE;QACxB,IAAI,CAACA,iBAAiB,CAACnb,KAAK,CAACC,OAAO,GAAG,MAAM;MACjD;;MAEA;MACA,IAAI,IAAI,CAAC8b,gBAAgB,EAAE;QACvB,IAAI,CAACA,gBAAgB,CAAC/b,KAAK,CAACC,OAAO,GAAG,OAAO;MACjD;;MAEA;MACA,IAAMgf,iBAAiB,GAAGlgB,QAAQ,CAACmB,aAAa,CAAC,wBAAwB,CAAC;MAC1E,IAAI+e,iBAAiB,EAAE;QACnBA,iBAAiB,CAACjf,KAAK,CAACC,OAAO,GAAG,MAAM;MAC5C;;MAEA;MACA,IAAI,IAAI,CAAC+b,cAAc,EAAE;QACrB,IAAMrD,KAAK,GAAGqG,OAAO,CAACrG,KAAK,IAAI,CAAC;QAChC,IAAMuG,WAAW,GAAGF,OAAO,CAACG,YAAY,IAAI,IAAI,CAACxE,SAAS,CAAC1c,MAAM;QACjE,IAAMmhB,UAAU,GAAGpe,IAAI,CAACqW,KAAK,CAAEsB,KAAK,GAAGuG,WAAW,GAAI,GAAG,CAAC;QAE1D,IAAIG,WAAW,GAAG,SAAS;QAC3B,IAAID,UAAU,IAAI,EAAE,EAAE;UAClBC,WAAW,GAAG,WAAW;QAC7B,CAAC,MAAM,IAAID,UAAU,IAAI,EAAE,EAAE;UACzBC,WAAW,GAAG,MAAM;QACxB,CAAC,MAAM,IAAID,UAAU,GAAG,EAAE,EAAE;UACxBC,WAAW,GAAG,MAAM;QACxB;QAEA,IAAI,CAACrD,cAAc,CAACjX,SAAS,iDAAAtF,MAAA,CACC4f,WAAW,iEAAA5f,MAAA,CACCkZ,KAAK,OAAAlZ,MAAA,CAAIyf,WAAW,wEAAAzf,MAAA,CAChB2f,UAAU,omBAAA3f,MAAA,CAetC,IAAI,CAAC6f,mBAAmB,CAACN,OAAO,CAACO,cAAc,IAAI,EAAE,CAAC,gOAAA9f,MAAA,CAO3Duf,OAAO,CAAChH,QAAQ,IAAI,wBAAwB,+CAExD;MACL;IACJ;;IAEA;AACJ;AACA;AACA;AACA;AACA;EALI;IAAAnZ,GAAA;IAAAC,KAAA,EAMA,SAAAwgB,mBAAmBA,CAACE,aAAa,EAAE;MAC/B,IAAI,CAACA,aAAa,IAAI,CAACA,aAAa,CAACvhB,MAAM,EAAE;QACzC,OAAO,2DAA2D;MACtE;MAEA,OAAOuhB,aAAa,CAACpY,GAAG,CAAC,UAAAwW,OAAO,EAAI;QAChC,IAAMwB,UAAU,GAAGpe,IAAI,CAACqW,KAAK,CAAEuG,OAAO,CAACjF,KAAK,GAAGiF,OAAO,CAAC6B,KAAK,GAAI,GAAG,CAAC;QACpE,0DAAAhgB,MAAA,CAEcme,OAAO,CAAC/D,KAAK,qCAAApa,MAAA,CACbme,OAAO,CAACjF,KAAK,qCAAAlZ,MAAA,CACbme,OAAO,CAAC6B,KAAK,qCAAAhgB,MAAA,CACb2f,UAAU;MAG5B,CAAC,CAAC,CAAC9G,IAAI,CAAC,EAAE,CAAC;IACf;;IAEA;AACJ;AACA;EAFI;IAAAzZ,GAAA;IAAAC,KAAA,EAGA,SAAA+d,eAAeA,CAAA,EAAG;MACd,IAAI,CAAC1X,YAAY,GAAG,IAAI;;MAExB;MACA,IAAI,IAAI,CAACgW,iBAAiB,EAAE;QACxB,IAAI,CAACA,iBAAiB,CAACnb,KAAK,CAACC,OAAO,GAAG,OAAO;MAClD;;MAEA;MACA,IAAMgf,iBAAiB,GAAGlgB,QAAQ,CAACmB,aAAa,CAAC,wBAAwB,CAAC;MAC1E,IAAI+e,iBAAiB,EAAE;QACnBA,iBAAiB,CAACjf,KAAK,CAACC,OAAO,GAAG,OAAO;;QAEzC;QACA,IAAI,IAAI,CAACyF,YAAY,EAAE;UACnB,IAAI,CAACA,YAAY,CAAC1F,KAAK,CAACC,OAAO,GAAG,MAAM;QAC5C;;QAEA;QACA,IAAI,IAAI,CAAC0b,UAAU,EAAE;UACjB,IAAI,CAACA,UAAU,CAAC3b,KAAK,CAACC,OAAO,GAAG,cAAc;QAClD;MACJ;;MAEA;MACA,IAAI,IAAI,CAAC8b,gBAAgB,EAAE;QACvB,IAAI,CAACA,gBAAgB,CAAC/b,KAAK,CAACC,OAAO,GAAG,MAAM;MAChD;;MAEA;MACA,IAAI,CAAC4a,oBAAoB,GAAG,CAAC;MAC7B,IAAI,CAACkC,mBAAmB,CAAC,CAAC;IAC9B;EAAC;AAAA,KAGL;AACAhe,QAAQ,CAACkD,gBAAgB,CAAC,kBAAkB,EAAE,YAAM;EAChD,IAAMyd,iBAAiB,GAAG3gB,QAAQ,CAACC,cAAc,CAAC,mBAAmB,CAAC;EACtE,IAAI0gB,iBAAiB,EAAE;IACnB,IAAMC,cAAc,GAAG,IAAInF,cAAc,CAACkF,iBAAiB,CAAC;IAC5DC,cAAc,CAAChhB,IAAI,CAAC,CAAC;EACzB;AACJ,CAAC,CAAC;AAEF,iEAAe6b,cAAc,E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1mB7B;AACA;AACA;AACA;AACA;AACA;AALA,IAMMoF,WAAW;EACb;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,SAAAA,YAAA,EAA0B;IAAA,IAAd7hB,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IAAAG,eAAA,OAAAyhB,WAAA;IACpB;IACA,IAAMC,UAAU,GAAGxe,MAAM,CAACye,iBAAiB,IAAI,CAAC,CAAC;IAEjD,IAAI,CAACC,OAAO,GAAGhiB,OAAO,CAACgiB,OAAO,IAAIF,UAAU,CAACE,OAAO,IAAI,0BAA0B;IAClF,IAAI,CAACC,KAAK,GAAGjiB,OAAO,CAACiiB,KAAK,IAAIH,UAAU,CAACG,KAAK,IAAI,EAAE;IACpD,IAAI,CAACC,UAAU,GAAGliB,OAAO,CAACkiB,UAAU,IAAI,OAAO;IAC/C,IAAI,CAACC,OAAO,GAAGniB,OAAO,CAACmiB,OAAO,IAAI,IAAI;;IAEtC;IACA,IAAI,CAAC,IAAI,CAACH,OAAO,EAAE;MACflf,OAAO,CAACC,KAAK,CAAC,kCAAkC,CAAC;IACrD;IAEA,IAAI,CAAC,IAAI,CAACkf,KAAK,EAAE;MACbnf,OAAO,CAAC8M,IAAI,CAAC,uEAAuE,CAAC;IACzF;;IAEA;IACA,IAAI,CAACwS,eAAe,GAAG,IAAIC,GAAG,CAAC,CAAC;EACpC;;EAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EAPI,OAAAxhB,YAAA,CAAAghB,WAAA;IAAA/gB,GAAA;IAAAC,KAAA,EAQA,SAAAuhB,GAAGA,CAACC,MAAM,EAA6B;MAAA,IAA3BlK,MAAM,GAAApY,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MAAA,IAAED,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MACjC,IAAMuiB,aAAa,GAAA/e,aAAA,CAAAgf,eAAA;QACfF,MAAM,EAANA;MAAM,GACL,IAAI,CAACL,UAAU,EAAG,IAAI,CAACD,KAAK,GAC1B5J,MAAM,CACZ;MAED,IAAMqK,WAAW,GAAG,IAAIC,eAAe,CAACH,aAAa,CAAC,CAACrf,QAAQ,CAAC,CAAC;MACjE,IAAMyf,GAAG,MAAAlhB,MAAA,CAAM,IAAI,CAACsgB,OAAO,OAAAtgB,MAAA,CAAIghB,WAAW,CAAE;MAE5C,OAAO,IAAI,CAACG,YAAY,CAACD,GAAG,EAAAnf,aAAA;QACxBqf,MAAM,EAAE;MAAK,GACV9iB,OAAO,CACb,CAAC;IACN;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EAPI;IAAAc,GAAA;IAAAC,KAAA,EAQA,SAAAgiB,IAAIA,CAACR,MAAM,EAA2B;MAAA,IAAzBvQ,IAAI,GAAA/R,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MAAA,IAAED,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MAChC,IAAM+iB,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;;MAE/B;MACAD,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAEX,MAAM,CAAC;MACjCS,QAAQ,CAACE,MAAM,CAAC,IAAI,CAAChB,UAAU,EAAE,IAAI,CAACD,KAAK,CAAC;;MAE5C;MACA7V,MAAM,CAAC+W,OAAO,CAACnR,IAAI,CAAC,CAAChO,OAAO,CAAC,UAAAyF,IAAA,EAAkB;QAAA,IAAAmK,KAAA,GAAAwP,cAAA,CAAA3Z,IAAA;UAAhB3I,GAAG,GAAA8S,KAAA;UAAE7S,KAAK,GAAA6S,KAAA;QACrC;QACA,IAAI7S,KAAK,YAAY8R,IAAI,EAAE;UACvBmQ,QAAQ,CAACE,MAAM,CAACpiB,GAAG,EAAEC,KAAK,EAAEA,KAAK,CAACsiB,IAAI,OAAA3hB,MAAA,CAAOZ,GAAG,UAAO,CAAC;QAC5D;QACA;QAAA,KACK,IAAI4N,OAAA,CAAO3N,KAAK,MAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,EAAE;UAClDiiB,QAAQ,CAACE,MAAM,CAACpiB,GAAG,EAAE4E,IAAI,CAAC4d,SAAS,CAACviB,KAAK,CAAC,CAAC;QAC/C,CAAC,MAAM;UACHiiB,QAAQ,CAACE,MAAM,CAACpiB,GAAG,EAAEC,KAAK,CAAC;QAC/B;MACJ,CAAC,CAAC;MAEF,OAAO,IAAI,CAAC8hB,YAAY,CAAC,IAAI,CAACb,OAAO,EAAAve,aAAA;QACjCqf,MAAM,EAAE,MAAM;QACdvhB,IAAI,EAAEyhB;MAAQ,GACXhjB,OAAO,CACb,CAAC;IACN;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EAPI;IAAAc,GAAA;IAAAC,KAAA,EAQA,SAAAwiB,QAAQA,CAAChB,MAAM,EAA2B;MAAA,IAAzBvQ,IAAI,GAAA/R,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MAAA,IAAED,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MACpC,IAAMujB,WAAW,GAAA/f,aAAA,CAAAgf,eAAA;QACbF,MAAM,EAANA;MAAM,GACL,IAAI,CAACL,UAAU,EAAG,IAAI,CAACD,KAAK,GAC1BjQ,IAAI,CACV;MAED,OAAO,IAAI,CAAC6Q,YAAY,CAAC,IAAI,CAACb,OAAO,EAAAve,aAAA;QACjCqf,MAAM,EAAE,MAAM;QACdW,OAAO,EAAE;UACL,cAAc,EAAE;QACpB,CAAC;QACDliB,IAAI,EAAEmE,IAAI,CAAC4d,SAAS,CAACE,WAAW;MAAC,GAC9BxjB,OAAO,CACb,CAAC;IACN;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EAPI;IAAAc,GAAA;IAAAC,KAAA,EAQA,SAAA8hB,YAAYA,CAACD,GAAG,EAAE5iB,OAAO,EAAE;MAAA,IAAAwC,KAAA;MACvB;MACA,IAAMkhB,SAAS,GAAGzgB,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACwgB,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;;MAE7D;MACA,IAAMC,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;MACxC,IAAQC,MAAM,GAAKF,UAAU,CAArBE,MAAM;;MAEd;MACA,IAAMC,OAAO,GAAG/jB,OAAO,CAAC+jB,OAAO,IAAI,KAAK,CAAC,CAAC;MAC1C,IAAMC,SAAS,GAAGrhB,UAAU,CAAC,YAAM;QAC/BihB,UAAU,CAACK,KAAK,CAAC,CAAC;MACtB,CAAC,EAAEF,OAAO,CAAC;;MAEX;MACA,IAAI,CAAC3B,eAAe,CAAC8B,GAAG,CAACR,SAAS,EAAE;QAAEE,UAAU,EAAVA;MAAW,CAAC,CAAC;;MAEnD;MACA,IAAMljB,MAAM,GAAGV,OAAO,CAACU,MAAM,IAAK4C,MAAM,CAAC6gB,WAAW,IAAI,IAAK;MAC7D,IAAMC,UAAU,GAAGpkB,OAAO,CAACokB,UAAU,KAAK,KAAK;MAE/C,IAAI1jB,MAAM,IAAI0jB,UAAU,EAAE;QACtB1jB,MAAM,CAACqB,IAAI,CAAC/B,OAAO,CAACqkB,UAAU,IAAI,YAAY,CAAC;MACnD;;MAEA;MACA,OAAOC,KAAK,CAAC1B,GAAG,EAAAnf,aAAA,CAAAA,aAAA,KACTzD,OAAO;QACV8jB,MAAM,EAANA,MAAM;QACNS,WAAW,EAAE;MAAa,EAC7B,CAAC,CACDrW,IAAI,CAAC,UAAA1G,QAAQ,EAAI;QACd;QACAgd,YAAY,CAACR,SAAS,CAAC;;QAEvB;QACA,IAAI,CAACxc,QAAQ,CAACid,EAAE,EAAE;UACd,MAAM,IAAIpa,KAAK,eAAA3I,MAAA,CAAe8F,QAAQ,CAACkd,MAAM,QAAAhjB,MAAA,CAAK8F,QAAQ,CAACmd,UAAU,CAAE,CAAC;QAC5E;;QAEA;QACA,IAAMC,WAAW,GAAGpd,QAAQ,CAACic,OAAO,CAACnB,GAAG,CAAC,cAAc,CAAC;QACxD,IAAIsC,WAAW,IAAIA,WAAW,CAACzZ,QAAQ,CAAC,kBAAkB,CAAC,EAAE;UACzD,OAAO3D,QAAQ,CAACqd,IAAI,CAAC,CAAC;QAC1B;QAEA,OAAOrd,QAAQ,CAAClH,IAAI,CAAC,CAAC;MAC1B,CAAC,CAAC,CACD4N,IAAI,CAAC,UAAA8D,IAAI,EAAI;QACV;QACA,IAAItD,OAAA,CAAOsD,IAAI,MAAK,QAAQ,IAAIA,IAAI,KAAK,IAAI,EAAE;UAC3C;UACA,IAAIA,IAAI,CAAC8S,OAAO,KAAK,KAAK,EAAE;YACxB,MAAM,IAAIza,KAAK,CAAC2H,IAAI,CAACA,IAAI,IAAI,eAAe,CAAC;UACjD;;UAEA;UACA,OAAOA,IAAI,CAACA,IAAI,KAAK7R,SAAS,GAAG6R,IAAI,CAACA,IAAI,GAAGA,IAAI;QACrD;QAEA,OAAOA,IAAI;MACf,CAAC,CAAC,SACI,CAAC,UAAAjP,KAAK,EAAI;QACZ;QACA,IAAIA,KAAK,CAACsgB,IAAI,KAAK,YAAY,EAAE;UAC7B,MAAM,IAAIhZ,KAAK,CAAC,mBAAmB,CAAC;QACxC;;QAEA;QACA,IAAI7H,KAAI,CAAC2f,OAAO,EAAE;UACd3f,KAAI,CAAC2f,OAAO,CAACpf,KAAK,CAAC;QACvB;QAEA,MAAMA,KAAK;MACf,CAAC,CAAC,WACM,CAAC,YAAM;QACX;QACAP,KAAI,CAAC4f,eAAe,UAAO,CAACsB,SAAS,CAAC;;QAEtC;QACA,IAAIhjB,MAAM,IAAI0jB,UAAU,EAAE;UACtB;UACA,IAAMW,uBAAuB,GAAGlc,KAAK,CAACC,IAAI,CAACtG,KAAI,CAAC4f,eAAe,CAAC4C,MAAM,CAAC,CAAC,CAAC,CACpEC,IAAI,CAAC,UAAAC,GAAG;YAAA,OAAIA,GAAG,CAACxkB,MAAM,KAAKA,MAAM;UAAA,EAAC;UAEvC,IAAI,CAACqkB,uBAAuB,EAAE;YAC1BrkB,MAAM,CAACoB,IAAI,CAAC,CAAC;UACjB;QACJ;MACJ,CAAC,CAAC;IACN;;IAEA;AACJ;AACA;EAFI;IAAAhB,GAAA;IAAAC,KAAA,EAGA,SAAAokB,SAASA,CAAA,EAAG;MACR,IAAI,CAAC/C,eAAe,CAACpe,OAAO,CAAC,UAAAohB,OAAO,EAAI;QACpCA,OAAO,CAACxB,UAAU,CAACK,KAAK,CAAC,CAAC;MAC9B,CAAC,CAAC;MAEF,IAAI,CAAC7B,eAAe,CAACiD,KAAK,CAAC,CAAC;IAChC;;IAEA;AACJ;AACA;AACA;AACA;EAJI;IAAAvkB,GAAA;IAAAC,KAAA,EAKA,SAAAukB,WAAWA,CAACrD,KAAK,EAAE;MACf,IAAI,CAACA,KAAK,GAAGA,KAAK;IACtB;;IAEA;AACJ;AACA;AACA;AACA;AACA;EALI;IAAAnhB,GAAA;IAAAC,KAAA,EAMA,SAAOsL,MAAMA,CAAA,EAAe;MAAA,IAAdrM,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MACtB,OAAO,IAAI4hB,WAAW,CAAC7hB,OAAO,CAAC;IACnC;EAAC;AAAA,KAGL;AACA,IAAMulB,WAAW,GAAG,IAAI1D,WAAW,CAAC,CAAC;AAErC,iEAAe0D,WAAW,EAAC;;AAE3B;;;;;;;;;;;;;;;;;0BClQA,uKAAAlhB,CAAA,EAAAoH,CAAA,EAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,WAAA,8BAAAzC,EAAAoC,CAAA,EAAAE,CAAA,EAAAE,CAAA,EAAAxC,CAAA,QAAA0C,CAAA,GAAAJ,CAAA,IAAAA,CAAA,CAAAK,SAAA,YAAAC,SAAA,GAAAN,CAAA,GAAAM,SAAA,EAAAC,CAAA,GAAAC,MAAA,CAAAC,MAAA,CAAAL,CAAA,CAAAC,SAAA,UAAAK,mBAAA,CAAAH,CAAA,uBAAAT,CAAA,EAAAE,CAAA,EAAAE,CAAA,QAAAxC,CAAA,EAAA0C,CAAA,EAAAG,CAAA,EAAAI,CAAA,MAAAC,CAAA,GAAAV,CAAA,QAAAW,CAAA,OAAAC,CAAA,KAAAF,CAAA,KAAAZ,CAAA,KAAAe,CAAA,EAAAtI,CAAA,EAAAuI,CAAA,EAAAC,CAAA,EAAAN,CAAA,EAAAM,CAAA,CAAAzI,IAAA,CAAAC,CAAA,MAAAwI,CAAA,WAAAA,EAAApB,CAAA,EAAAC,CAAA,WAAApC,CAAA,GAAAmC,CAAA,EAAAO,CAAA,MAAAG,CAAA,GAAA9H,CAAA,EAAAqI,CAAA,CAAAd,CAAA,GAAAF,CAAA,EAAAkB,CAAA,gBAAAC,EAAAnB,CAAA,EAAAE,CAAA,SAAAI,CAAA,GAAAN,CAAA,EAAAS,CAAA,GAAAP,CAAA,EAAAH,CAAA,OAAAgB,CAAA,IAAAF,CAAA,KAAAT,CAAA,IAAAL,CAAA,GAAAe,CAAA,CAAAtM,MAAA,EAAAuL,CAAA,UAAAK,CAAA,EAAAxC,CAAA,GAAAkD,CAAA,CAAAf,CAAA,GAAAoB,CAAA,GAAAH,CAAA,CAAAF,CAAA,EAAAM,CAAA,GAAAxD,CAAA,KAAAoC,CAAA,QAAAI,CAAA,GAAAgB,CAAA,KAAAlB,CAAA,MAAAO,CAAA,GAAA7C,CAAA,EAAA0C,CAAA,GAAA1C,CAAA,YAAA0C,CAAA,WAAA1C,CAAA,MAAAA,CAAA,MAAAjF,CAAA,IAAAiF,CAAA,OAAAuD,CAAA,MAAAf,CAAA,GAAAJ,CAAA,QAAAmB,CAAA,GAAAvD,CAAA,QAAA0C,CAAA,MAAAU,CAAA,CAAAC,CAAA,GAAAf,CAAA,EAAAc,CAAA,CAAAd,CAAA,GAAAtC,CAAA,OAAAuD,CAAA,GAAAC,CAAA,KAAAhB,CAAA,GAAAJ,CAAA,QAAApC,CAAA,MAAAsC,CAAA,IAAAA,CAAA,GAAAkB,CAAA,MAAAxD,CAAA,MAAAoC,CAAA,EAAApC,CAAA,MAAAsC,CAAA,EAAAc,CAAA,CAAAd,CAAA,GAAAkB,CAAA,EAAAd,CAAA,cAAAF,CAAA,IAAAJ,CAAA,aAAAkB,CAAA,QAAAH,CAAA,OAAAb,CAAA,qBAAAE,CAAA,EAAAU,CAAA,EAAAM,CAAA,QAAAP,CAAA,YAAAQ,SAAA,uCAAAN,CAAA,UAAAD,CAAA,IAAAK,CAAA,CAAAL,CAAA,EAAAM,CAAA,GAAAd,CAAA,GAAAQ,CAAA,EAAAL,CAAA,GAAAW,CAAA,GAAArB,CAAA,GAAAO,CAAA,OAAA3H,CAAA,GAAA8H,CAAA,MAAAM,CAAA,KAAAnD,CAAA,KAAA0C,CAAA,GAAAA,CAAA,QAAAA,CAAA,SAAAU,CAAA,CAAAd,CAAA,QAAAiB,CAAA,CAAAb,CAAA,EAAAG,CAAA,KAAAO,CAAA,CAAAd,CAAA,GAAAO,CAAA,GAAAO,CAAA,CAAAC,CAAA,GAAAR,CAAA,aAAAI,CAAA,MAAAjD,CAAA,QAAA0C,CAAA,KAAAF,CAAA,YAAAL,CAAA,GAAAnC,CAAA,CAAAwC,CAAA,WAAAL,CAAA,GAAAA,CAAA,CAAAuB,IAAA,CAAA1D,CAAA,EAAA6C,CAAA,UAAAY,SAAA,2CAAAtB,CAAA,CAAAwB,IAAA,SAAAxB,CAAA,EAAAU,CAAA,GAAAV,CAAA,CAAA1K,KAAA,EAAAiL,CAAA,SAAAA,CAAA,oBAAAA,CAAA,KAAAP,CAAA,GAAAnC,CAAA,eAAAmC,CAAA,CAAAuB,IAAA,CAAA1D,CAAA,GAAA0C,CAAA,SAAAG,CAAA,GAAAY,SAAA,uCAAAjB,CAAA,gBAAAE,CAAA,OAAA1C,CAAA,GAAAjF,CAAA,cAAAoH,CAAA,IAAAgB,CAAA,GAAAC,CAAA,CAAAd,CAAA,QAAAO,CAAA,GAAAT,CAAA,CAAAsB,IAAA,CAAApB,CAAA,EAAAc,CAAA,OAAAE,CAAA,kBAAAnB,CAAA,IAAAnC,CAAA,GAAAjF,CAAA,EAAA2H,CAAA,MAAAG,CAAA,GAAAV,CAAA,cAAAc,CAAA,mBAAAxL,KAAA,EAAA0K,CAAA,EAAAwB,IAAA,EAAAR,CAAA,SAAAf,CAAA,EAAAI,CAAA,EAAAxC,CAAA,QAAA6C,CAAA,QAAAS,CAAA,gBAAAV,UAAA,cAAAgB,kBAAA,cAAAC,2BAAA,KAAA1B,CAAA,GAAAW,MAAA,CAAAgB,cAAA,MAAApB,CAAA,MAAAJ,CAAA,IAAAH,CAAA,CAAAA,CAAA,IAAAG,CAAA,SAAAU,mBAAA,CAAAb,CAAA,OAAAG,CAAA,iCAAAH,CAAA,GAAAU,CAAA,GAAAgB,0BAAA,CAAAlB,SAAA,GAAAC,SAAA,CAAAD,SAAA,GAAAG,MAAA,CAAAC,MAAA,CAAAL,CAAA,YAAAO,EAAAlI,CAAA,WAAA+H,MAAA,CAAAiB,cAAA,GAAAjB,MAAA,CAAAiB,cAAA,CAAAhJ,CAAA,EAAA8I,0BAAA,KAAA9I,CAAA,CAAAiJ,SAAA,GAAAH,0BAAA,EAAAb,mBAAA,CAAAjI,CAAA,EAAAyH,CAAA,yBAAAzH,CAAA,CAAA4H,SAAA,GAAAG,MAAA,CAAAC,MAAA,CAAAF,CAAA,GAAA9H,CAAA,WAAA6I,iBAAA,CAAAjB,SAAA,GAAAkB,0BAAA,EAAAb,mBAAA,CAAAH,CAAA,iBAAAgB,0BAAA,GAAAb,mBAAA,CAAAa,0BAAA,iBAAAD,iBAAA,GAAAA,iBAAA,CAAAK,WAAA,wBAAAjB,mBAAA,CAAAa,0BAAA,EAAArB,CAAA,wBAAAQ,mBAAA,CAAAH,CAAA,GAAAG,mBAAA,CAAAH,CAAA,EAAAL,CAAA,gBAAAQ,mBAAA,CAAAH,CAAA,EAAAP,CAAA,iCAAAU,mBAAA,CAAAH,CAAA,8DAAAqB,YAAA,YAAAA,aAAA,aAAAC,CAAA,EAAAnE,CAAA,EAAAoE,CAAA,EAAAnB,CAAA;AAAA,SAAAD,oBAAAjI,CAAA,EAAAqH,CAAA,EAAAE,CAAA,EAAAH,CAAA,QAAAnC,CAAA,GAAA8C,MAAA,CAAAuB,cAAA,QAAArE,CAAA,uBAAAjF,CAAA,IAAAiF,CAAA,QAAAgD,mBAAA,YAAAsB,mBAAAvJ,CAAA,EAAAqH,CAAA,EAAAE,CAAA,EAAAH,CAAA,QAAAC,CAAA,EAAApC,CAAA,GAAAA,CAAA,CAAAjF,CAAA,EAAAqH,CAAA,IAAA3K,KAAA,EAAA6K,CAAA,EAAAiC,UAAA,GAAApC,CAAA,EAAAqC,YAAA,GAAArC,CAAA,EAAAsC,QAAA,GAAAtC,CAAA,MAAApH,CAAA,CAAAqH,CAAA,IAAAE,CAAA,YAAAE,CAAA,YAAAA,EAAAJ,CAAA,EAAAE,CAAA,IAAAU,mBAAA,CAAAjI,CAAA,EAAAqH,CAAA,YAAArH,CAAA,gBAAA2J,OAAA,CAAAtC,CAAA,EAAAE,CAAA,EAAAvH,CAAA,UAAAyH,CAAA,aAAAA,CAAA,cAAAA,CAAA,oBAAAQ,mBAAA,CAAAjI,CAAA,EAAAqH,CAAA,EAAAE,CAAA,EAAAH,CAAA;AAAA,SAAAwC,mBAAArC,CAAA,EAAAH,CAAA,EAAApH,CAAA,EAAAqH,CAAA,EAAAI,CAAA,EAAAc,CAAA,EAAAZ,CAAA,cAAA1C,CAAA,GAAAsC,CAAA,CAAAgB,CAAA,EAAAZ,CAAA,GAAAG,CAAA,GAAA7C,CAAA,CAAAvI,KAAA,WAAA6K,CAAA,gBAAAvH,CAAA,CAAAuH,CAAA,KAAAtC,CAAA,CAAA2D,IAAA,GAAAxB,CAAA,CAAAU,CAAA,IAAA1J,OAAA,CAAAC,OAAA,CAAAyJ,CAAA,EAAA+B,IAAA,CAAAxC,CAAA,EAAAI,CAAA;AAAA,SAAAqC,kBAAAvC,CAAA,6BAAAH,CAAA,SAAApH,CAAA,GAAApE,SAAA,aAAAwC,OAAA,WAAAiJ,CAAA,EAAAI,CAAA,QAAAc,CAAA,GAAAhB,CAAA,CAAAwC,KAAA,CAAA3C,CAAA,EAAApH,CAAA,YAAAgK,MAAAzC,CAAA,IAAAqC,kBAAA,CAAArB,CAAA,EAAAlB,CAAA,EAAAI,CAAA,EAAAuC,KAAA,EAAAC,MAAA,UAAA1C,CAAA,cAAA0C,OAAA1C,CAAA,IAAAqC,kBAAA,CAAArB,CAAA,EAAAlB,CAAA,EAAAI,CAAA,EAAAuC,KAAA,EAAAC,MAAA,WAAA1C,CAAA,KAAAyC,KAAA;AAAA,SAAAjO,gBAAAwM,CAAA,EAAAhB,CAAA,UAAAgB,CAAA,YAAAhB,CAAA,aAAAmB,SAAA;AAAA,SAAAwB,kBAAAlK,CAAA,EAAAqH,CAAA,aAAAD,CAAA,MAAAA,CAAA,GAAAC,CAAA,CAAAxL,MAAA,EAAAuL,CAAA,UAAAK,CAAA,GAAAJ,CAAA,CAAAD,CAAA,GAAAK,CAAA,CAAA+B,UAAA,GAAA/B,CAAA,CAAA+B,UAAA,QAAA/B,CAAA,CAAAgC,YAAA,kBAAAhC,CAAA,KAAAA,CAAA,CAAAiC,QAAA,QAAA3B,MAAA,CAAAuB,cAAA,CAAAtJ,CAAA,EAAAmK,cAAA,CAAA1C,CAAA,CAAAhL,GAAA,GAAAgL,CAAA;AAAA,SAAAjL,aAAAwD,CAAA,EAAAqH,CAAA,EAAAD,CAAA,WAAAC,CAAA,IAAA6C,iBAAA,CAAAlK,CAAA,CAAA4H,SAAA,EAAAP,CAAA,GAAAD,CAAA,IAAA8C,iBAAA,CAAAlK,CAAA,EAAAoH,CAAA,GAAAW,MAAA,CAAAuB,cAAA,CAAAtJ,CAAA,iBAAA0J,QAAA,SAAA1J,CAAA;AAAA,SAAAmK,eAAA/C,CAAA,QAAAnC,CAAA,GAAAmF,YAAA,CAAAhD,CAAA,gCAAAiD,OAAA,CAAApF,CAAA,IAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAmF,aAAAhD,CAAA,EAAAC,CAAA,oBAAAgD,OAAA,CAAAjD,CAAA,MAAAA,CAAA,SAAAA,CAAA,MAAApH,CAAA,GAAAoH,CAAA,CAAAE,MAAA,CAAAgD,WAAA,kBAAAtK,CAAA,QAAAiF,CAAA,GAAAjF,CAAA,CAAA2I,IAAA,CAAAvB,CAAA,EAAAC,CAAA,gCAAAgD,OAAA,CAAApF,CAAA,UAAAA,CAAA,YAAAyD,SAAA,yEAAArB,CAAA,GAAAkD,MAAA,GAAAC,MAAA,EAAApD,CAAA;AADA;AACA;AACA;AACA;AAC2C;AAAA,IAErCqD,oBAAoB;EACtB,SAAAA,qBAAA,EAAc;IAAA1O,eAAA,OAAA0O,oBAAA;IACV,IAAI,CAACyW,WAAW,GAAGA,uDAAW;EAClC;;EAEA;AACJ;AACA;AACA;AACA;AACA;EALI,OAAA1kB,YAAA,CAAAiO,oBAAA;IAAAhO,GAAA;IAAAC,KAAA;MAAA,IAAAykB,eAAA,GAAArX,iBAAA,cAAAX,YAAA,GAAAE,CAAA,CAMA,SAAAyC,QAAqByC,SAAS;QAAA,IAAAyF,MAAA;UAAArG,IAAA;UAAAyT,KAAA,GAAAxlB,SAAA;UAAAoQ,EAAA;QAAA,OAAA7C,YAAA,GAAAC,CAAA,WAAA6C,QAAA;UAAA,kBAAAA,QAAA,CAAA9D,CAAA,GAAA8D,QAAA,CAAA1E,CAAA;YAAA;cAAEyM,MAAM,GAAAoN,KAAA,CAAAvlB,MAAA,QAAAulB,KAAA,QAAAtlB,SAAA,GAAAslB,KAAA,MAAG,CAAC,CAAC;cAAAnV,QAAA,CAAA9D,CAAA;cAEnC;cACMwF,IAAI,GAAG;gBACT/K,KAAK,EAAE2L,SAAS;gBAChBzR,EAAE,EAAEkX,MAAM,CAAClX,EAAE,IAAI;cACrB,CAAC,EAED;cAAAmP,QAAA,CAAA1E,CAAA;cAAA,OACa,IAAI,CAAC2Z,WAAW,CAACxC,IAAI,CAAC,qBAAqB,EAAE/Q,IAAI,CAAC;YAAA;cAAA,OAAA1B,QAAA,CAAA1D,CAAA,IAAA0D,QAAA,CAAA3D,CAAA;YAAA;cAAA2D,QAAA,CAAA9D,CAAA;cAAA6D,EAAA,GAAAC,QAAA,CAAA3D,CAAA;cAG/D7J,OAAO,CAACC,KAAK,CAAC,0BAA0B,EAAAsN,EAAO,CAAC;cAAC,MAAAA,EAAA;YAAA;cAAA,OAAAC,QAAA,CAAA1D,CAAA;UAAA;QAAA,GAAAuD,OAAA;MAAA,CAGxD;MAAA,SAfKyI,cAAcA,CAAAlE,EAAA;QAAA,OAAA8Q,eAAA,CAAApX,KAAA,OAAAnO,SAAA;MAAA;MAAA,OAAd2Y,cAAc;IAAA;IAiBpB;AACJ;AACA;AACA;IAHI;EAAA;IAAA9X,GAAA;IAAAC,KAAA;MAAA,IAAA2kB,UAAA,GAAAvX,iBAAA,cAAAX,YAAA,GAAAE,CAAA,CAIA,SAAAqD,SAAA;QAAA,IAAAO,GAAA;QAAA,OAAA9D,YAAA,GAAAC,CAAA,WAAAuD,SAAA;UAAA,kBAAAA,SAAA,CAAAxE,CAAA,GAAAwE,SAAA,CAAApF,CAAA;YAAA;cAAAoF,SAAA,CAAAxE,CAAA;cAAAwE,SAAA,CAAApF,CAAA;cAAA,OAEqB,IAAI,CAAC2Z,WAAW,CAACxC,IAAI,CAAC,0BAA0B,CAAC;YAAA;cAAA,OAAA/R,SAAA,CAAApE,CAAA,IAAAoE,SAAA,CAAArE,CAAA;YAAA;cAAAqE,SAAA,CAAAxE,CAAA;cAAA8E,GAAA,GAAAN,SAAA,CAAArE,CAAA;cAE9D7J,OAAO,CAACC,KAAK,CAAC,wBAAwB,EAAAuO,GAAO,CAAC;cAAC,MAAAA,GAAA;YAAA;cAAA,OAAAN,SAAA,CAAApE,CAAA;UAAA;QAAA,GAAAmE,QAAA;MAAA,CAGtD;MAAA,SAPK4U,SAASA,CAAA;QAAA,OAAAD,UAAA,CAAAtX,KAAA,OAAAnO,SAAA;MAAA;MAAA,OAAT0lB,SAAS;IAAA;IASf;AACJ;AACA;AACA;AACA;IAJI;EAAA;IAAA7kB,GAAA;IAAAC,KAAA;MAAA,IAAA6kB,cAAA,GAAAzX,iBAAA,cAAAX,YAAA,GAAAE,CAAA,CAKA,SAAA0D,SAAoByU,OAAO;QAAA,IAAA5R,GAAA;QAAA,OAAAzG,YAAA,GAAAC,CAAA,WAAA8D,SAAA;UAAA,kBAAAA,SAAA,CAAA/E,CAAA,GAAA+E,SAAA,CAAA3F,CAAA;YAAA;cAAA2F,SAAA,CAAA/E,CAAA;cAAA+E,SAAA,CAAA3F,CAAA;cAAA,OAEN,IAAI,CAAC2Z,WAAW,CAACxC,IAAI,CAAC,iBAAiB,EAAE;gBAClDtK,QAAQ,EAAEoN;cACd,CAAC,CAAC;YAAA;cAAA,OAAAtU,SAAA,CAAA3E,CAAA,IAAA2E,SAAA,CAAA5E,CAAA;YAAA;cAAA4E,SAAA,CAAA/E,CAAA;cAAAyH,GAAA,GAAA1C,SAAA,CAAA5E,CAAA;cAEF7J,OAAO,CAACC,KAAK,CAAC,6BAA6B,EAAAkR,GAAO,CAAC;cAAC,MAAAA,GAAA;YAAA;cAAA,OAAA1C,SAAA,CAAA3E,CAAA;UAAA;QAAA,GAAAwE,QAAA;MAAA,CAG3D;MAAA,SATK0U,aAAaA,CAAAvR,GAAA;QAAA,OAAAqR,cAAA,CAAAxX,KAAA,OAAAnO,SAAA;MAAA;MAAA,OAAb6lB,aAAa;IAAA;IAWnB;AACJ;AACA;AACA;AACA;IAJI;EAAA;IAAAhlB,GAAA;IAAAC,KAAA;MAAA,IAAAglB,WAAA,GAAA5X,iBAAA,cAAAX,YAAA,GAAAE,CAAA,CAKA,SAAAmG,SAAiBmS,UAAU;QAAA,IAAA1N,GAAA;QAAA,OAAA9K,YAAA,GAAAC,CAAA,WAAAyG,SAAA;UAAA,kBAAAA,SAAA,CAAA1H,CAAA,GAAA0H,SAAA,CAAAtI,CAAA;YAAA;cAAAsI,SAAA,CAAA1H,CAAA;cAAA0H,SAAA,CAAAtI,CAAA;cAAA,OAEN,IAAI,CAAC2Z,WAAW,CAACxC,IAAI,CAAC,2BAA2B,EAAE;gBAC5DkD,WAAW,EAAEvgB,IAAI,CAAC4d,SAAS,CAAC0C,UAAU;cAC1C,CAAC,CAAC;YAAA;cAAA,OAAA9R,SAAA,CAAAtH,CAAA,IAAAsH,SAAA,CAAAvH,CAAA;YAAA;cAAAuH,SAAA,CAAA1H,CAAA;cAAA8L,GAAA,GAAApE,SAAA,CAAAvH,CAAA;cAEF7J,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAAuV,GAAO,CAAC;cAAC,MAAAA,GAAA;YAAA;cAAA,OAAApE,SAAA,CAAAtH,CAAA;UAAA;QAAA,GAAAiH,QAAA;MAAA,CAGpD;MAAA,SATKqS,UAAUA,CAAAlN,GAAA;QAAA,OAAA+M,WAAA,CAAA3X,KAAA,OAAAnO,SAAA;MAAA;MAAA,OAAVimB,UAAU;IAAA;IAWhB;AACJ;AACA;AACA;AACA;IAJI;EAAA;IAAAplB,GAAA;IAAAC,KAAA;MAAA,IAAAolB,WAAA,GAAAhY,iBAAA,cAAAX,YAAA,GAAAE,CAAA,CAKA,SAAAwF,SAAA;QAAA,IAAAkT,OAAA;UAAApU,IAAA;UAAAqU,MAAA,GAAApmB,SAAA;UAAAmc,GAAA;QAAA,OAAA5O,YAAA,GAAAC,CAAA,WAAA2F,SAAA;UAAA,kBAAAA,SAAA,CAAA5G,CAAA,GAAA4G,SAAA,CAAAxH,CAAA;YAAA;cAAiBwa,OAAO,GAAAC,MAAA,CAAAnmB,MAAA,QAAAmmB,MAAA,QAAAlmB,SAAA,GAAAkmB,MAAA,MAAG,CAAC,CAAC;cAAAjT,SAAA,CAAA5G,CAAA;cAEfwF,IAAI,GAAG,CAAC,CAAC,EAEf;cACA,IAAIoU,OAAO,CAAC3N,QAAQ,EAAE;gBAClBzG,IAAI,CAACyG,QAAQ,GAAG2N,OAAO,CAAC3N,QAAQ;cACpC;cACA,IAAI2N,OAAO,CAACE,KAAK,EAAE;gBACftU,IAAI,CAACsU,KAAK,GAAGF,OAAO,CAACE,KAAK;cAC9B;cACA,IAAIF,OAAO,CAACnP,MAAM,EAAE;gBAChBjF,IAAI,CAACiF,MAAM,GAAGmP,OAAO,CAACnP,MAAM;cAChC;cAAC7D,SAAA,CAAAxH,CAAA;cAAA,OAEY,IAAI,CAAC2Z,WAAW,CAACxC,IAAI,CAAC,2BAA2B,EAAE/Q,IAAI,CAAC;YAAA;cAAA,OAAAoB,SAAA,CAAAxG,CAAA,IAAAwG,SAAA,CAAAzG,CAAA;YAAA;cAAAyG,SAAA,CAAA5G,CAAA;cAAA4P,GAAA,GAAAhJ,SAAA,CAAAzG,CAAA;cAErE7J,OAAO,CAACC,KAAK,CAAC,yBAAyB,EAAAqZ,GAAO,CAAC;cAAC,MAAAA,GAAA;YAAA;cAAA,OAAAhJ,SAAA,CAAAxG,CAAA;UAAA;QAAA,GAAAsG,QAAA;MAAA,CAGvD;MAAA,SApBKqT,UAAUA,CAAA;QAAA,OAAAJ,WAAA,CAAA/X,KAAA,OAAAnO,SAAA;MAAA;MAAA,OAAVsmB,UAAU;IAAA;IAsBhB;AACJ;AACA;AACA;IAHI;EAAA;IAAAzlB,GAAA;IAAAC,KAAA;MAAA,IAAAylB,eAAA,GAAArY,iBAAA,cAAAX,YAAA,GAAAE,CAAA,CAIA,SAAA0K,SAAA3O,IAAA;QAAA,IAAA5C,MAAA,EAAA6U,OAAA,EAAA1J,IAAA,EAAAyU,GAAA;QAAA,OAAAjZ,YAAA,GAAAC,CAAA,WAAA8K,SAAA;UAAA,kBAAAA,SAAA,CAAA/L,CAAA,GAAA+L,SAAA,CAAA3M,CAAA;YAAA;cAAsB/E,MAAM,GAAA4C,IAAA,CAAN5C,MAAM,EAAE6U,OAAO,GAAAjS,IAAA,CAAPiS,OAAO;cAAAnD,SAAA,CAAA/L,CAAA;cAEzBwF,IAAI,GAAG;gBACP,QAAQ,EAAEnL,MAAM;gBAChB,SAAS,EAAE6U;cACf,CAAC;cAAAnD,SAAA,CAAA3M,CAAA;cAAA,OACY,IAAI,CAAC2Z,WAAW,CAACxC,IAAI,CAAC,+BAA+B,EAAE/Q,IAAI,CAAC;YAAA;cAAA,OAAAuG,SAAA,CAAA3L,CAAA,IAAA2L,SAAA,CAAA5L,CAAA;YAAA;cAAA4L,SAAA,CAAA/L,CAAA;cAAAia,GAAA,GAAAlO,SAAA,CAAA5L,CAAA;cAEzE7J,OAAO,CAACC,KAAK,CAAC,6BAA6B,EAAA0jB,GAAO,CAAC;cAAC,MAAAA,GAAA;YAAA;cAAA,OAAAlO,SAAA,CAAA3L,CAAA;UAAA;QAAA,GAAAwL,QAAA;MAAA,CAG3D;MAAA,SAXKkE,cAAcA,CAAAC,GAAA;QAAA,OAAAiK,eAAA,CAAApY,KAAA,OAAAnO,SAAA;MAAA;MAAA,OAAdqc,cAAc;IAAA;EAAA;AAAA,KAcxB;AACA,iEAAexN,oBAAoB,E;;;;;;;;;;;;;;;;;;;;;;;;AC7HnC;AACA;AACA;AACA;AACA;AACA;AALA,IAMM4X,WAAW;EACb;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,SAAAA,YAAA,EAA0B;IAAA,IAAd1mB,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IAAAG,eAAA,OAAAsmB,WAAA;IACpB;IACA,IAAM5E,UAAU,GAAGxe,MAAM,CAACqjB,iBAAiB,IAAI,CAAC,CAAC;IAEjD,IAAI,CAACC,OAAO,GAAG5mB,OAAO,CAAC4mB,OAAO,IAAI9E,UAAU,CAAC8E,OAAO,IAAI,4BAA4B;IACpF,IAAI,CAAC3E,KAAK,GAAGjiB,OAAO,CAACiiB,KAAK,IAAIH,UAAU,CAAC+E,SAAS,IAAI,EAAE;IACxD,IAAI,CAAC1E,OAAO,GAAGniB,OAAO,CAACmiB,OAAO,IAAI,IAAI;;IAEtC;IACA,IAAI,CAAC,IAAI,CAACyE,OAAO,EAAE;MACf9jB,OAAO,CAACC,KAAK,CAAC,kCAAkC,CAAC;IACrD;IAEA,IAAI,CAAC,IAAI,CAACkf,KAAK,EAAE;MACbnf,OAAO,CAAC8M,IAAI,CAAC,uEAAuE,CAAC;IACzF;;IAEA;IACA,IAAI,CAACwS,eAAe,GAAG,IAAIC,GAAG,CAAC,CAAC;EACpC;;EAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EAPI,OAAAxhB,YAAA,CAAA6lB,WAAA;IAAA5lB,GAAA;IAAAC,KAAA,EAQA,SAAAuhB,GAAGA,CAACwE,QAAQ,EAA6B;MAAA,IAA3BzO,MAAM,GAAApY,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MAAA,IAAED,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MACnC;MACA,IAAMyiB,WAAW,GAAGtW,MAAM,CAACsU,IAAI,CAACrI,MAAM,CAAC,CAACnY,MAAM,GACxC,GAAG,GAAG,IAAIyiB,eAAe,CAACtK,MAAM,CAAC,CAAClV,QAAQ,CAAC,CAAC,GAC5C,EAAE;MAER,IAAMyf,GAAG,MAAAlhB,MAAA,CAAM,IAAI,CAACklB,OAAO,EAAAllB,MAAA,CAAGolB,QAAQ,EAAAplB,MAAA,CAAGghB,WAAW,CAAE;MAEtD,OAAO,IAAI,CAACG,YAAY,CAACD,GAAG,EAAAnf,aAAA;QACxBqf,MAAM,EAAE;MAAK,GACV9iB,OAAO,CACb,CAAC;IACN;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EAPI;IAAAc,GAAA;IAAAC,KAAA,EAQA,SAAAgiB,IAAIA,CAAC+D,QAAQ,EAA2B;MAAA,IAAzB9U,IAAI,GAAA/R,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MAAA,IAAED,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MAClC,IAAM2iB,GAAG,MAAAlhB,MAAA,CAAM,IAAI,CAACklB,OAAO,EAAAllB,MAAA,CAAGolB,QAAQ,CAAE;MAExC,OAAO,IAAI,CAACjE,YAAY,CAACD,GAAG,EAAAnf,aAAA;QACxBqf,MAAM,EAAE,MAAM;QACdW,OAAO,EAAE;UACL,cAAc,EAAE;QACpB,CAAC;QACDliB,IAAI,EAAEmE,IAAI,CAAC4d,SAAS,CAACtR,IAAI;MAAC,GACvBhS,OAAO,CACb,CAAC;IACN;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EAPI;IAAAc,GAAA;IAAAC,KAAA,EAQA,SAAA8hB,YAAYA,CAACD,GAAG,EAAE5iB,OAAO,EAAE;MAAA,IAAAwC,KAAA;MACvB;MACA,IAAMkhB,SAAS,GAAGzgB,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACwgB,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;;MAE7D;MACA,IAAMC,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;MACxC,IAAQC,MAAM,GAAKF,UAAU,CAArBE,MAAM;;MAEd;MACA,IAAMC,OAAO,GAAG/jB,OAAO,CAAC+jB,OAAO,IAAI,KAAK,CAAC,CAAC;MAC1C,IAAMC,SAAS,GAAGrhB,UAAU,CAAC,YAAM;QAC/BihB,UAAU,CAACK,KAAK,CAAC,CAAC;MACtB,CAAC,EAAEF,OAAO,CAAC;;MAEX;MACA,IAAI,CAAC3B,eAAe,CAAC8B,GAAG,CAACR,SAAS,EAAE;QAAEE,UAAU,EAAVA;MAAW,CAAC,CAAC;;MAEnD;MACA,IAAMljB,MAAM,GAAGV,OAAO,CAACU,MAAM,IAAK4C,MAAM,CAAC6gB,WAAW,IAAI,IAAK;MAC7D,IAAMC,UAAU,GAAGpkB,OAAO,CAACokB,UAAU,KAAK,KAAK;MAE/C,IAAI1jB,MAAM,IAAI0jB,UAAU,EAAE;QACtB1jB,MAAM,CAACqB,IAAI,CAAC/B,OAAO,CAACqkB,UAAU,IAAI,YAAY,CAAC;MACnD;;MAEA;MACA,IAAMZ,OAAO,GAAGzjB,OAAO,CAACyjB,OAAO,IAAI,CAAC,CAAC;MACrCA,OAAO,CAAC,YAAY,CAAC,GAAG,IAAI,CAACxB,KAAK,CAAC,CAAC;;MAEpC;MACA,OAAOqC,KAAK,CAAC1B,GAAG,EAAAnf,aAAA,CAAAA,aAAA,KACTzD,OAAO;QACVyjB,OAAO,EAAPA,OAAO;QACPK,MAAM,EAANA,MAAM;QACNS,WAAW,EAAE;MAAa,EAC7B,CAAC,CACDrW,IAAI,CAAC,UAAA1G,QAAQ,EAAI;QACd;QACAgd,YAAY,CAACR,SAAS,CAAC;;QAEvB;QACA,IAAI,CAACxc,QAAQ,CAACid,EAAE,EAAE;UACd,MAAM,IAAIpa,KAAK,eAAA3I,MAAA,CAAe8F,QAAQ,CAACkd,MAAM,QAAAhjB,MAAA,CAAK8F,QAAQ,CAACmd,UAAU,CAAE,CAAC;QAC5E;;QAEA;QACA,IAAMC,WAAW,GAAGpd,QAAQ,CAACic,OAAO,CAACnB,GAAG,CAAC,cAAc,CAAC;QACxD,IAAIsC,WAAW,IAAIA,WAAW,CAACzZ,QAAQ,CAAC,kBAAkB,CAAC,EAAE;UACzD,OAAO3D,QAAQ,CAACqd,IAAI,CAAC,CAAC;QAC1B;QAEA,OAAOrd,QAAQ,CAAClH,IAAI,CAAC,CAAC;MAC1B,CAAC,CAAC,SACI,CAAC,UAAAyC,KAAK,EAAI;QACZ;QACA,IAAIA,KAAK,CAACsgB,IAAI,KAAK,YAAY,EAAE;UAC7B,MAAM,IAAIhZ,KAAK,CAAC,mBAAmB,CAAC;QACxC;;QAEA;QACA,IAAI7H,KAAI,CAAC2f,OAAO,EAAE;UACd3f,KAAI,CAAC2f,OAAO,CAACpf,KAAK,CAAC;QACvB;QAEA,MAAMA,KAAK;MACf,CAAC,CAAC,WACM,CAAC,YAAM;QACX;QACAP,KAAI,CAAC4f,eAAe,UAAO,CAACsB,SAAS,CAAC;;QAEtC;QACA,IAAIhjB,MAAM,IAAI0jB,UAAU,EAAE;UACtB;UACA,IAAMW,uBAAuB,GAAGlc,KAAK,CAACC,IAAI,CAACtG,KAAI,CAAC4f,eAAe,CAAC4C,MAAM,CAAC,CAAC,CAAC,CACpEC,IAAI,CAAC,UAAAC,GAAG;YAAA,OAAIA,GAAG,CAACxkB,MAAM,KAAKA,MAAM;UAAA,EAAC;UAEvC,IAAI,CAACqkB,uBAAuB,EAAE;YAC1BrkB,MAAM,CAACoB,IAAI,CAAC,CAAC;UACjB;QACJ;MACJ,CAAC,CAAC;IACN;;IAEA;AACJ;AACA;EAFI;IAAAhB,GAAA;IAAAC,KAAA,EAGA,SAAAokB,SAASA,CAAA,EAAG;MACR,IAAI,CAAC/C,eAAe,CAACpe,OAAO,CAAC,UAAAohB,OAAO,EAAI;QACpCA,OAAO,CAACxB,UAAU,CAACK,KAAK,CAAC,CAAC;MAC9B,CAAC,CAAC;MAEF,IAAI,CAAC7B,eAAe,CAACiD,KAAK,CAAC,CAAC;IAChC;;IAEA;AACJ;AACA;AACA;AACA;EAJI;IAAAvkB,GAAA;IAAAC,KAAA,EAKA,SAAAukB,WAAWA,CAACrD,KAAK,EAAE;MACf,IAAI,CAACA,KAAK,GAAGA,KAAK;IACtB;;IAEA;AACJ;AACA;AACA;AACA;AACA;EALI;IAAAnhB,GAAA;IAAAC,KAAA,EAMA,SAAOsL,MAAMA,CAAA,EAAe;MAAA,IAAdrM,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MACtB,OAAO,IAAIymB,WAAW,CAAC1mB,OAAO,CAAC;IACnC;EAAC;AAAA,KAGL;AACA,IAAM+mB,WAAW,GAAG,IAAIL,WAAW,CAAC,CAAC;AAErC,iEAAeK,WAAW,EAAC;;AAE3B;;;;;;;;;;;;;;;;;;;;;;;AC9MA;AACA;AACA;AACA;AACA;AACA;;AAEwC;AAAA,IAElCC,WAAW;EAAA,SAAAA,YAAA;IAAA5mB,eAAA,OAAA4mB,WAAA;EAAA;EAAA,OAAAnmB,YAAA,CAAAmmB,WAAA;IAAAlmB,GAAA;IAAAC,KAAA;IACb;AACJ;AACA;AACA;AACA;AACA;IACI,SAAAkmB,cAAcA,CAACC,MAAM,EAAE;MACnB,OAAOH,oDAAW,CAACzE,GAAG,WAAA5gB,MAAA,CAAWwlB,MAAM,CAAE,CAAC;IAC9C;;IAEA;AACJ;AACA;AACA;AACA;AACA;EALI;IAAApmB,GAAA;IAAAC,KAAA,EAMA,SAAAwd,gBAAgBA,CAAC2I,MAAM,EAAE;MACrB,OAAOH,oDAAW,CAACzE,GAAG,WAAA5gB,MAAA,CAAWwlB,MAAM,eAAY,CAAC;IACxD;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EAPI;IAAApmB,GAAA;IAAAC,KAAA,EAQA,SAAAoe,UAAUA,CAAC+H,MAAM,EAAEnG,OAAO,EAAEoG,SAAS,EAAE;MACnC,OAAOJ,oDAAW,CAAChE,IAAI,WAAArhB,MAAA,CAAWwlB,MAAM,cAAW;QAC/CnG,OAAO,EAAEA,OAAO;QAChBD,UAAU,EAAEqG;MAChB,CAAC,CAAC;IACN;;IAEA;AACJ;AACA;AACA;AACA;AACA;EALI;IAAArmB,GAAA;IAAAC,KAAA,EAMA,SAAAqmB,iBAAiBA,CAAA,EAAe;MAAA,IAAdhB,OAAO,GAAAnmB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MAC1B,OAAO8mB,oDAAW,CAACzE,GAAG,CAAC,QAAQ,EAAE8D,OAAO,CAAC;IAC7C;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;EANI;IAAAtlB,GAAA;IAAAC,KAAA,EAOA,SAAAsmB,qBAAqBA,CAAA,EAAuB;MAAA,IAAtBf,KAAK,GAAArmB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;MAAA,IAAEqnB,IAAI,GAAArnB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;MACtC,OAAO8mB,oDAAW,CAACzE,GAAG,CAAC,UAAU,EAAE;QAC/BgE,KAAK,EAAEA,KAAK;QACZgB,IAAI,EAAEA;MACV,CAAC,CAAC;IACN;;IAEA;AACJ;AACA;AACA;AACA;AACA;EALI;IAAAxmB,GAAA;IAAAC,KAAA,EAMA,SAAAwmB,aAAaA,CAACC,QAAQ,EAAE;MACpB,OAAOT,oDAAW,CAACzE,GAAG,aAAA5gB,MAAA,CAAa8lB,QAAQ,CAAE,CAAC;IAClD;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;EANI;IAAA1mB,GAAA;IAAAC,KAAA,EAOA,SAAA0mB,gBAAgBA,CAACP,MAAM,EAAEQ,QAAQ,EAAE;MAC/B,OAAOX,oDAAW,CAAChE,IAAI,WAAArhB,MAAA,CAAWwlB,MAAM,gBAAa;QACjDQ,QAAQ,EAAEA,QAAQ;QAClBC,gBAAgB,EAAED,QAAQ,CAACE,eAAe,IAAI;MAClD,CAAC,CAAC;IACN;;IAEA;AACJ;AACA;AACA;AACA;AACA;EALI;IAAA9mB,GAAA;IAAAC,KAAA,EAMA,SAAA8mB,gBAAgBA,CAACX,MAAM,EAAE;MACrB,OAAOH,oDAAW,CAACzE,GAAG,WAAA5gB,MAAA,CAAWwlB,MAAM,cAAW,CAAC;IACvD;;IAEA;AACJ;AACA;AACA;AACA;AACA;EALI;IAAApmB,GAAA;IAAAC,KAAA,EAMA,SAAA+mB,iBAAiBA,CAACZ,MAAM,EAAE;MACtB,OAAOH,oDAAW,CAACzE,GAAG,WAAA5gB,MAAA,CAAWwlB,MAAM,gBAAa,CAAC;IACzD;;IAEA;AACJ;AACA;AACA;AACA;AACA;EALI;IAAApmB,GAAA;IAAAC,KAAA,EAMA,SAAAgnB,iBAAiBA,CAACC,SAAS,EAAE;MACzB;MACA,OAAOvlB,OAAO,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC9B;EAAC;AAAA,KAGL;AACA,IAAM8Z,WAAW,GAAG,IAAIwK,WAAW,CAAC,CAAC;AACrC,iEAAexK,WAAW,EAAC;;AAE3B;;;;;;;UCjIA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;WCtBA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA,E;;;;;WCPA,wF;;;;;WCAA;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D,E;;;;;;;;;;;;;;;;;;;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;;AAEA;AAC2C;AACF;AACyB;AACA;AACZ;AACa;;AAEnE;AACAxb,QAAQ,CAACkD,gBAAgB,CAAC,kBAAkB,EAAE,YAAM;EAChD;EACAZ,MAAM,CAAC6gB,WAAW,GAAG,IAAIpkB,0DAAM,CAAC;IAC5BS,UAAU,EAAE;EAChB,CAAC,CAAC;;EAEF;EACA8C,MAAM,CAAC2kB,SAAS,GAAG1C,6DAAW;EAC9BjiB,MAAM,CAAC4kB,SAAS,GAAG1L,6DAAW;;EAE9B;EACA,IAAMjN,SAAS,GAAGvO,QAAQ,CAACC,cAAc,CAAC,YAAY,CAAC;EACvD,IAAIsO,SAAS,EAAE;IACXjM,MAAM,CAAC6kB,kBAAkB,GAAG,IAAIpZ,4EAAqB,CAAC;MAACC,aAAa,EAAEO,SAAS,CAAC9I,OAAO,CAACuI;IAAa,CAAC,CAAC;EAC3G;;EAEA;EACA,IAAMoZ,OAAO,GAAGpnB,QAAQ,CAACmB,aAAa,CAAC,gBAAgB,CAAC;EACxD,IAAIimB,OAAO,EAAE;IACT,IAAMA,QAAO,GAAG,IAAI1kB,2DAAO,CAAC0kB,QAAO,CAAC;IACpCA,QAAO,CAACxnB,IAAI,CAAC,CAAC;EAClB;AACJ,CAAC,CAAC;;AAEF", "sources": ["webpack://ToeicPractice/webpack/universalModuleDefinition", "webpack://ToeicPractice/./assets/js/frontend/src/components/Loader.js", "webpack://ToeicPractice/./assets/js/frontend/src/components/Sidebar.js", "webpack://ToeicPractice/./assets/js/frontend/src/components/questions/MatchingRenderer.js", "webpack://ToeicPractice/./assets/js/frontend/src/components/questions/MultipleChoiceRenderer.js", "webpack://ToeicPractice/./assets/js/frontend/src/components/questions/QuestionRenderer.js", "webpack://ToeicPractice/./assets/js/frontend/src/components/questions/QuestionRendererFactory.js", "webpack://ToeicPractice/./assets/js/frontend/src/components/questions/TextInputRenderer.js", "webpack://ToeicPractice/./assets/js/frontend/src/components/questions/index.js", "webpack://ToeicPractice/./assets/js/frontend/src/pages/pronunciation-page.js", "webpack://ToeicPractice/./assets/js/frontend/src/pages/test-detail-page.js", "webpack://ToeicPractice/./assets/js/frontend/src/services/AjaxService.js", "webpack://ToeicPractice/./assets/js/frontend/src/services/PronunciationService.js", "webpack://ToeicPractice/./assets/js/frontend/src/services/RestService.js", "webpack://ToeicPractice/./assets/js/frontend/src/services/TestService.js", "webpack://ToeicPractice/webpack/bootstrap", "webpack://ToeicPractice/webpack/runtime/define property getters", "webpack://ToeicPractice/webpack/runtime/hasOwnProperty shorthand", "webpack://ToeicPractice/webpack/runtime/make namespace object", "webpack://ToeicPractice/./assets/js/frontend/src/index.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"ToeicPractice\"] = factory();\n\telse\n\t\troot[\"ToeicPractice\"] = factory();\n})(self, () => {\nreturn ", "/**\r\n * Loader Component\r\n * \r\n * A reusable loading indicator component that can be shown during\r\n * long-running processes like AJAX requests.\r\n */\r\nclass Loader {\r\n    /**\r\n     * Constructor\r\n     * \r\n     * @param {Object} options - Configuration options\r\n     * @param {string} options.containerId - ID of the container element (default: 'toeic-loader-container')\r\n     * @param {string} options.text - Loading text to display (default: 'Loading...')\r\n     * @param {string} options.size - Size of the loader ('small', 'medium', 'large') (default: 'medium')\r\n     * @param {boolean} options.fullScreen - Whether to show loader as a fullscreen overlay (default: false)\r\n     */\r\n    constructor(options = {}) {\r\n        this.options = {\r\n            containerId: options.containerId || 'toeic-loader-container',\r\n            text: options.text || 'Loading...',\r\n            size: options.size || 'medium',\r\n            fullScreen: options.fullScreen || false\r\n        };\r\n        \r\n        this.container = null;\r\n        this.loader = null;\r\n        this.isVisible = false;\r\n        \r\n        this.init();\r\n    }\r\n    \r\n    /**\r\n     * Initialize the loader\r\n     */\r\n    init() {\r\n        // Check if container already exists\r\n        this.container = document.getElementById(this.options.containerId);\r\n        \r\n        // If container doesn't exist, create it\r\n        if (!this.container) {\r\n            this.container = document.createElement('div');\r\n            this.container.id = this.options.containerId;\r\n            this.container.className = 'toeic-loader-container';\r\n            \r\n            if (this.options.fullScreen) {\r\n                this.container.classList.add('fullscreen');\r\n            }\r\n            \r\n            document.body.appendChild(this.container);\r\n        }\r\n        \r\n        // Create loader element\r\n        this.createLoader();\r\n    }\r\n    \r\n    /**\r\n     * Create the loader element\r\n     */\r\n    createLoader() {\r\n        this.loader = document.createElement('div');\r\n        this.loader.className = `toeic-loader ${this.options.size}`;\r\n        \r\n        // Create spinner\r\n        const spinner = document.createElement('div');\r\n        spinner.className = 'toeic-spinner';\r\n        this.loader.appendChild(spinner);\r\n        \r\n        // Create text element if text is provided\r\n        if (this.options.text) {\r\n            const textElement = document.createElement('div');\r\n            textElement.className = 'toeic-loader-text';\r\n            textElement.textContent = this.options.text;\r\n            this.loader.appendChild(textElement);\r\n        }\r\n        \r\n        // Add to container but keep hidden initially\r\n        this.container.appendChild(this.loader);\r\n        this.hide();\r\n    }\r\n    \r\n    /**\r\n     * Show the loader\r\n     * \r\n     * @param {string} text - Optional text to update the loader with\r\n     */\r\n    show(text = null) {\r\n        if (text) {\r\n            this.updateText(text);\r\n        }\r\n        \r\n        this.container.classList.add('show');\r\n        this.isVisible = true;\r\n        \r\n        return this;\r\n    }\r\n    \r\n    /**\r\n     * Hide the loader\r\n     */\r\n    hide() {\r\n        this.container.style.display = 'none';\r\n        this.isVisible = false;\r\n        \r\n        return this;\r\n    }\r\n    \r\n    /**\r\n     * Update the loader text\r\n     * \r\n     * @param {string} text - New text to display\r\n     */\r\n    updateText(text) {\r\n        const textElement = this.loader.querySelector('.toeic-loader-text');\r\n        \r\n        if (textElement) {\r\n            textElement.textContent = text;\r\n        } else if (text) {\r\n            // Create text element if it doesn't exist\r\n            const newTextElement = document.createElement('div');\r\n            newTextElement.className = 'toeic-loader-text';\r\n            newTextElement.textContent = text;\r\n            this.loader.appendChild(newTextElement);\r\n        }\r\n        \r\n        return this;\r\n    }\r\n    \r\n    /**\r\n     * Toggle the loader visibility\r\n     * \r\n     * @param {string} text - Optional text to update the loader with\r\n     */\r\n    toggle(text = null) {\r\n        if (this.isVisible) {\r\n            this.hide();\r\n        } else {\r\n            this.show(text);\r\n        }\r\n        \r\n        return this;\r\n    }\r\n    \r\n    /**\r\n     * Show the loader for a specific duration\r\n     * \r\n     * @param {number} duration - Duration in milliseconds\r\n     * @param {string} text - Optional text to update the loader with\r\n     * @returns {Promise} - Promise that resolves when the loader is hidden\r\n     */\r\n    showFor(duration, text = null) {\r\n        return new Promise(resolve => {\r\n            this.show(text);\r\n            \r\n            setTimeout(() => {\r\n                this.hide();\r\n                resolve();\r\n            }, duration);\r\n        });\r\n    }\r\n    \r\n    /**\r\n     * Create a loader instance for a specific element\r\n     * \r\n     * @param {HTMLElement|string} element - Element or element selector to show loader in\r\n     * @param {Object} options - Loader options\r\n     * @returns {Loader} - New Loader instance\r\n     */\r\n    static forElement(element, options = {}) {\r\n        // If element is a string, treat it as a selector\r\n        if (typeof element === 'string') {\r\n            element = document.querySelector(element);\r\n        }\r\n        \r\n        if (!element) {\r\n            console.error('Element not found for Loader.forElement');\r\n            return null;\r\n        }\r\n        \r\n        // Generate a unique ID for this loader\r\n        const uniqueId = 'toeic-loader-' + Math.random().toString(36).substr(2, 9);\r\n        \r\n        // Make sure the element has position relative for proper loader positioning\r\n        const computedStyle = window.getComputedStyle(element);\r\n        if (computedStyle.position === 'static') {\r\n            element.style.position = 'relative';\r\n        }\r\n        \r\n        // Create loader container inside the element\r\n        const container = document.createElement('div');\r\n        container.id = uniqueId;\r\n        container.className = 'toeic-loader-container';\r\n        element.appendChild(container);\r\n        \r\n        // Create and return loader instance\r\n        return new Loader({\r\n            ...options,\r\n            containerId: uniqueId,\r\n            fullScreen: false\r\n        });\r\n    }\r\n}\r\n\r\nexport default Loader;\r\n", "/**\r\n * Sidebar Component\r\n * \r\n * Handles the left sidebar navigation that's common across all pages\r\n * in the TOEIC Practice plugin frontend.\r\n */\r\nclass Sidebar {\r\n    /**\r\n     * Constructor\r\n     * \r\n     * @param {HTMLElement} element - The sidebar container element\r\n     */\r\n    constructor(element) {\r\n        this.element = element;\r\n        this.menuItems = this.element.querySelectorAll('.toeic-menu-item');\r\n        this.sidebarToggle = this.element.querySelector('.toeic-sidebar__toggle');\r\n    }\r\n\r\n    /**\r\n     * Initialize the sidebar\r\n     */\r\n    init() {\r\n        this.setupEventListeners();\r\n        this.highlightCurrentPage();\r\n    }\r\n\r\n    /**\r\n     * Set up event listeners for menu items\r\n     */\r\n    setupEventListeners() {\r\n        this.menuItems.forEach(item => {\r\n            item.addEventListener('click', this.handleMenuItemClick.bind(this));\r\n        });\r\n\r\n        this.element.addEventListener('click', (e) => {\r\n            e.stopPropagation();\r\n        });\r\n        this.sidebarToggle.addEventListener('click', this.handleSidebarToggle.bind(this));\r\n\r\n        // when click on document, hide sidebar\r\n        document.addEventListener('click', this.handleDocumentClick.bind(this));\r\n    }\r\n\r\n    /**\r\n     * Handle document click\r\n     */\r\n    handleDocumentClick() {\r\n        this.element.classList.remove('show');\r\n    }\r\n\r\n    /**\r\n     * Handle sidebar toggle\r\n     */\r\n    handleSidebarToggle() {\r\n        this.element.classList.toggle('show');\r\n    }\r\n\r\n    /**\r\n     * Handle menu item click\r\n     * \r\n     * @param {Event} event - Click event\r\n     */\r\n    handleMenuItemClick(event) {\r\n        // Remove active class from all menu items\r\n        this.menuItems.forEach(item => {\r\n            item.classList.remove('active');\r\n        });\r\n\r\n        // Add active class to clicked menu item\r\n        event.currentTarget.classList.add('active');\r\n    }\r\n\r\n    /**\r\n     * Highlight the current page in the menu\r\n     */\r\n    highlightCurrentPage() {\r\n        // Get current page path\r\n        const currentPath = window.location.pathname;\r\n        \r\n        // Find and highlight the corresponding menu item\r\n        this.menuItems.forEach(item => {\r\n            const link = item.querySelector('a');\r\n            if (link && link.getAttribute('href') === currentPath) {\r\n                item.classList.add('active');\r\n            }\r\n        });\r\n    }\r\n}\r\n\r\nexport default Sidebar;\r\n", "/**\r\n * Matching Question Renderer\r\n * \r\n * Handles rendering of matching questions where users match items from two columns.\r\n */\r\n\r\nimport Question<PERSON><PERSON><PERSON> from './QuestionRenderer';\r\n\r\nclass MatchingRenderer extends QuestionRenderer {\r\n    /**\r\n     * Render the matching question\r\n     */\r\n    render() {\r\n        this.clearContainer();\r\n\r\n        // Parse items if needed\r\n        let items = [];\r\n        try {\r\n            items = typeof this.question.items === 'string' ? \r\n                JSON.parse(this.question.items) : this.question.items;\r\n        } catch (e) {\r\n            console.error('Error parsing matching items:', e);\r\n            return;\r\n        }\r\n        \r\n        // If no items found, return\r\n        if (!items || !items.length) {\r\n            console.error('No items found for matching question:', this.question.id);\r\n            return;\r\n        }\r\n        \r\n        // Create matching container\r\n        const matchingContainer = document.createElement('div');\r\n        matchingContainer.className = 'toeic-matching-container';\r\n        \r\n        // Get user answers\r\n        const userAnswers = this.getUserAnswer() || {};\r\n        \r\n        // Create left column (English words)\r\n        const leftColumn = document.createElement('div');\r\n        leftColumn.className = 'toeic-matching-column toeic-matching-left';\r\n        \r\n        // Create right column (Vietnamese words)\r\n        const rightColumn = document.createElement('div');\r\n        rightColumn.className = 'toeic-matching-column toeic-matching-right';\r\n\r\n        // Add column headers\r\n        const leftHeader = document.createElement('div');\r\n        leftHeader.className = 'toeic-matching-header';\r\n        leftHeader.textContent = 'English';\r\n        leftColumn.appendChild(leftHeader);\r\n\r\n        const rightHeader = document.createElement('div');\r\n        rightHeader.className = 'toeic-matching-header';\r\n        rightHeader.textContent = 'Vietnamese';\r\n        rightColumn.appendChild(rightHeader);\r\n\r\n        // Track selected items for matching\r\n        this.selectedLeft = null;\r\n        this.selectedRight = null;\r\n        \r\n        // Track total number of pairs and paired items\r\n        this.totalPairs = items.length;\r\n        this.pairedCount = 0;\r\n\r\n        // Add items to columns\r\n        items.forEach((item, index) => {\r\n            // Left column item (English word)\r\n            const leftItem = document.createElement('div');\r\n            leftItem.className = 'toeic-matching-card';\r\n            leftItem.dataset.itemId = item.id;\r\n            \r\n            const wordContainer = document.createElement('div');\r\n            wordContainer.className = 'toeic-matching-word-container';\r\n            \r\n            // Add the word text\r\n            const wordText = document.createElement('span');\r\n            wordText.className = 'toeic-matching-word';\r\n            wordText.textContent = item.prompt;\r\n            wordContainer.appendChild(wordText);\r\n            \r\n            // Add audio button if available\r\n            if (item.audio_link) {\r\n                const audioButton = document.createElement('button');\r\n                audioButton.className = 'toeic-matching-audio-btn';\r\n                audioButton.innerHTML = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" fill=\"currentColor\" viewBox=\"0 0 16 16\"><path d=\"M11.536 14.01A8.473 8.473 0 0 0 14.026 8a8.473 8.473 0 0 0-2.49-6.01l-.708.707A7.476 7.476 0 0 1 13.025 8c0 2.071-.84 3.946-2.197 5.303l.708.707z\"/><path d=\"M10.121 12.596A6.48 6.48 0 0 0 12.025 8a6.48 6.48 0 0 0-1.904-4.596l-.707.707A5.483 5.483 0 0 1 11.025 8a5.483 5.483 0 0 1-1.61 3.89l.706.706z\"/><path d=\"M8.707 11.182A4.486 4.486 0 0 0 10.025 8a4.486 4.486 0 0 0-1.318-3.182L8 5.525A3.489 3.489 0 0 1 9.025 8 3.49 3.49 0 0 1 8 10.475l.707.707zM6.717 3.55A.5.5 0 0 1 7 4v8a.5.5 0 0 1-.812.39L3.825 10.5H1.5A.5.5 0 0 1 1 10V6a.5.5 0 0 1 .5-.5h2.325l2.363-1.89a.5.5 0 0 1 .529-.06z\"/></svg>';\r\n                \r\n                audioButton.addEventListener('click', (e) => {\r\n                    e.stopPropagation(); // Prevent card selection when clicking audio button\r\n                    const audio = new Audio(item.audio_link);\r\n                    audio.play().catch(e => {\r\n                        console.error('Error playing audio:', e);\r\n                    });\r\n                });\r\n                \r\n                wordContainer.appendChild(audioButton);\r\n            }\r\n            \r\n            leftItem.appendChild(wordContainer);\r\n            \r\n            // Add click event for selection\r\n            leftItem.addEventListener('click', () => {\r\n                if (this.isReviewMode) return;\r\n                \r\n                // Toggle selection\r\n                this.toggleLeftSelection(leftItem);\r\n                \r\n                // Check if we have a match\r\n                this.checkForMatch();\r\n            });\r\n            \r\n            leftColumn.appendChild(leftItem);\r\n            \r\n            // Right column item (Vietnamese translation)\r\n            const rightItem = document.createElement('div');\r\n            rightItem.className = 'toeic-matching-card';\r\n            rightItem.dataset.itemId = item.id;\r\n            rightItem.textContent = item.response;\r\n            \r\n            // Add click event for selection\r\n            rightItem.addEventListener('click', () => {\r\n                if (this.isReviewMode) return;\r\n\r\n                console.log('Right item clicked');\r\n                \r\n                // Toggle selection\r\n                this.toggleRightSelection(rightItem);\r\n                \r\n                // Check if we have a match\r\n                this.checkForMatch();\r\n            });\r\n            \r\n            rightColumn.appendChild(rightItem);\r\n        });\r\n        \r\n        // Add columns to container\r\n        matchingContainer.appendChild(leftColumn);\r\n        matchingContainer.appendChild(rightColumn);\r\n        \r\n        // Add to main container\r\n        this.container.appendChild(matchingContainer);\r\n        \r\n        // Create submit button (initially hidden)\r\n        const submitButton = document.createElement('button');\r\n        submitButton.className = 'toeic-submit-btn';\r\n        submitButton.textContent = 'Submit Answers';\r\n        submitButton.disabled = true;\r\n        submitButton.addEventListener('click', () => {\r\n            // Trigger submission of the question\r\n            if (typeof this.options.onSubmit === 'function') {\r\n                this.options.onSubmit(this.question.id);\r\n            }\r\n        });\r\n        this.container.appendChild(submitButton);\r\n        this.submitButton = submitButton;\r\n        \r\n        // Add CSS for the matching cards\r\n        this.addStyles();\r\n        \r\n        // In review mode, show the correct matches\r\n        if (this.isReviewMode) {\r\n            this.showCorrectMatches(items, userAnswers);\r\n        } else {\r\n            // If we have existing answers, show them\r\n            this.showExistingAnswers(items, userAnswers);\r\n        }\r\n    }\r\n    \r\n    /**\r\n     * Add custom styles for matching cards\r\n     */\r\n    addStyles() {\r\n        // Check if styles are already added\r\n        if (document.getElementById('toeic-matching-styles')) return;\r\n        \r\n        const styleEl = document.createElement('style');\r\n        styleEl.id = 'toeic-matching-styles';\r\n        styleEl.textContent = `\r\n            .toeic-matching-container {\r\n                display: flex;\r\n                gap: 20px;\r\n                margin-bottom: 20px;\r\n            }\r\n            \r\n            .toeic-matching-column {\r\n                flex: 1;\r\n                display: flex;\r\n                flex-direction: column;\r\n                gap: 10px;\r\n            }\r\n            \r\n            .toeic-matching-header {\r\n                font-weight: bold;\r\n                font-size: 16px;\r\n                padding: 10px;\r\n                background-color: #f5f5f5;\r\n                border-radius: 5px;\r\n                text-align: center;\r\n            }\r\n            \r\n            .toeic-matching-card {\r\n                padding: 15px;\r\n                border-radius: 5px;\r\n                background-color: #fff;\r\n                box-shadow: 0 2px 4px rgba(0,0,0,0.1);\r\n                cursor: pointer;\r\n                transition: all 0.3s ease;\r\n                position: relative;\r\n                display: flex;\r\n                align-items: center;\r\n                height: 60px;\r\n                font-size: 24px;\r\n            }\r\n            \r\n            .toeic-matching-word-container {\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: space-between;\r\n                width: 100%;\r\n            }\r\n            \r\n            .toeic-matching-card:hover:not(.paired):not(.disabled) {\r\n                background: linear-gradient(135deg, #ff9966, #ff5e62);\r\n                color: white;\r\n                transform: translateY(-2px);\r\n            }\r\n            \r\n            .toeic-matching-card.selected {\r\n                background: linear-gradient(135deg, #ff9966, #ff5e62);\r\n                color: white;\r\n                transform: translateY(-2px);\r\n                box-shadow: 0 4px 8px rgba(0,0,0,0.2);\r\n            }\r\n            \r\n            .toeic-matching-card.fade-out {\r\n                animation: fadeOut 0.8s forwards;\r\n                pointer-events: none;\r\n            }\r\n            \r\n            @keyframes fadeOut {\r\n                0% { opacity: 1; transform: scale(1); }\r\n                50% { opacity: 0.8; transform: scale(1.05); }\r\n                100% { opacity: 0.4; transform: scale(0.95); }\r\n            }\r\n            \r\n            .toeic-matching-card.paired {\r\n                opacity: 0.4;\r\n                transform: scale(0.95);\r\n                background-color: #f0f0f0;\r\n                color: #666;\r\n                border: 1px dashed #ccc;\r\n                pointer-events: none;\r\n            }\r\n            \r\n            .toeic-matching-card.disabled {\r\n                cursor: default;\r\n                pointer-events: none;\r\n            }\r\n            \r\n            .toeic-matching-audio-btn {\r\n                background: transparent;\r\n                border: none;\r\n                color: inherit;\r\n                cursor: pointer;\r\n                padding: 5px;\r\n                margin-left: 10px;\r\n                width: 30px;\r\n                height: 30px;\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: center;\r\n                border-radius: 50%;\r\n                transition: all 0.2s ease;\r\n            }\r\n            \r\n            .toeic-matching-audio-btn:hover {\r\n                background-color: rgba(255, 255, 255, 0.3);\r\n                transform: scale(1.1);\r\n            }\r\n            \r\n            .toeic-matching-word {\r\n                flex: 1;\r\n            }\r\n            \r\n            .toeic-submit-btn {\r\n                margin-top: 20px;\r\n                padding: 10px 20px;\r\n                background: linear-gradient(135deg, #ff9966, #ff5e62);\r\n                color: white;\r\n                border: none;\r\n                border-radius: 5px;\r\n                cursor: pointer;\r\n                font-weight: bold;\r\n                display: none;\r\n            }\r\n            \r\n            .toeic-submit-btn.active {\r\n                display: block;\r\n            }\r\n            \r\n            .toeic-submit-btn:disabled {\r\n                background: #cccccc;\r\n                cursor: not-allowed;\r\n            }\r\n        `;\r\n        \r\n        document.head.appendChild(styleEl);\r\n    }\r\n    \r\n    /**\r\n     * Toggle selection of a left column item\r\n     * \r\n     * @param {HTMLElement} item - The item to toggle\r\n     */\r\n    toggleLeftSelection(item) {\r\n        // If item is already paired or disabled, do nothing\r\n        if (item.classList.contains('paired') || item.classList.contains('disabled')) {\r\n            return;\r\n        }\r\n        \r\n        // If this item is already selected, deselect it\r\n        if (this.selectedLeft === item) {\r\n            item.classList.remove('selected');\r\n            this.selectedLeft = null;\r\n            return;\r\n        }\r\n        \r\n        // Deselect any previously selected item\r\n        if (this.selectedLeft) {\r\n            this.selectedLeft.classList.remove('selected');\r\n        }\r\n        \r\n        // Select this item\r\n        item.classList.add('selected');\r\n        this.selectedLeft = item;\r\n    }\r\n    \r\n    /**\r\n     * Toggle selection of a right column item\r\n     * \r\n     * @param {HTMLElement} item - The item to toggle\r\n     */\r\n    toggleRightSelection(item) {\r\n        // If item is already paired or disabled, do nothing\r\n        if (item.classList.contains('paired') || item.classList.contains('disabled')) {\r\n            return;\r\n        }\r\n        \r\n        // If this item is already selected, deselect it\r\n        if (this.selectedRight === item) {\r\n            item.classList.remove('selected');\r\n            this.selectedRight = null;\r\n            return;\r\n        }\r\n        \r\n        // Deselect any previously selected item\r\n        if (this.selectedRight) {\r\n            this.selectedRight.classList.remove('selected');\r\n        }\r\n        \r\n        // Select this item\r\n        item.classList.add('selected');\r\n        this.selectedRight = item;\r\n    }\r\n    \r\n    /**\r\n     * Check if we have a match between selected items\r\n     */\r\n    checkForMatch() {\r\n        console.log('Checking for match...');\r\n        if (!this.selectedLeft || !this.selectedRight) return;\r\n        \r\n        const leftId = this.selectedLeft.dataset.itemId;\r\n        const rightId = this.selectedRight.dataset.itemId;\r\n        \r\n        // Update user answer\r\n        const answer = this.getUserAnswer() || {};\r\n        answer[leftId] = rightId;\r\n        this.setUserAnswer(answer);\r\n        \r\n        // Apply fade out animation to both cards\r\n        this.selectedLeft.classList.add('fade-out');\r\n        this.selectedRight.classList.add('fade-out');\r\n        \r\n        // Mark cards as paired (regardless of correct or not)\r\n        this.selectedLeft.classList.add('paired');\r\n        this.selectedRight.classList.add('paired');\r\n        this.selectedLeft.classList.remove('selected');\r\n        this.selectedRight.classList.remove('selected');\r\n        \r\n        // Disable paired cards\r\n        this.selectedLeft.classList.add('disabled');\r\n        this.selectedRight.classList.add('disabled');\r\n        \r\n        // Reset selection\r\n        this.selectedLeft = null;\r\n        this.selectedRight = null;\r\n        \r\n        // Check if all pairs have been matched\r\n        this.checkAllPaired();\r\n    }\r\n\r\n    /**\r\n     * Set user answer\r\n     * \r\n     * @param {Object} answer - The user's answer\r\n     */\r\n    setUserAnswer(answer) {\r\n        this.userAnswer = answer;\r\n    }\r\n    \r\n    /**\r\n     * Show correct matches in review mode\r\n     * \r\n     * @param {Array} items - The items array\r\n     * @param {Object} userAnswers - User's answers\r\n     */\r\n    showCorrectMatches(items, userAnswers) {\r\n        const leftCards = this.container.querySelectorAll('.toeic-matching-left .toeic-matching-card');\r\n        const rightCards = this.container.querySelectorAll('.toeic-matching-right .toeic-matching-card');\r\n        \r\n        items.forEach(item => {\r\n            const leftCard = Array.from(leftCards).find(card => card.dataset.itemId === item.id);\r\n            const rightCard = Array.from(rightCards).find(card => card.dataset.itemId === item.id);\r\n            \r\n            if (leftCard && rightCard) {\r\n                const userAnswer = userAnswers[item.id];\r\n                \r\n                if (userAnswer === item.id) {\r\n                    // Correct match\r\n                    leftCard.classList.add('matched');\r\n                    rightCard.classList.add('matched');\r\n                } else if (userAnswer) {\r\n                    // Incorrect match\r\n                    leftCard.classList.add('incorrect');\r\n                    \r\n                    // Find the incorrectly matched right card\r\n                    const wrongRightCard = Array.from(rightCards).find(card => card.dataset.itemId === userAnswer);\r\n                    if (wrongRightCard) {\r\n                        wrongRightCard.classList.add('incorrect');\r\n                    }\r\n                }\r\n            }\r\n        });\r\n    }\r\n    \r\n    /**\r\n     * Show existing answers\r\n     * \r\n     * @param {Array} items - The items array\r\n     * @param {Object} userAnswers - User's answers\r\n     */\r\n    showExistingAnswers(items, userAnswers) {\r\n        const leftCards = this.container.querySelectorAll('.toeic-matching-left .toeic-matching-card');\r\n        const rightCards = this.container.querySelectorAll('.toeic-matching-right .toeic-matching-card');\r\n        \r\n        let pairedCount = 0;\r\n        \r\n        items.forEach(item => {\r\n            const leftCard = Array.from(leftCards).find(card => card.dataset.itemId === item.id);\r\n            const userAnswer = userAnswers[item.id];\r\n            \r\n            if (leftCard && userAnswer) {\r\n                const rightCard = Array.from(rightCards).find(card => card.dataset.itemId === userAnswer);\r\n                \r\n                if (rightCard) {\r\n                    // Mark as paired\r\n                    leftCard.classList.add('paired');\r\n                    rightCard.classList.add('paired');\r\n                    leftCard.classList.add('disabled');\r\n                    rightCard.classList.add('disabled');\r\n                    pairedCount++;\r\n                }\r\n            }\r\n        });\r\n        \r\n        // Update paired count\r\n        this.pairedCount = pairedCount;\r\n        \r\n        // Check if all pairs are matched\r\n        this.checkAllPaired();\r\n    }\r\n    \r\n    /**\r\n     * Check if all pairs have been matched\r\n     */\r\n    checkAllPaired() {\r\n        if (this.pairedCount >= this.totalPairs) {\r\n            // All pairs have been matched, show submit button\r\n            if (this.submitButton) {\r\n                this.submitButton.classList.add('active');\r\n                this.submitButton.disabled = false;\r\n            }\r\n        } else {\r\n            // Not all pairs matched yet\r\n            if (this.submitButton) {\r\n                this.submitButton.disabled = true;\r\n            }\r\n        }\r\n        \r\n        // Update paired count\r\n        this.pairedCount = this.container.querySelectorAll('.toeic-matching-card.paired').length / 2;\r\n    }\r\n    \r\n    /**\r\n     * Shuffle the response items to prevent ordering hints\r\n     * \r\n     * @param {Array} items - The original items array\r\n     * @returns {Array} Shuffled array of response items\r\n     */\r\n    shuffleResponses(items) {\r\n        // Extract just the response data needed\r\n        const responses = items.map(item => ({\r\n            id: item.id,\r\n            response: item.response\r\n        }));\r\n        \r\n        // Shuffle using Fisher-Yates algorithm\r\n        // Only shuffle in non-review mode to keep consistent ordering in review\r\n        if (!this.isReviewMode) {\r\n            for (let i = responses.length - 1; i > 0; i--) {\r\n                const j = Math.floor(Math.random() * (i + 1));\r\n                [responses[i], responses[j]] = [responses[j], responses[i]];\r\n            }\r\n        }\r\n        \r\n        return responses;\r\n    }\r\n}\r\n\r\nexport default MatchingRenderer;\r\n", "/**\r\n * Multiple Choice Question Renderer\r\n * \r\n * Handles rendering of multiple choice questions with radio button options.\r\n */\r\n\r\nimport Question<PERSON><PERSON><PERSON> from './QuestionRenderer';\r\n\r\nclass MultipleC<PERSON>ice<PERSON><PERSON><PERSON> extends Question<PERSON><PERSON>er {\r\n    /**\r\n     * Render the multiple choice question\r\n     */\r\n    render() {\r\n        this.clearContainer();\r\n        \r\n        // Parse options if needed\r\n        let options = [];\r\n        try {\r\n            options = typeof this.question.options === 'string' ? \r\n                JSON.parse(this.question.options) : this.question.options;\r\n        } catch (e) {\r\n            console.error('Error parsing question options:', e);\r\n            return;\r\n        }\r\n        \r\n        // If no options found, return\r\n        if (!options || !options.length) {\r\n            console.error('No options found for multiple choice question:', this.question.id);\r\n            return;\r\n        }\r\n        \r\n        // Create option elements\r\n        options.forEach((option, index) => {\r\n            const optionElement = this.createOptionElement(option, index);\r\n            this.container.appendChild(optionElement);\r\n        });\r\n    }\r\n    \r\n    /**\r\n     * Create an option element for a multiple choice question\r\n     * \r\n     * @param {Object} option - The option object with value and text\r\n     * @param {number} index - The index of the option\r\n     * @returns {HTMLElement} The created option element\r\n     */\r\n    createOptionElement(option, index) {\r\n        const optionElement = document.createElement('div');\r\n        optionElement.className = 'toeic-question-option';\r\n        \r\n        const inputId = `question-${this.question.id}-option-${index}`;\r\n        const isChecked = this.getUserAnswer() === option.value;\r\n        \r\n        optionElement.innerHTML = `\r\n            <input type=\"radio\" \r\n                   id=\"${inputId}\" \r\n                   name=\"question-${this.question.id}\" \r\n                   value=\"${option.value}\" \r\n                   ${isChecked ? 'checked' : ''}\r\n                   ${this.isReviewMode ? 'disabled' : ''}\r\n            >\r\n            <label for=\"${inputId}\">${option.text}</label>\r\n        `;\r\n        \r\n        // In review mode, highlight correct and incorrect answers\r\n        if (this.isReviewMode && this.question.correct_answer) {\r\n            if (option.value === this.question.correct_answer) {\r\n                optionElement.classList.add('correct');\r\n            } else if (isChecked) {\r\n                optionElement.classList.add('incorrect');\r\n            }\r\n        }\r\n        \r\n        // Add event listener for option selection\r\n        if (!this.isReviewMode) {\r\n            const input = optionElement.querySelector('input');\r\n            if (input && typeof this.options.onAnswerSelected === 'function') {\r\n                input.addEventListener('change', () => {\r\n                    this.options.onAnswerSelected(this.question.id, option.value);\r\n                });\r\n            }\r\n        }\r\n        \r\n        return optionElement;\r\n    }\r\n}\r\n\r\nexport default MultipleChoiceRenderer;\r\n", "/**\r\n * Base Question Renderer\r\n * \r\n * Abstract base class for all question type renderers.\r\n * Each specific question type should extend this class.\r\n */\r\n\r\nclass Question<PERSON>enderer {\r\n    /**\r\n     * Constructor\r\n     * \r\n     * @param {Object} question - The question object to render\r\n     * @param {HTMLElement} container - The container element to render into\r\n     * @param {Object} options - Additional options for rendering\r\n     */\r\n    constructor(question, container, options = {}) {\r\n        if (new.target === QuestionRenderer) {\r\n            throw new Error('QuestionRenderer is an abstract class and cannot be instantiated directly');\r\n        }\r\n        \r\n        this.question = question;\r\n        this.container = container;\r\n        this.options = options;\r\n        this.isReviewMode = options.isReviewMode || false;\r\n        this.userAnswers = options.userAnswers || {};\r\n    }\r\n    \r\n    /**\r\n     * Render the question\r\n     * This method must be implemented by subclasses\r\n     */\r\n    render() {\r\n        throw new Error('render() method must be implemented by subclass');\r\n    }\r\n    \r\n    /**\r\n     * Get the user's answer for this question\r\n     * \r\n     * @returns {*} The user's answer or null if not answered\r\n     */\r\n    getUserAnswer() {\r\n        return this.userAnswers[this.question.id] || null;\r\n    }\r\n    \r\n    /**\r\n     * Check if the question has been answered\r\n     * \r\n     * @returns {boolean} True if the question has been answered\r\n     */\r\n    isAnswered() {\r\n        return this.question.id in this.userAnswers;\r\n    }\r\n    \r\n    /**\r\n     * Clear the container before rendering\r\n     */\r\n    clearContainer() {\r\n        if (this.container) {\r\n            this.container.innerHTML = '';\r\n        }\r\n    }\r\n    \r\n    /**\r\n     * Create a basic wrapper element for the question\r\n     * \r\n     * @returns {HTMLElement} The wrapper element\r\n     */\r\n    createWrapper() {\r\n        const wrapper = document.createElement('div');\r\n        wrapper.className = 'toeic-question-wrapper';\r\n        wrapper.dataset.questionId = this.question.id;\r\n        wrapper.dataset.questionType = this.question.type;\r\n        return wrapper;\r\n    }\r\n}\r\n\r\nexport default QuestionRenderer;\r\n", "/**\r\n * Question Renderer Factory\r\n * \r\n * Factory class that creates the appropriate renderer for a given question type.\r\n */\r\n\r\nimport MultipleC<PERSON>iceRenderer from './MultipleChoiceRenderer';\r\nimport TextInputRenderer from './TextInputRenderer';\r\nimport MatchingRenderer from './MatchingRenderer';\r\n\r\nclass QuestionRendererFactory {\r\n    /**\r\n     * Create a renderer instance for the given question\r\n     * \r\n     * @param {Object} question - The question object\r\n     * @param {HTMLElement} container - The container to render into\r\n     * @param {Object} options - Additional options for rendering\r\n     * @returns {QuestionRenderer} An instance of the appropriate renderer\r\n     * @throws {Error} If no renderer is available for the question type\r\n     */\r\n    static createRenderer(question, container, options = {}) {\r\n        if (!question || !question.question_type) {\r\n            throw new Error('Question object is invalid or missing type');\r\n        }\r\n        \r\n        // Select the appropriate renderer based on question type\r\n        switch (question.question_type.toLowerCase()) {\r\n            case 'multiple_choice':\r\n                return new MultipleChoiceRenderer(question, container, options);\r\n                \r\n            case 'text_input':\r\n            case 'short_answer':\r\n            case 'fill_in_blank':\r\n                return new TextInputRenderer(question, container, options);\r\n                \r\n            case 'matching':\r\n                return new MatchingRenderer(question, container, options);\r\n                \r\n            default:\r\n                throw new Error(`No renderer available for question type: ${question.question_type}`);\r\n        }\r\n    }\r\n    \r\n    /**\r\n     * Check if a renderer is available for the given question type\r\n     * \r\n     * @param {string} questionType - The question type to check\r\n     * @returns {boolean} True if a renderer is available\r\n     */\r\n    static hasRendererForType(questionType) {\r\n        if (!questionType) return false;\r\n        \r\n        const supportedTypes = [\r\n            'multiple_choice',\r\n            'text_input',\r\n            'short_answer',\r\n            'fill_in_blank',\r\n            'matching'\r\n        ];\r\n        \r\n        return supportedTypes.includes(questionType.toLowerCase());\r\n    }\r\n}\r\n\r\nexport default QuestionRendererFactory;\r\n", "/**\r\n * Text Input Question Renderer\r\n * \r\n * Handles rendering of text input questions where users type their answers.\r\n */\r\n\r\nimport Question<PERSON><PERSON><PERSON> from './QuestionRenderer';\r\n\r\nclass TextInputRenderer extends Question<PERSON>enderer {\r\n    /**\r\n     * Render the text input question\r\n     */\r\n    render() {\r\n        this.clearContainer();\r\n        \r\n        // Create wrapper\r\n        const wrapper = this.createWrapper();\r\n        \r\n        // Create input element\r\n        const inputContainer = document.createElement('div');\r\n        inputContainer.className = 'toeic-question-input-container';\r\n        \r\n        const inputId = `question-${this.question.id}-input`;\r\n        const userAnswer = this.getUserAnswer() || '';\r\n        \r\n        inputContainer.innerHTML = `\r\n            <input type=\"text\" \r\n                   id=\"${inputId}\" \r\n                   class=\"toeic-question-text-input\"\r\n                   value=\"${userAnswer}\"\r\n                   placeholder=\"${this.question.placeholder || 'Type your answer here...'}\"\r\n                   ${this.isReviewMode ? 'disabled' : ''}\r\n            >\r\n        `;\r\n        \r\n        // In review mode, show correct answer and highlight\r\n        if (this.isReviewMode && this.question.correct_answer) {\r\n            const reviewInfo = document.createElement('div');\r\n            reviewInfo.className = 'toeic-question-review-info';\r\n            \r\n            const isCorrect = userAnswer.toLowerCase() === this.question.correct_answer.toLowerCase();\r\n            reviewInfo.classList.add(isCorrect ? 'correct' : 'incorrect');\r\n            \r\n            reviewInfo.innerHTML = `\r\n                <div class=\"toeic-question-correct-answer\">\r\n                    <strong>Correct answer:</strong> ${this.question.correct_answer}\r\n                </div>\r\n            `;\r\n            \r\n            inputContainer.appendChild(reviewInfo);\r\n        }\r\n        \r\n        wrapper.appendChild(inputContainer);\r\n        this.container.appendChild(wrapper);\r\n        \r\n        // Add event listener for input changes\r\n        if (!this.isReviewMode) {\r\n            const input = wrapper.querySelector('input');\r\n            if (input && typeof this.options.onAnswerSelected === 'function') {\r\n                input.addEventListener('change', (e) => {\r\n                    this.options.onAnswerSelected(this.question.id, e.target.value);\r\n                });\r\n                \r\n                // Also listen for input event to capture typing in real-time\r\n                input.addEventListener('input', (e) => {\r\n                    this.options.onAnswerSelected(this.question.id, e.target.value);\r\n                });\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\nexport default TextInputRenderer;\r\n", "/**\r\n * Question Renderers Index\r\n * \r\n * Exports all question renderers and the factory.\r\n */\r\n\r\nimport QuestionRenderer from './QuestionRenderer';\r\nimport MultipleC<PERSON>iceRenderer from './MultipleChoiceRenderer';\r\nimport TextInputRenderer from './TextInputRenderer';\r\nimport MatchingRenderer from './MatchingRenderer';\r\nimport QuestionRendererFactory from './QuestionRendererFactory';\r\n\r\nexport {\r\n    QuestionRenderer,\r\n    MultipleChoiceRenderer,\r\n    TextInputRenderer,\r\n    MatchingRenderer,\r\n    QuestionRendererFactory\r\n};\r\n", "// pronunciation-page.js\r\n\r\nimport PronunciationService from '../services/PronunciationService';\r\n\r\nclass PronunciationRecorder {\r\n    constructor({paragraphText}) {\r\n        this.isRecording = false;\r\n        this.mediaRecorder = null;\r\n        this.audioChunks = [];\r\n        this.startTime = null;\r\n        this.timerInterval = null;\r\n        this.paragraphText = paragraphText;\r\n        \r\n        // Initialize pronunciation service\r\n        this.pronunciationService = new PronunciationService();\r\n        \r\n        // DOM elements\r\n        this.recordBtn = document.getElementById('record-btn');\r\n        this.recordingStatus = document.querySelector('.recording-status');\r\n        this.recordingDuration = document.querySelector('.recording-duration');\r\n        this.durationText = document.querySelector('.recording-duration-text');\r\n        this.suggestionBtns = document.querySelectorAll('.suggestion-btn');\r\n        \r\n        this.init();\r\n    }\r\n    \r\n    init() {\r\n        // Check if required elements exist\r\n        if (!this.recordBtn) {\r\n            console.warn('Record button not found');\r\n            return;\r\n        }\r\n\r\n        console.log('Record button found');\r\n        \r\n        // Initially hide recording status elements\r\n        this.hideRecordingElements();\r\n        \r\n        // Bind event listeners\r\n        this.recordBtn.addEventListener('click', () => this.toggleRecording());\r\n        this.suggestionBtns.forEach(button => button.addEventListener('click', (e) => this.handleSuggestion(e)));\r\n        \r\n        // Check for microphone permissions\r\n        this.checkMicrophonePermissions();\r\n    }\r\n    \r\n    async checkMicrophonePermissions() {\r\n        try {\r\n            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });\r\n            stream.getTracks().forEach(track => track.stop());\r\n            console.log('Microphone access granted');\r\n        } catch (error) {\r\n            console.error('Microphone access denied:', error);\r\n            this.showError('Microphone access is required for recording. Please allow microphone permissions.');\r\n        }\r\n    }\r\n    \r\n    async toggleRecording() {\r\n        if (this.isRecording) {\r\n            this.stopRecording();\r\n        } else {\r\n            await this.startRecording();\r\n        }\r\n    }\r\n    \r\n    async startRecording() {\r\n        try {\r\n            // Get user media with optimized settings for speech (16kHz, mono)\r\n            const stream = await navigator.mediaDevices.getUserMedia({ \r\n                audio: {\r\n                    echoCancellation: true,\r\n                    noiseSuppression: true,\r\n                    sampleRate: 16000, // Reduced from 44100 to 16000 for speech\r\n                    channelCount: 1     // Mono channel for speech\r\n                }\r\n            });\r\n            \r\n            // Create MediaRecorder with fallback MIME types\r\n            let mimeType = 'audio/webm;codecs=opus';\r\n            if (!MediaRecorder.isTypeSupported(mimeType)) {\r\n                mimeType = 'audio/webm';\r\n                if (!MediaRecorder.isTypeSupported(mimeType)) {\r\n                    mimeType = 'audio/mp4';\r\n                    if (!MediaRecorder.isTypeSupported(mimeType)) {\r\n                        mimeType = ''; // Let browser choose\r\n                    }\r\n                }\r\n            }\r\n            \r\n            this.mediaRecorder = new MediaRecorder(stream, {\r\n                mimeType: mimeType\r\n            });\r\n            \r\n            // Store the actual MIME type used\r\n            this.actualMimeType = mimeType;\r\n            \r\n            // Reset audio chunks\r\n            this.audioChunks = [];\r\n            \r\n            // Set up event handlers\r\n            this.mediaRecorder.ondataavailable = (event) => {\r\n                if (event.data.size > 0) {\r\n                    this.audioChunks.push(event.data);\r\n                }\r\n            };\r\n            \r\n            this.mediaRecorder.onstop = () => {\r\n                this.processRecording();\r\n            };\r\n            \r\n            // Start recording\r\n            this.mediaRecorder.start();\r\n            this.isRecording = true;\r\n            this.startTime = Date.now();\r\n            \r\n            // Update UI\r\n            this.showRecordingElements();\r\n            this.updateButtonState();\r\n            this.startTimer();\r\n        } catch (error) {\r\n            console.error('Error starting recording:', error);\r\n            this.showError('Failed to start recording. Please check your microphone permissions.');\r\n        }\r\n    }\r\n    \r\n    stopRecording() {\r\n        if (this.mediaRecorder && this.isRecording) {\r\n            this.mediaRecorder.stop();\r\n            this.isRecording = false;\r\n            \r\n            // Stop all tracks\r\n            if (this.mediaRecorder.stream) {\r\n                this.mediaRecorder.stream.getTracks().forEach(track => track.stop());\r\n            }\r\n            \r\n            // Update UI\r\n            this.hideRecordingElements();\r\n            this.updateButtonState();\r\n            this.stopTimer();\r\n            \r\n            console.log('Recording stopped');\r\n        }\r\n    }\r\n    \r\n    processRecording() {\r\n        if (this.audioChunks.length === 0) {\r\n            this.showError('No audio data recorded');\r\n            return;\r\n        }\r\n        \r\n        // Create blob with the correct MIME type that was actually recorded\r\n        const audioBlob = new Blob(this.audioChunks, { \r\n            type: this.actualMimeType || 'audio/webm' \r\n        });\r\n\r\n        // Convert to WAV if needed for better compatibility\r\n        this.convertToWAV(audioBlob).then(wavBlob => {\r\n            this.submitForGrading(wavBlob);\r\n        }).catch(error => {\r\n            console.warn('WAV conversion failed, using original format:', error);\r\n            // Fallback to original blob if conversion fails\r\n            this.submitForGrading(audioBlob);\r\n        });\r\n    }\r\n    \r\n    async convertToWAV(audioBlob) {\r\n        return new Promise((resolve, reject) => {\r\n            const audioContext = new (window.AudioContext || window.webkitAudioContext)();\r\n            const fileReader = new FileReader();\r\n            \r\n            fileReader.onload = async (e) => {\r\n                try {\r\n                    // Decode the audio data\r\n                    const arrayBuffer = e.target.result;\r\n                    const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);\r\n                    \r\n                    // Trim silence from the audio buffer\r\n                    const trimmedBuffer = this.trimSilence(audioBuffer, audioContext);\r\n                    \r\n                    // Convert to WAV\r\n                    const wavBlob = this.audioBufferToWav(trimmedBuffer);\r\n                    resolve(wavBlob);\r\n                } catch (error) {\r\n                    reject(error);\r\n                }\r\n            };\r\n            \r\n            fileReader.onerror = () => reject(new Error('Failed to read audio file'));\r\n            fileReader.readAsArrayBuffer(audioBlob);\r\n        });\r\n    }\r\n    \r\n    /**\r\n     * Trim silence from the beginning and end of an audio buffer\r\n     * @param {AudioBuffer} audioBuffer - The original audio buffer\r\n     * @param {AudioContext} audioContext - The audio context to use for creating new buffers\r\n     * @param {number} silenceThreshold - Threshold for silence detection (0-1, default: 0.01)\r\n     * @param {number} marginMs - Margin to keep before/after speech in milliseconds (default: 100ms)\r\n     * @returns {AudioBuffer} - Trimmed audio buffer\r\n     */\r\n    trimSilence(audioBuffer, audioContext, silenceThreshold = 0.01, marginMs = 100) {\r\n        const sampleRate = audioBuffer.sampleRate;\r\n        const numberOfChannels = audioBuffer.numberOfChannels;\r\n        const length = audioBuffer.length;\r\n        \r\n        // Convert margin from milliseconds to samples\r\n        const marginSamples = Math.floor((marginMs / 1000) * sampleRate);\r\n        \r\n        // Get audio data for analysis (use first channel if stereo)\r\n        const audioData = audioBuffer.getChannelData(0);\r\n        \r\n        // Find start of speech (first non-silent sample)\r\n        let startIndex = 0;\r\n        for (let i = 0; i < length; i++) {\r\n            if (Math.abs(audioData[i]) > silenceThreshold) {\r\n                startIndex = Math.max(0, i - marginSamples);\r\n                break;\r\n            }\r\n        }\r\n        \r\n        // Find end of speech (last non-silent sample)\r\n        let endIndex = length - 1;\r\n        for (let i = length - 1; i >= 0; i--) {\r\n            if (Math.abs(audioData[i]) > silenceThreshold) {\r\n                endIndex = Math.min(length - 1, i + marginSamples);\r\n                break;\r\n            }\r\n        }\r\n        \r\n        // If no speech detected, return a minimal buffer\r\n        if (startIndex >= endIndex) {\r\n            console.warn('No speech detected in audio, returning minimal buffer');\r\n            const minimalLength = Math.floor(sampleRate * 0.1); // 100ms minimal buffer\r\n            const minimalBuffer = audioContext.createBuffer(\r\n                numberOfChannels, \r\n                minimalLength, \r\n                sampleRate\r\n            );\r\n            return minimalBuffer;\r\n        }\r\n        \r\n        // Calculate new buffer length\r\n        const newLength = endIndex - startIndex + 1;\r\n        \r\n        // Create new trimmed buffer\r\n        const trimmedBuffer = audioContext.createBuffer(\r\n            numberOfChannels,\r\n            newLength,\r\n            sampleRate\r\n        );\r\n        \r\n        // Copy trimmed audio data for all channels\r\n        for (let channel = 0; channel < numberOfChannels; channel++) {\r\n            const originalData = audioBuffer.getChannelData(channel);\r\n            const trimmedData = trimmedBuffer.getChannelData(channel);\r\n            \r\n            for (let i = 0; i < newLength; i++) {\r\n                trimmedData[i] = originalData[startIndex + i];\r\n            }\r\n        }\r\n        \r\n        // Log trimming results\r\n        const originalDuration = (length / sampleRate * 1000).toFixed(0);\r\n        const trimmedDuration = (newLength / sampleRate * 1000).toFixed(0);\r\n        const trimmedStart = (startIndex / sampleRate * 1000).toFixed(0);\r\n        const trimmedEnd = ((length - endIndex - 1) / sampleRate * 1000).toFixed(0);\r\n        \r\n        console.log(`Audio trimmed: ${originalDuration}ms → ${trimmedDuration}ms (removed ${trimmedStart}ms from start, ${trimmedEnd}ms from end)`);\r\n        \r\n        return trimmedBuffer;\r\n    }\r\n    \r\n    audioBufferToWav(buffer) {\r\n        // Target settings for 256kbps (approximately)\r\n        const targetSampleRate = 16000; // 16kHz for speech quality\r\n        const targetChannels = 1;       // Mono for speech\r\n        const bitsPerSample = 16;\r\n        \r\n        // Resample and convert to mono if needed\r\n        const resampledBuffer = this.resampleAndConvertToMono(buffer, targetSampleRate);\r\n        \r\n        const length = resampledBuffer.length;\r\n        const numberOfChannels = targetChannels;\r\n        const sampleRate = targetSampleRate;\r\n        const bytesPerSample = bitsPerSample / 8;\r\n        const blockAlign = numberOfChannels * bytesPerSample;\r\n        const byteRate = sampleRate * blockAlign; // This will be 32000 bytes/sec = 256kbps\r\n        const dataSize = length * blockAlign;\r\n        const bufferSize = 44 + dataSize;\r\n        \r\n        const arrayBuffer = new ArrayBuffer(bufferSize);\r\n        const view = new DataView(arrayBuffer);\r\n        \r\n        // Write WAV header\r\n        const writeString = (offset, string) => {\r\n            for (let i = 0; i < string.length; i++) {\r\n                view.setUint8(offset + i, string.charCodeAt(i));\r\n            }\r\n        };\r\n        \r\n        // RIFF header\r\n        writeString(0, 'RIFF');\r\n        view.setUint32(4, bufferSize - 8, true);\r\n        writeString(8, 'WAVE');\r\n        \r\n        // Format chunk\r\n        writeString(12, 'fmt ');\r\n        view.setUint32(16, 16, true); // Subchunk1Size\r\n        view.setUint16(20, 1, true); // AudioFormat (PCM)\r\n        view.setUint16(22, numberOfChannels, true);\r\n        view.setUint32(24, sampleRate, true);\r\n        view.setUint32(28, byteRate, true);\r\n        view.setUint16(32, blockAlign, true);\r\n        view.setUint16(34, bitsPerSample, true);\r\n        \r\n        // Data chunk\r\n        writeString(36, 'data');\r\n        view.setUint32(40, dataSize, true);\r\n        \r\n        // Write audio data (mono)\r\n        let offset = 44;\r\n        for (let i = 0; i < length; i++) {\r\n            const sample = Math.max(-1, Math.min(1, resampledBuffer[i]));\r\n            const intSample = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;\r\n            view.setInt16(offset, intSample, true);\r\n            offset += 2;\r\n        }\r\n        \r\n        return new Blob([arrayBuffer], { type: 'audio/wav' });\r\n    }\r\n    \r\n    resampleAndConvertToMono(buffer, targetSampleRate) {\r\n        const originalSampleRate = buffer.sampleRate;\r\n        const originalLength = buffer.length;\r\n        const numberOfChannels = buffer.numberOfChannels;\r\n        \r\n        // Calculate new length after resampling\r\n        const resampleRatio = targetSampleRate / originalSampleRate;\r\n        const newLength = Math.floor(originalLength * resampleRatio);\r\n        \r\n        // Create output array\r\n        const resampledData = new Float32Array(newLength);\r\n        \r\n        // Convert to mono first (mix all channels)\r\n        const monoData = new Float32Array(originalLength);\r\n        for (let i = 0; i < originalLength; i++) {\r\n            let sum = 0;\r\n            for (let channel = 0; channel < numberOfChannels; channel++) {\r\n                sum += buffer.getChannelData(channel)[i];\r\n            }\r\n            monoData[i] = sum / numberOfChannels; // Average all channels\r\n        }\r\n        \r\n        // Resample using linear interpolation\r\n        for (let i = 0; i < newLength; i++) {\r\n            const originalIndex = i / resampleRatio;\r\n            const index = Math.floor(originalIndex);\r\n            const fraction = originalIndex - index;\r\n            \r\n            if (index + 1 < originalLength) {\r\n                // Linear interpolation between two samples\r\n                resampledData[i] = monoData[index] * (1 - fraction) + monoData[index + 1] * fraction;\r\n            } else {\r\n                // Use the last sample if we're at the end\r\n                resampledData[i] = monoData[index] || 0;\r\n            }\r\n        }\r\n        \r\n        return resampledData;\r\n    }\r\n    \r\n    async submitForGrading(audioBlob) {\r\n        try {\r\n            // Show loading state\r\n            this.showLoadingState();\r\n            \r\n            // Get current practice parameters\r\n            const params = {\r\n                topic_id: topicItemData.topic_id || '', // topicItemData is global variable\r\n                id: topicItemData.id || '',\r\n                item_type: topicItemData.item_type || 'question'\r\n            };\r\n            \r\n            // Submit to pronunciation service\r\n            const result = await this.pronunciationService.gradeRecording(audioBlob, params);\r\n            \r\n            // Display results\r\n            this.displayResults(result);\r\n            \r\n        } catch (error) {\r\n            console.error('Error submitting recording:', error);\r\n            this.showError(error.message || 'Failed to submit recording for grading');\r\n        } finally {\r\n            this.hideLoadingState();\r\n        }\r\n    }\r\n    \r\n    displayResults(data) {\r\n        // Extract real data from Azure pronunciation assessment response\r\n        if (!data || !data.analysis) {\r\n            this.showError('Invalid pronunciation analysis data received');\r\n            return;\r\n        }\r\n\r\n        const analysis = data.analysis;\r\n        const scores = analysis.scores || {};\r\n        const itemData = data.item_data || {};\r\n        \r\n        // Extract scores with fallback values\r\n        const overallScore = Math.round(analysis.overall_score || 0);\r\n        const accuracy = Math.round(scores.accuracy || 0);\r\n        const fluency = Math.round(scores.fluency || 0);\r\n        const completeness = Math.round(scores.completeness || 0);\r\n        const pronunciation = Math.round(scores.pronunciation || 0);\r\n        \r\n        // Extract text data\r\n        const expectedText = data.expected_text || itemData.word || 'Unknown';\r\n        const recognizedText = analysis.recognized_text || 'Not recognized';\r\n        const feedback = analysis.feedback || 'No feedback available';\r\n        \r\n        // Extract additional information\r\n        const detectedIssues = analysis.detected_issues || [];\r\n        const suggestions = analysis.suggestions || [];\r\n        \r\n        // Build issues and suggestions HTML\r\n        let issuesHtml = '';\r\n        if (detectedIssues.length > 0) {\r\n            issuesHtml = `\r\n                <div class=\"detected-issues\">\r\n                    <h4>Detected Issues:</h4>\r\n                    <ul>\r\n                        ${detectedIssues.map(issue => `<li>${issue}</li>`).join('')}\r\n                    </ul>\r\n                </div>\r\n            `;\r\n        }\r\n        \r\n        let suggestionsHtml = '';\r\n        if (suggestions.length > 0) {\r\n            suggestionsHtml = `\r\n                <div class=\"suggestions\">\r\n                    <h4>Suggestions for Improvement:</h4>\r\n                    <ul>\r\n                        ${suggestions.map(suggestion => `<li>${suggestion}</li>`).join('')}\r\n                    </ul>\r\n                </div>\r\n            `;\r\n        }\r\n        \r\n        // Update practice result section\r\n        const practiceResult = document.querySelector('.practice-result');\r\n        if (practiceResult) {\r\n            practiceResult.innerHTML = `\r\n                <div class=\"result-card\">\r\n                    <h3>Pronunciation Analysis</h3>\r\n                    \r\n                    <div class=\"text-comparison\">\r\n                        <div class=\"expected-text\">\r\n                            <span class=\"text-label\">Expected:</span>\r\n                            <span class=\"text-value\">\"${expectedText}\"</span>\r\n                        </div>\r\n                        <div class=\"recognized-text\">\r\n                            <span class=\"text-label\">You said:</span>\r\n                            <span class=\"text-value\">\"${recognizedText}\"</span>\r\n                        </div>\r\n                    </div>\r\n                    \r\n                    <div class=\"overall-score\">\r\n                        <div class=\"circular-progress ${this.getScoreClass(overallScore)}\">\r\n                            <div class=\"circular-progress-inner\">\r\n                                <div class=\"circular-progress-circle\">\r\n                                    <div class=\"circular-progress-mask full\">\r\n                                        <div class=\"circular-progress-fill\" style=\"transform: rotate(${overallScore * 3.6}deg)\"></div>\r\n                                    </div>\r\n                                    <div class=\"circular-progress-mask\">\r\n                                        <div class=\"circular-progress-fill\" style=\"transform: rotate(${Math.min(180, overallScore * 3.6)}deg)\"></div>\r\n                                    </div>\r\n                                    <div class=\"circular-progress-inside\">\r\n                                        <span class=\"circular-progress-percentage\">${overallScore}<span class=\"percentage-sign\">%</span></span>\r\n                                        <span class=\"circular-progress-label\">Overall Score</span>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    \r\n                    <div class=\"detailed-metrics\">\r\n                        <div class=\"metric\">\r\n                            <span class=\"metric-label\">Accuracy:</span>\r\n                            <div class=\"metric-bar\">\r\n                                <div class=\"metric-fill ${this.getScoreClass(accuracy)}\" style=\"width: ${accuracy}%\"></div>\r\n                            </div>\r\n                            <span class=\"metric-value\">${accuracy}%</span>\r\n                        </div>\r\n                        <div class=\"metric\">\r\n                            <span class=\"metric-label\">Fluency:</span>\r\n                            <div class=\"metric-bar\">\r\n                                <div class=\"metric-fill ${this.getScoreClass(fluency)}\" style=\"width: ${fluency}%\"></div>\r\n                            </div>\r\n                            <span class=\"metric-value\">${fluency}%</span>\r\n                        </div>\r\n                        <div class=\"metric\">\r\n                            <span class=\"metric-label\">Completeness:</span>\r\n                            <div class=\"metric-bar\">\r\n                                <div class=\"metric-fill ${this.getScoreClass(completeness)}\" style=\"width: ${completeness}%\"></div>\r\n                            </div>\r\n                            <span class=\"metric-value\">${completeness}%</span>\r\n                        </div>\r\n                        <div class=\"metric\">\r\n                            <span class=\"metric-label\">Pronunciation:</span>\r\n                            <div class=\"metric-bar\">\r\n                                <div class=\"metric-fill ${this.getScoreClass(pronunciation)}\" style=\"width: ${pronunciation}%\"></div>\r\n                            </div>\r\n                            <span class=\"metric-value\">${pronunciation}%</span>\r\n                        </div>\r\n                    </div>\r\n                    \r\n                    <div class=\"feedback-text\">\r\n                        <h4>Feedback:</h4>\r\n                        <p>${feedback}</p>\r\n                    </div>\r\n                    \r\n                    ${issuesHtml}\r\n                    ${suggestionsHtml}\r\n                    \r\n                    <div class=\"result-actions\">\r\n                        <button class=\"btn btn-primary\" onclick=\"pronunciationRecorder.resetRecording()\">Try Again</button>\r\n                        <button class=\"btn btn-secondary\" onclick=\"window.history.back()\">Back to Topics</button>\r\n                    </div>\r\n                </div>\r\n            `;\r\n        }\r\n        \r\n        console.log('Pronunciation results displayed:', {\r\n            overallScore,\r\n            scores: { accuracy, fluency, completeness, pronunciation },\r\n            expectedText,\r\n            recognizedText,\r\n            feedback,\r\n            detectedIssues,\r\n            suggestions\r\n        });\r\n    }\r\n    \r\n    getScoreClass(score) {\r\n        if (score >= 80) return 'score-excellent';\r\n        if (score >= 60) return 'score-good';\r\n        if (score >= 40) return 'score-fair';\r\n        return 'score-poor';\r\n    }\r\n    \r\n    showRecordingElements() {\r\n        if (this.recordingStatus) {\r\n            this.recordingStatus.style.display = 'block';\r\n        }\r\n        if (this.recordingDuration) {\r\n            this.recordingDuration.style.display = 'flex';\r\n        }\r\n    }\r\n    \r\n    hideRecordingElements() {\r\n        if (this.recordingStatus) {\r\n            this.recordingStatus.style.display = 'none';\r\n        }\r\n        if (this.recordingDuration) {\r\n            this.recordingDuration.style.display = 'none';\r\n        }\r\n    }\r\n    \r\n    updateButtonState() {\r\n        if (this.recordBtn) {\r\n            const recordText = this.recordBtn.querySelector('.record-text');\r\n            if (this.isRecording) {\r\n                this.recordBtn.classList.add('recording');\r\n                if (recordText) recordText.textContent = 'Stop Recording';\r\n            } else {\r\n                this.recordBtn.classList.remove('recording');\r\n                if (recordText) recordText.textContent = 'Record now';\r\n            }\r\n        }\r\n    }\r\n    \r\n    startTimer() {\r\n        this.timerInterval = setInterval(() => {\r\n            if (this.startTime && this.durationText) {\r\n                const elapsed = Date.now() - this.startTime;\r\n                const minutes = Math.floor(elapsed / 60000);\r\n                const seconds = Math.floor((elapsed % 60000) / 1000);\r\n                \r\n                const timeString = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;\r\n                this.durationText.textContent = timeString;\r\n            }\r\n        }, 100);\r\n    }\r\n    \r\n    stopTimer() {\r\n        if (this.timerInterval) {\r\n            clearInterval(this.timerInterval);\r\n            this.timerInterval = null;\r\n        }\r\n    }\r\n    \r\n    showLoadingState() {\r\n        if (this.recordBtn) {\r\n            this.recordBtn.disabled = true;\r\n            const recordText = this.recordBtn.querySelector('.record-text');\r\n            if (recordText) recordText.textContent = 'Processing...';\r\n        }\r\n    }\r\n    \r\n    hideLoadingState() {\r\n        if (this.recordBtn) {\r\n            this.recordBtn.disabled = false;\r\n            this.updateButtonState();\r\n        }\r\n    }\r\n    \r\n    showError(message) {\r\n        console.error('PronunciationRecorder Error:', message);\r\n        \r\n        // You can implement a more sophisticated error display here\r\n        const practiceResult = document.querySelector('.practice-result');\r\n        if (practiceResult) {\r\n            practiceResult.innerHTML = `\r\n                <div class=\"error-message\">\r\n                    <h3>Error</h3>\r\n                    <p>${message}</p>\r\n                    <button class=\"btn btn-primary\" onclick=\"pronunciationRecorder.resetRecording()\">Try Again</button>\r\n                </div>\r\n            `;\r\n        }\r\n    }\r\n    \r\n    resetRecording() {\r\n        // Reset the recording state\r\n        this.isRecording = false;\r\n        this.audioChunks = [];\r\n        this.startTime = null;\r\n        \r\n        // Clear any existing intervals\r\n        this.stopTimer();\r\n        \r\n        // Reset UI\r\n        this.hideRecordingElements();\r\n        this.updateButtonState();\r\n        \r\n        // Clear results\r\n        const practiceResult = document.querySelector('.practice-result');\r\n        if (practiceResult) {\r\n            practiceResult.innerHTML = '';\r\n        }\r\n        \r\n        // Reset duration text\r\n        if (this.durationText) {\r\n            this.durationText.textContent = '00:00';\r\n        }\r\n    }\r\n    \r\n    getNonce() {\r\n        // Get WordPress nonce if available\r\n        return window.toeic_ajax_nonce || '';\r\n    }\r\n\r\n    handleSuggestion(e) {\r\n        const prompt = e.target.dataset.prompt;\r\n        const subject = this.paragraphText;\r\n        this.getSuggestion({prompt, subject})\r\n    .then((data) => {\r\n            this.showSuggestion(data);\r\n        });\r\n    }\r\n\r\n    showSuggestion({title, suggestion}) {\r\n        let template = `\r\n            <div class=\"suggestion-item\">\r\n                <div class=\"suggestion-item__content\">${suggestion}</div>\r\n            </div>\r\n        `\r\n\r\n        const suggestionsResult = document.querySelector('.suggestion-result');\r\n        if (suggestionsResult) {\r\n            suggestionsResult.innerHTML += template;\r\n        }\r\n    }\r\n\r\n    async getSuggestion({prompt, subject}) {\r\n        try {\r\n            const data = await this.pronunciationService.getSuggestions({prompt, subject});\r\n            return {\r\n                \"suggestion\": data.suggestion,\r\n            };\r\n        } catch (error) {\r\n            console.error('Error fetching suggestions:', error);\r\n        }\r\n    }\r\n}\r\n\r\nexport {PronunciationRecorder};\r\n", "/**\r\n * TOEIC Test Detail Page\r\n * \r\n * Handles the test-taking functionality including:\r\n * 1. Timer countdown with progress bar\r\n * 2. Question navigation and display\r\n * 3. Answer selection and submission\r\n */\r\n\r\nimport Loader from '../components/Loader';\r\nimport testService from '../services/TestService';\r\nimport { QuestionRendererFactory } from '../components/questions';\r\n\r\nclass TestDetailPage {\r\n    /**\r\n     * Constructor\r\n     * \r\n     * @param {HTMLElement} element - The test detail page container element\r\n     */\r\n    constructor(element) {\r\n        // Main container and data\r\n        this.element = element;\r\n        this.testData = window.toeicTestData || {};\r\n        this.questions = [];\r\n        this.sections = this.testData.sections || [];\r\n        this.currentQuestionIndex = 0;\r\n        this.userAnswers = {};\r\n        this.isTestActive = false;\r\n        this.isReviewMode = false;\r\n        \r\n        // Timer elements\r\n        this.timerBar = document.getElementById('toeic-timer-bar');\r\n        this.timerDisplay = document.getElementById('toeic-timer-display');\r\n        this.timerMinutes = document.getElementById('toeic-timer-minutes');\r\n        this.timerSeconds = document.getElementById('toeic-timer-seconds');\r\n        \r\n        // Question elements\r\n        this.questionContainer = document.getElementById('toeic-question-container');\r\n        this.sectionInfo = document.getElementById('toeic-section-info');\r\n        this.sectionTitle = document.getElementById('toeic-section-title');\r\n        this.sectionInstructions = document.getElementById('toeic-section-instructions');\r\n        this.questionNumber = document.getElementById('toeic-question-number');\r\n        this.questionText = document.getElementById('toeic-question-text');\r\n        this.questionOptions = document.getElementById('toeic-question-options');\r\n        \r\n        // Navigation elements\r\n        this.prevButton = document.getElementById('toeic-prev-btn');\r\n        this.nextButton = document.getElementById('toeic-next-btn');\r\n        this.submitButton = document.getElementById('toeic-submit-btn');\r\n        this.questionIndicators = document.getElementById('toeic-question-indicators');\r\n        this.currentQuestionDisplay = document.getElementById('toeic-current-question');\r\n        this.totalQuestionsDisplay = document.getElementById('toeic-total-questions');\r\n        \r\n        // Results elements\r\n        this.resultsContainer = document.getElementById('toeic-results-container');\r\n        this.resultsContent = document.getElementById('toeic-results-content');\r\n        this.reviewButton = document.getElementById('toeic-review-btn');\r\n        \r\n        // Timer variables\r\n        this.timeLimit = this.testData.time_limit || 3600; // Default 60 minutes\r\n        this.timeRemaining = this.timeLimit;\r\n        this.timerInterval = null;\r\n        \r\n        // Create loader\r\n        this.loader = Loader.forElement(this.element, {\r\n            text: 'Loading test data...'\r\n        });\r\n    }\r\n    \r\n    /**\r\n     * Initialize the test page\r\n     */\r\n    init() {\r\n        // Load questions\r\n        this.loadQuestions();\r\n        \r\n        // Set up event listeners\r\n        this.setupEventListeners();\r\n    }\r\n    \r\n    /**\r\n     * Load questions from the server\r\n     */\r\n    loadQuestions() {\r\n        if (!this.testData.id) {\r\n            console.error('No test ID provided');\r\n            return;\r\n        }\r\n        \r\n        this.loader.show('Loading test questions...');\r\n        \r\n        testService.getTestQuestions(this.testData.id)\r\n            .then(data => {\r\n                this.questions = data.questions || [];\r\n                this.renderQuestionIndicators();\r\n                this.startTest();\r\n            })\r\n            .catch(error => {\r\n                console.error('Error loading questions:', error);\r\n                alert('Failed to load test questions. Please try again.');\r\n            })\r\n            .finally(() => {\r\n                this.loader.hide();\r\n            });\r\n    }\r\n    \r\n    /**\r\n     * Set up event listeners\r\n     */\r\n    setupEventListeners() {\r\n        // Navigation buttons\r\n        if (this.prevButton) {\r\n            this.prevButton.addEventListener('click', this.goToPreviousQuestion.bind(this));\r\n        }\r\n        \r\n        if (this.nextButton) {\r\n            this.nextButton.addEventListener('click', this.goToNextQuestion.bind(this));\r\n        }\r\n        \r\n        if (this.submitButton) {\r\n            this.submitButton.addEventListener('click', this.confirmSubmitTest.bind(this));\r\n        }\r\n        \r\n        if (this.reviewButton) {\r\n            this.reviewButton.addEventListener('click', this.startReviewMode.bind(this));\r\n        }\r\n        \r\n        // Handle beforeunload event to warn user before leaving\r\n        window.addEventListener('beforeunload', (event) => {\r\n            if (this.isTestActive && !this.isReviewMode) {\r\n                const message = 'You are in the middle of a test. Are you sure you want to leave?';\r\n                event.returnValue = message;\r\n                return message;\r\n            }\r\n        });\r\n    }\r\n    \r\n    /**\r\n     * Start the test\r\n     */\r\n    startTest() {\r\n        if (!this.questions.length) {\r\n            alert('No questions available for this test.');\r\n            return;\r\n        }\r\n        \r\n        this.isTestActive = true;\r\n        this.currentQuestionIndex = 0;\r\n        this.userAnswers = {};\r\n        \r\n        // Start the timer\r\n        this.startTimer();\r\n        \r\n        // Show the first question\r\n        this.showCurrentQuestion();\r\n    }\r\n    \r\n    /**\r\n     * Start the timer\r\n     */\r\n    startTimer() {\r\n        // Reset timer\r\n        this.timeRemaining = this.timeLimit;\r\n        this.updateTimerDisplay();\r\n        \r\n        // Clear any existing interval\r\n        if (this.timerInterval) {\r\n            clearInterval(this.timerInterval);\r\n        }\r\n        \r\n        // Start new interval\r\n        const startTime = Date.now();\r\n        this.timerInterval = setInterval(() => {\r\n            const elapsedSeconds = Math.floor((Date.now() - startTime) / 1000);\r\n            this.timeRemaining = Math.max(0, this.timeLimit - elapsedSeconds);\r\n            \r\n            this.updateTimerDisplay();\r\n            \r\n            // If time is up, auto-submit the test\r\n            if (this.timeRemaining <= 0) {\r\n                clearInterval(this.timerInterval);\r\n                this.submitTest();\r\n            }\r\n        }, 1000);\r\n    }\r\n    \r\n    /**\r\n     * Update the timer display\r\n     */\r\n    updateTimerDisplay() {\r\n        // Calculate minutes and seconds\r\n        const minutes = Math.floor(this.timeRemaining / 60);\r\n        const seconds = this.timeRemaining % 60;\r\n        \r\n        // Update text display\r\n        if (this.timerMinutes) {\r\n            this.timerMinutes.textContent = minutes.toString().padStart(2, '0');\r\n        }\r\n        \r\n        if (this.timerSeconds) {\r\n            this.timerSeconds.textContent = seconds.toString().padStart(2, '0');\r\n        }\r\n        \r\n        // Update progress bar\r\n        if (this.timerBar) {\r\n            const percentRemaining = (this.timeRemaining / this.timeLimit) * 100;\r\n            this.timerBar.style.width = `${percentRemaining}%`;\r\n            \r\n            // Change color based on time remaining\r\n            if (percentRemaining < 20) {\r\n                this.timerBar.style.backgroundColor = '#ff4d4d'; // Red\r\n            } else if (percentRemaining < 50) {\r\n                this.timerBar.style.backgroundColor = '#ffa64d'; // Orange\r\n            } else {\r\n                this.timerBar.style.backgroundColor = '#4CAF50'; // Green\r\n            }\r\n        }\r\n    }\r\n    \r\n    /**\r\n     * Render question indicators for navigation\r\n     */\r\n    renderQuestionIndicators() {\r\n        if (!this.questionIndicators || !this.questions.length) return;\r\n        \r\n        this.questionIndicators.innerHTML = '';\r\n        \r\n        this.questions.forEach((_, index) => {\r\n            const indicator = document.createElement('div');\r\n            indicator.className = 'toeic-question-indicator';\r\n            indicator.dataset.index = index;\r\n            indicator.addEventListener('click', () => this.goToQuestion(index));\r\n            \r\n            this.questionIndicators.appendChild(indicator);\r\n        });\r\n    }\r\n    \r\n    /**\r\n     * Update question indicators based on answered status\r\n     */\r\n    updateQuestionIndicators() {\r\n        if (!this.questionIndicators) return;\r\n        \r\n        const indicators = this.questionIndicators.querySelectorAll('.toeic-question-indicator');\r\n        \r\n        indicators.forEach((indicator, index) => {\r\n            // Remove all classes first\r\n            indicator.classList.remove('current', 'answered', 'unanswered');\r\n            \r\n            // Add appropriate class\r\n            if (index === this.currentQuestionIndex) {\r\n                indicator.classList.add('current');\r\n            }\r\n            \r\n            const questionId = this.questions[index]?.id;\r\n            if (questionId && this.userAnswers[questionId]) {\r\n                indicator.classList.add('answered');\r\n            } else {\r\n                indicator.classList.add('unanswered');\r\n            }\r\n        });\r\n    }\r\n    \r\n    /**\r\n     * Show the current question\r\n     */\r\n    showCurrentQuestion() {\r\n        if (!this.questions.length) return;\r\n        \r\n        const question = this.questions[this.currentQuestionIndex];\r\n        if (!question) return;\r\n        \r\n        // Update question number display\r\n        if (this.currentQuestionDisplay) {\r\n            this.currentQuestionDisplay.textContent = (this.currentQuestionIndex + 1).toString();\r\n        }\r\n        \r\n        if (this.totalQuestionsDisplay) {\r\n            this.totalQuestionsDisplay.textContent = this.questions.length.toString();\r\n        }\r\n        \r\n        // Find section for this question\r\n        const section = this.sections.find(s => s.id === question.section_id);\r\n        \r\n        // Update section info if it's available and different from current\r\n        if (section) {\r\n            if (this.sectionTitle) {\r\n                this.sectionTitle.textContent = section.title || '';\r\n            }\r\n            \r\n            if (this.sectionInstructions) {\r\n                this.sectionInstructions.innerHTML = section.instructions || '';\r\n            }\r\n        }\r\n        \r\n        // Update question display\r\n        if (this.questionNumber) {\r\n            this.questionNumber.textContent = `Question ${this.currentQuestionIndex + 1}`;\r\n        }\r\n        \r\n        if (this.questionText) {\r\n            this.questionText.innerHTML = question.content || '';\r\n        }\r\n        \r\n        // Render question options\r\n        this.renderQuestionOptions(question);\r\n        \r\n        // Update navigation buttons\r\n        this.updateNavigationButtons();\r\n        \r\n        // Update question indicators\r\n        this.updateQuestionIndicators();\r\n    }\r\n    \r\n    /**\r\n     * Render options for the current question\r\n     * \r\n     * @param {Object} question - The question object\r\n     */\r\n    renderQuestionOptions(question) {\r\n        if (!this.questionOptions || !question) return;\r\n        \r\n        this.questionOptions.innerHTML = '';\r\n        \r\n        try {\r\n            // Use the factory to create the appropriate renderer\r\n            const renderer = QuestionRendererFactory.createRenderer(\r\n                question, \r\n                this.questionOptions, \r\n                {\r\n                    isReviewMode: this.isReviewMode,\r\n                    userAnswers: this.userAnswers,\r\n                    onAnswerSelected: this.saveAnswer.bind(this)\r\n                }\r\n            );\r\n            \r\n            // Render the question\r\n            renderer.render();\r\n        } catch (error) {\r\n            console.error('Error rendering question:', error);\r\n            \r\n            // Fallback for unsupported question types\r\n            this.renderFallbackQuestion(question);\r\n        }\r\n    }\r\n    \r\n    /**\r\n     * Fallback renderer for unsupported question types\r\n     * \r\n     * @param {Object} question - The question object\r\n     */\r\n    renderFallbackQuestion(question) {\r\n        const fallbackElement = document.createElement('div');\r\n        fallbackElement.className = 'toeic-question-fallback';\r\n        fallbackElement.innerHTML = `\r\n            <p class=\"toeic-question-unsupported\">\r\n                This question type (${question.type || 'unknown'}) is not supported in the current view.\r\n            </p>\r\n        `;\r\n        \r\n        this.questionOptions.appendChild(fallbackElement);\r\n    }\r\n    \r\n    /**\r\n     * Save the user's answer for a question\r\n     * \r\n     * @param {number|string} questionId - The question ID\r\n     * @param {string} answer - The selected answer\r\n     */\r\n    saveAnswer(questionId, answer) {\r\n        this.userAnswers[questionId] = answer;\r\n        this.updateQuestionIndicators();\r\n    }\r\n    \r\n    /**\r\n     * Update navigation buttons based on current question\r\n     */\r\n    updateNavigationButtons() {\r\n        // Previous button\r\n        if (this.prevButton) {\r\n            this.prevButton.disabled = this.currentQuestionIndex === 0;\r\n        }\r\n        \r\n        // Next button\r\n        if (this.nextButton) {\r\n            const isLastQuestion = this.currentQuestionIndex === this.questions.length - 1;\r\n            this.nextButton.style.display = isLastQuestion ? 'none' : 'inline-block';\r\n        }\r\n        \r\n        // Submit button is always visible with the new UI\r\n        // No need to toggle visibility based on question index\r\n    }\r\n    \r\n    /**\r\n     * Go to a specific question\r\n     * \r\n     * @param {number} index - Question index to navigate to\r\n     */\r\n    goToQuestion(index) {\r\n        if (index < 0 || index >= this.questions.length) return;\r\n        \r\n        this.currentQuestionIndex = index;\r\n        this.showCurrentQuestion();\r\n    }\r\n    \r\n    /**\r\n     * Go to the previous question\r\n     */\r\n    goToPreviousQuestion() {\r\n        if (this.currentQuestionIndex > 0) {\r\n            this.currentQuestionIndex--;\r\n            this.showCurrentQuestion();\r\n        }\r\n    }\r\n    \r\n    /**\r\n     * Go to the next question\r\n     */\r\n    goToNextQuestion() {\r\n        if (this.currentQuestionIndex < this.questions.length - 1) {\r\n            this.currentQuestionIndex++;\r\n            this.showCurrentQuestion();\r\n        }\r\n    }\r\n    \r\n    /**\r\n     * Confirm before submitting the test\r\n     */\r\n    confirmSubmitTest() {\r\n        // Count unanswered questions\r\n        const answeredCount = Object.keys(this.userAnswers).length;\r\n        const unansweredCount = this.questions.length - answeredCount;\r\n        \r\n        let message = 'Are you sure you want to submit your test?';\r\n        \r\n        if (unansweredCount > 0) {\r\n            message = `You have ${unansweredCount} unanswered question${unansweredCount > 1 ? 's' : ''}. Are you sure you want to submit your test?`;\r\n        }\r\n        \r\n        if (confirm(message)) {\r\n            this.submitTest();\r\n        }\r\n    }\r\n    \r\n    /**\r\n     * Submit the test\r\n     */\r\n    submitTest() {\r\n        // Stop the timer\r\n        if (this.timerInterval) {\r\n            clearInterval(this.timerInterval);\r\n        }\r\n        \r\n        this.isTestActive = false;\r\n        \r\n        // Show loader\r\n        this.loader.show('Submitting your test...');\r\n        \r\n        // Prepare data for submission\r\n        const data = {\r\n            test_id: this.testData.id,\r\n            time_taken: this.timeLimit - this.timeRemaining,\r\n            answers: this.userAnswers\r\n        };\r\n        \r\n        // Submit to server\r\n        testService.submitTest(this.testData.id, this.userAnswers, this.timeLimit - this.timeRemaining)\r\n            .then(response => {\r\n                this.showResults(response);\r\n            })\r\n            .catch(error => {\r\n                console.error('Error submitting test:', error);\r\n                alert('Failed to submit your test. Please try again.');\r\n            })\r\n            .finally(() => {\r\n                this.loader.hide();\r\n            });\r\n    }\r\n    \r\n    /**\r\n     * Show test results\r\n     * \r\n     * @param {Object} results - Test results from server\r\n     */\r\n    showResults(results) {\r\n        // Hide question container\r\n        if (this.questionContainer) {\r\n            this.questionContainer.style.display = 'none';\r\n        }\r\n        \r\n        // Show results container\r\n        if (this.resultsContainer) {\r\n            this.resultsContainer.style.display = 'block';\r\n        }\r\n        \r\n        // Hide navigation\r\n        const navigationElement = document.querySelector('.toeic-test-navigation');\r\n        if (navigationElement) {\r\n            navigationElement.style.display = 'none';\r\n        }\r\n        \r\n        // Render results content\r\n        if (this.resultsContent) {\r\n            const score = results.score || 0;\r\n            const totalPoints = results.total_points || this.questions.length;\r\n            const percentage = Math.round((score / totalPoints) * 100);\r\n            \r\n            let resultClass = 'average';\r\n            if (percentage >= 80) {\r\n                resultClass = 'excellent';\r\n            } else if (percentage >= 60) {\r\n                resultClass = 'good';\r\n            } else if (percentage < 40) {\r\n                resultClass = 'poor';\r\n            }\r\n            \r\n            this.resultsContent.innerHTML = `\r\n                <div class=\"toeic-score ${resultClass}\">\r\n                    <div class=\"toeic-score-number\">${score}/${totalPoints}</div>\r\n                    <div class=\"toeic-score-percentage\">${percentage}%</div>\r\n                </div>\r\n                \r\n                <div class=\"toeic-score-breakdown\">\r\n                    <h3>Score Breakdown</h3>\r\n                    <table class=\"toeic-score-table\">\r\n                        <thead>\r\n                            <tr>\r\n                                <th>Section</th>\r\n                                <th>Score</th>\r\n                                <th>Out of</th>\r\n                                <th>Percentage</th>\r\n                            </tr>\r\n                        </thead>\r\n                        <tbody>\r\n                            ${this.renderSectionScores(results.section_scores || [])}\r\n                        </tbody>\r\n                    </table>\r\n                </div>\r\n                \r\n                <div class=\"toeic-feedback\">\r\n                    <h3>Feedback</h3>\r\n                    <p>${results.feedback || 'No feedback available.'}</p>\r\n                </div>\r\n            `;\r\n        }\r\n    }\r\n    \r\n    /**\r\n     * Render section scores for the results table\r\n     * \r\n     * @param {Array} sectionScores - Array of section score objects\r\n     * @returns {string} HTML for the section scores table rows\r\n     */\r\n    renderSectionScores(sectionScores) {\r\n        if (!sectionScores || !sectionScores.length) {\r\n            return '<tr><td colspan=\"4\">No section scores available</td></tr>';\r\n        }\r\n        \r\n        return sectionScores.map(section => {\r\n            const percentage = Math.round((section.score / section.total) * 100);\r\n            return `\r\n                <tr>\r\n                    <td>${section.title}</td>\r\n                    <td>${section.score}</td>\r\n                    <td>${section.total}</td>\r\n                    <td>${percentage}%</td>\r\n                </tr>\r\n            `;\r\n        }).join('');\r\n    }\r\n    \r\n    /**\r\n     * Start review mode to review answers\r\n     */\r\n    startReviewMode() {\r\n        this.isReviewMode = true;\r\n        \r\n        // Show question container again\r\n        if (this.questionContainer) {\r\n            this.questionContainer.style.display = 'block';\r\n        }\r\n        \r\n        // Show navigation again but with modified buttons\r\n        const navigationElement = document.querySelector('.toeic-test-navigation');\r\n        if (navigationElement) {\r\n            navigationElement.style.display = 'block';\r\n            \r\n            // Hide submit button\r\n            if (this.submitButton) {\r\n                this.submitButton.style.display = 'none';\r\n            }\r\n            \r\n            // Show next button for all questions\r\n            if (this.nextButton) {\r\n                this.nextButton.style.display = 'inline-block';\r\n            }\r\n        }\r\n        \r\n        // Hide results\r\n        if (this.resultsContainer) {\r\n            this.resultsContainer.style.display = 'none';\r\n        }\r\n        \r\n        // Go to first question\r\n        this.currentQuestionIndex = 0;\r\n        this.showCurrentQuestion();\r\n    }\r\n}\r\n\r\n// Initialize the page when DOM is loaded\r\ndocument.addEventListener('DOMContentLoaded', () => {\r\n    const testDetailElement = document.getElementById('toeic-test-detail');\r\n    if (testDetailElement) {\r\n        const testDetailPage = new TestDetailPage(testDetailElement);\r\n        testDetailPage.init();\r\n    }\r\n});\r\n\r\nexport default TestDetailPage;", "/**\r\n * TOEIC Practice Plugin - Ajax Service\r\n * \r\n * A service class that provides a secure interface for making AJAX requests\r\n * to WordPress backend, handling nonces and other security aspects.\r\n */\r\nclass AjaxService {\r\n    /**\r\n     * Constructor\r\n     * \r\n     * @param {Object} options - Configuration options\r\n     * @param {string} options.ajaxUrl - WordPress AJAX URL (default: from global toeicPracticeAjax)\r\n     * @param {string} options.nonce - Security nonce (default: from global toeicPracticeAjax)\r\n     * @param {string} options.nonceParam - Name of nonce parameter (default: 'nonce')\r\n     * @param {Function} options.onError - Global error handler (optional)\r\n     */\r\n    constructor(options = {}) {\r\n        // Check if global toeicPracticeAjax is available\r\n        const globalData = window.toeicPracticeAjax || {};\r\n        \r\n        this.ajaxUrl = options.ajaxUrl || globalData.ajaxUrl || '/wp-admin/admin-ajax.php';\r\n        this.nonce = options.nonce || globalData.nonce || '';\r\n        this.nonceParam = options.nonceParam || 'nonce';\r\n        this.onError = options.onError || null;\r\n        \r\n        // Validate required parameters\r\n        if (!this.ajaxUrl) {\r\n            console.error('AjaxService: ajaxUrl is required');\r\n        }\r\n        \r\n        if (!this.nonce) {\r\n            console.warn('AjaxService: nonce is not provided, requests may fail security checks');\r\n        }\r\n        \r\n        // Keep track of pending requests\r\n        this.pendingRequests = new Map();\r\n    }\r\n    \r\n    /**\r\n     * Make a GET request\r\n     * \r\n     * @param {string} action - WordPress action name\r\n     * @param {Object} params - Additional parameters (optional)\r\n     * @param {Object} options - Request options (optional)\r\n     * @returns {Promise} - Promise that resolves with the response data\r\n     */\r\n    get(action, params = {}, options = {}) {\r\n        const requestParams = {\r\n            action,\r\n            [this.nonceParam]: this.nonce,\r\n            ...params\r\n        };\r\n        \r\n        const queryString = new URLSearchParams(requestParams).toString();\r\n        const url = `${this.ajaxUrl}?${queryString}`;\r\n        \r\n        return this._makeRequest(url, {\r\n            method: 'GET',\r\n            ...options\r\n        });\r\n    }\r\n    \r\n    /**\r\n     * Make a POST request\r\n     * \r\n     * @param {string} action - WordPress action name\r\n     * @param {Object} data - Data to send (optional)\r\n     * @param {Object} options - Request options (optional)\r\n     * @returns {Promise} - Promise that resolves with the response data\r\n     */\r\n    post(action, data = {}, options = {}) {\r\n        const formData = new FormData();\r\n        \r\n        // Add action and nonce\r\n        formData.append('action', action);\r\n        formData.append(this.nonceParam, this.nonce);\r\n        \r\n        // Add other data\r\n        Object.entries(data).forEach(([key, value]) => {\r\n            // Handle Blob objects (including File and audio blobs)\r\n            if (value instanceof Blob) {\r\n                formData.append(key, value, value.name || `${key}.blob`);\r\n            }\r\n            // Handle arrays and other objects (but not Blobs)\r\n            else if (typeof value === 'object' && value !== null) {\r\n                formData.append(key, JSON.stringify(value));\r\n            } else {\r\n                formData.append(key, value);\r\n            }\r\n        });\r\n        \r\n        return this._makeRequest(this.ajaxUrl, {\r\n            method: 'POST',\r\n            body: formData,\r\n            ...options\r\n        });\r\n    }\r\n    \r\n    /**\r\n     * Make a POST request with JSON data\r\n     * \r\n     * @param {string} action - WordPress action name\r\n     * @param {Object} data - Data to send (optional)\r\n     * @param {Object} options - Request options (optional)\r\n     * @returns {Promise} - Promise that resolves with the response data\r\n     */\r\n    postJson(action, data = {}, options = {}) {\r\n        const requestData = {\r\n            action,\r\n            [this.nonceParam]: this.nonce,\r\n            ...data\r\n        };\r\n        \r\n        return this._makeRequest(this.ajaxUrl, {\r\n            method: 'POST',\r\n            headers: {\r\n                'Content-Type': 'application/json'\r\n            },\r\n            body: JSON.stringify(requestData),\r\n            ...options\r\n        });\r\n    }\r\n    \r\n    /**\r\n     * Make a request with the fetch API\r\n     * \r\n     * @param {string} url - Request URL\r\n     * @param {Object} options - Request options\r\n     * @returns {Promise} - Promise that resolves with the response data\r\n     * @private\r\n     */\r\n    _makeRequest(url, options) {\r\n        // Generate a unique request ID\r\n        const requestId = Math.random().toString(36).substring(2, 15);\r\n        \r\n        // Create abort controller for timeout\r\n        const controller = new AbortController();\r\n        const { signal } = controller;\r\n        \r\n        // Set timeout if specified\r\n        const timeout = options.timeout || 30000; // Default 30 seconds\r\n        const timeoutId = setTimeout(() => {\r\n            controller.abort();\r\n        }, timeout);\r\n        \r\n        // Store the request in pending requests\r\n        this.pendingRequests.set(requestId, { controller });\r\n        \r\n        // Show loader if specified\r\n        const loader = options.loader || (window.toeicLoader || null);\r\n        const showLoader = options.showLoader !== false;\r\n        \r\n        if (loader && showLoader) {\r\n            loader.show(options.loaderText || 'Loading...');\r\n        }\r\n        \r\n        // Make the request\r\n        return fetch(url, {\r\n            ...options,\r\n            signal,\r\n            credentials: 'same-origin'\r\n        })\r\n        .then(response => {\r\n            // Clear timeout\r\n            clearTimeout(timeoutId);\r\n            \r\n            // Check if response is OK\r\n            if (!response.ok) {\r\n                throw new Error(`HTTP error ${response.status}: ${response.statusText}`);\r\n            }\r\n            \r\n            // Parse response based on content type\r\n            const contentType = response.headers.get('content-type');\r\n            if (contentType && contentType.includes('application/json')) {\r\n                return response.json();\r\n            }\r\n            \r\n            return response.text();\r\n        })\r\n        .then(data => {\r\n            // Handle WordPress AJAX response format\r\n            if (typeof data === 'object' && data !== null) {\r\n                // Check for WordPress error response\r\n                if (data.success === false) {\r\n                    throw new Error(data.data || 'Unknown error');\r\n                }\r\n                \r\n                // Return data or data.data if available\r\n                return data.data !== undefined ? data.data : data;\r\n            }\r\n            \r\n            return data;\r\n        })\r\n        .catch(error => {\r\n            // Handle aborted requests\r\n            if (error.name === 'AbortError') {\r\n                throw new Error('Request timed out');\r\n            }\r\n            \r\n            // Call global error handler if available\r\n            if (this.onError) {\r\n                this.onError(error);\r\n            }\r\n            \r\n            throw error;\r\n        })\r\n        .finally(() => {\r\n            // Remove from pending requests\r\n            this.pendingRequests.delete(requestId);\r\n            \r\n            // Hide loader\r\n            if (loader && showLoader) {\r\n                // Only hide if no other requests are pending with this loader\r\n                const otherRequestsWithLoader = Array.from(this.pendingRequests.values())\r\n                    .some(req => req.loader === loader);\r\n                \r\n                if (!otherRequestsWithLoader) {\r\n                    loader.hide();\r\n                }\r\n            }\r\n        });\r\n    }\r\n    \r\n    /**\r\n     * Cancel all pending requests\r\n     */\r\n    cancelAll() {\r\n        this.pendingRequests.forEach(request => {\r\n            request.controller.abort();\r\n        });\r\n        \r\n        this.pendingRequests.clear();\r\n    }\r\n    \r\n    /**\r\n     * Update the nonce\r\n     * \r\n     * @param {string} nonce - New nonce value\r\n     */\r\n    updateNonce(nonce) {\r\n        this.nonce = nonce;\r\n    }\r\n    \r\n    /**\r\n     * Create a new instance with custom settings\r\n     * \r\n     * @param {Object} options - Configuration options\r\n     * @returns {AjaxService} - New AjaxService instance\r\n     */\r\n    static create(options = {}) {\r\n        return new AjaxService(options);\r\n    }\r\n}\r\n\r\n// Create and export default instance\r\nconst ajaxService = new AjaxService();\r\n\r\nexport default ajaxService;\r\n\r\n// Also export the class for creating custom instances\r\nexport { AjaxService };\r\n", "/**\r\n * Pronunciation Service\r\n * Handles pronunciation grading and related API calls\r\n */\r\nimport ajaxService from './AjaxService.js';\r\n\r\nclass PronunciationService {\r\n    constructor() {\r\n        this.ajaxService = ajaxService;\r\n    }\r\n\r\n    /**\r\n     * Submit audio recording for pronunciation grading\r\n     * @param {Blob} audioBlob - The recorded audio blob\r\n     * @param {Object} params - Practice parameters\r\n     * @returns {Promise<Object>} - Grading results\r\n     */\r\n    async gradeRecording(audioBlob, params = {}) {\r\n        try {\r\n            // Create data object with audio file and parameters\r\n            const data = {\r\n                audio: audioBlob,\r\n                id: params.id || '',\r\n            };\r\n            \r\n            // Use AjaxService post method which handles FormData automatically\r\n            return await this.ajaxService.post('grade_pronunciation', data);\r\n            \r\n        } catch (error) {\r\n            console.error('Error grading recording:', error);\r\n            throw error;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Get pronunciation topics\r\n     * @returns {Promise<Array>} - List of pronunciation topics\r\n     */\r\n    async getTopics() {\r\n        try {\r\n            return await this.ajaxService.post('get_pronunciation_topics');\r\n        } catch (error) {\r\n            console.error('Error fetching topics:', error);\r\n            throw error;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Get topic items (questions and vocabulary)\r\n     * @param {number} topicId - Topic ID\r\n     * @returns {Promise<Array>} - List of topic items\r\n     */\r\n    async getTopicItems(topicId) {\r\n        try {\r\n            return await this.ajaxService.post('get_topic_items', {\r\n                topic_id: topicId\r\n            });\r\n        } catch (error) {\r\n            console.error('Error fetching topic items:', error);\r\n            throw error;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Save pronunciation practice result\r\n     * @param {Object} resultData - Practice result data\r\n     * @returns {Promise<Object>} - Save result\r\n     */\r\n    async saveResult(resultData) {\r\n        try {\r\n            return await this.ajaxService.post('save_pronunciation_result', {\r\n                result_data: JSON.stringify(resultData)\r\n            });\r\n        } catch (error) {\r\n            console.error('Error saving result:', error);\r\n            throw error;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Get user's pronunciation history\r\n     * @param {Object} filters - Filter options\r\n     * @returns {Promise<Array>} - List of practice results\r\n     */\r\n    async getHistory(filters = {}) {\r\n        try {\r\n            const data = {};\r\n            \r\n            // Add filters if provided\r\n            if (filters.topic_id) {\r\n                data.topic_id = filters.topic_id;\r\n            }\r\n            if (filters.limit) {\r\n                data.limit = filters.limit;\r\n            }\r\n            if (filters.offset) {\r\n                data.offset = filters.offset;\r\n            }\r\n            \r\n            return await this.ajaxService.post('get_pronunciation_history', data);\r\n        } catch (error) {\r\n            console.error('Error fetching history:', error);\r\n            throw error;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Get suggestions for pronunciation practice\r\n     * @returns {Promise<Array>} - List of suggestions\r\n     */\r\n    async getSuggestions({prompt, subject}) {\r\n        try {\r\n            let data = {\r\n                \"prompt\": prompt,\r\n                \"subject\": subject\r\n            }\r\n            return await this.ajaxService.post('get_pronunciation_suggestions', data);\r\n        } catch (error) {\r\n            console.error('Error fetching suggestions:', error);\r\n            throw error;\r\n        }\r\n    }\r\n}\r\n\r\n// Export for use in other modules\r\nexport default PronunciationService;\r\n", "/**\r\n * TOEIC Practice Plugin - REST Service\r\n * \r\n * A service class that provides a secure interface for making REST API requests\r\n * to WordPress backend, handling nonces and other security aspects.\r\n */\r\nclass RestService {\r\n    /**\r\n     * Constructor\r\n     * \r\n     * @param {Object} options - Configuration options\r\n     * @param {string} options.restUrl - WordPress REST API URL (default: from global toeicPracticeRest)\r\n     * @param {string} options.nonce - Security nonce (default: from global toeicPracticeRest)\r\n     * @param {Function} options.onError - Global error handler (optional)\r\n     */\r\n    constructor(options = {}) {\r\n        // Check if global toeicPracticeRest is available\r\n        const globalData = window.toeicPracticeRest || {};\r\n        \r\n        this.restUrl = options.restUrl || globalData.restUrl || '/wp-json/toeic-practice/v1';\r\n        this.nonce = options.nonce || globalData.restNonce || '';\r\n        this.onError = options.onError || null;\r\n        \r\n        // Validate required parameters\r\n        if (!this.restUrl) {\r\n            console.error('RestService: restUrl is required');\r\n        }\r\n        \r\n        if (!this.nonce) {\r\n            console.warn('RestService: nonce is not provided, requests may fail security checks');\r\n        }\r\n        \r\n        // Keep track of pending requests\r\n        this.pendingRequests = new Map();\r\n    }\r\n    \r\n    /**\r\n     * Make a GET request\r\n     * \r\n     * @param {string} endpoint - REST API endpoint\r\n     * @param {Object} params - Query parameters (optional)\r\n     * @param {Object} options - Request options (optional)\r\n     * @returns {Promise} - Promise that resolves with the response data\r\n     */\r\n    get(endpoint, params = {}, options = {}) {\r\n        // Build query string\r\n        const queryString = Object.keys(params).length \r\n            ? '?' + new URLSearchParams(params).toString() \r\n            : '';\r\n        \r\n        const url = `${this.restUrl}${endpoint}${queryString}`;\r\n        \r\n        return this._makeRequest(url, {\r\n            method: 'GET',\r\n            ...options\r\n        });\r\n    }\r\n    \r\n    /**\r\n     * Make a POST request\r\n     * \r\n     * @param {string} endpoint - REST API endpoint\r\n     * @param {Object} data - Data to send (optional)\r\n     * @param {Object} options - Request options (optional)\r\n     * @returns {Promise} - Promise that resolves with the response data\r\n     */\r\n    post(endpoint, data = {}, options = {}) {\r\n        const url = `${this.restUrl}${endpoint}`;\r\n        \r\n        return this._makeRequest(url, {\r\n            method: 'POST',\r\n            headers: {\r\n                'Content-Type': 'application/json'\r\n            },\r\n            body: JSON.stringify(data),\r\n            ...options\r\n        });\r\n    }\r\n    \r\n    /**\r\n     * Make a request with the fetch API\r\n     * \r\n     * @param {string} url - Request URL\r\n     * @param {Object} options - Request options\r\n     * @returns {Promise} - Promise that resolves with the response data\r\n     * @private\r\n     */\r\n    _makeRequest(url, options) {\r\n        // Generate a unique request ID\r\n        const requestId = Math.random().toString(36).substring(2, 15);\r\n        \r\n        // Create abort controller for timeout\r\n        const controller = new AbortController();\r\n        const { signal } = controller;\r\n        \r\n        // Set timeout if specified\r\n        const timeout = options.timeout || 30000; // Default 30 seconds\r\n        const timeoutId = setTimeout(() => {\r\n            controller.abort();\r\n        }, timeout);\r\n        \r\n        // Store the request in pending requests\r\n        this.pendingRequests.set(requestId, { controller });\r\n        \r\n        // Show loader if specified\r\n        const loader = options.loader || (window.toeicLoader || null);\r\n        const showLoader = options.showLoader !== false;\r\n        \r\n        if (loader && showLoader) {\r\n            loader.show(options.loaderText || 'Loading...');\r\n        }\r\n        \r\n        // Add nonce to headers\r\n        const headers = options.headers || {};\r\n        headers['X-WP-Nonce'] = this.nonce; // Use the REST API nonce\r\n\r\n        // Make the request\r\n        return fetch(url, {\r\n            ...options,\r\n            headers,\r\n            signal,\r\n            credentials: 'same-origin'\r\n        })\r\n        .then(response => {\r\n            // Clear timeout\r\n            clearTimeout(timeoutId);\r\n\r\n            // Check if response is OK\r\n            if (!response.ok) {\r\n                throw new Error(`HTTP error ${response.status}: ${response.statusText}`);\r\n            }\r\n            \r\n            // Parse response based on content type\r\n            const contentType = response.headers.get('content-type');\r\n            if (contentType && contentType.includes('application/json')) {\r\n                return response.json();\r\n            }\r\n            \r\n            return response.text();\r\n        })\r\n        .catch(error => {\r\n            // Handle aborted requests\r\n            if (error.name === 'AbortError') {\r\n                throw new Error('Request timed out');\r\n            }\r\n            \r\n            // Call global error handler if available\r\n            if (this.onError) {\r\n                this.onError(error);\r\n            }\r\n            \r\n            throw error;\r\n        })\r\n        .finally(() => {\r\n            // Remove from pending requests\r\n            this.pendingRequests.delete(requestId);\r\n            \r\n            // Hide loader\r\n            if (loader && showLoader) {\r\n                // Only hide if no other requests are pending with this loader\r\n                const otherRequestsWithLoader = Array.from(this.pendingRequests.values())\r\n                    .some(req => req.loader === loader);\r\n                \r\n                if (!otherRequestsWithLoader) {\r\n                    loader.hide();\r\n                }\r\n            }\r\n        });\r\n    }\r\n    \r\n    /**\r\n     * Cancel all pending requests\r\n     */\r\n    cancelAll() {\r\n        this.pendingRequests.forEach(request => {\r\n            request.controller.abort();\r\n        });\r\n        \r\n        this.pendingRequests.clear();\r\n    }\r\n    \r\n    /**\r\n     * Update the nonce\r\n     * \r\n     * @param {string} nonce - New nonce value\r\n     */\r\n    updateNonce(nonce) {\r\n        this.nonce = nonce;\r\n    }\r\n    \r\n    /**\r\n     * Create a new instance with custom settings\r\n     * \r\n     * @param {Object} options - Configuration options\r\n     * @returns {RestService} - New RestService instance\r\n     */\r\n    static create(options = {}) {\r\n        return new RestService(options);\r\n    }\r\n}\r\n\r\n// Create and export default instance\r\nconst restService = new RestService();\r\n\r\nexport default restService;\r\n\r\n// Also export the class for creating custom instances\r\nexport { RestService };\r\n", "/**\r\n * Test Service\r\n * \r\n * Handles all test-related REST API operations.\r\n * Uses the RestService for making requests to the server.\r\n */\r\n\r\nimport restService from './RestService';\r\n\r\nclass TestService {\r\n    /**\r\n     * Fetch test details by ID\r\n     * \r\n     * @param {number} testId - The ID of the test to fetch\r\n     * @returns {Promise} - Promise that resolves with test details\r\n     */\r\n    getTestDetails(testId) {\r\n        return restService.get(`/tests/${testId}`);\r\n    }\r\n    \r\n    /**\r\n     * Fetch test questions by test ID\r\n     * \r\n     * @param {number} testId - The ID of the test to fetch questions for\r\n     * @returns {Promise} - Promise that resolves with test questions\r\n     */\r\n    getTestQuestions(testId) {\r\n        return restService.get(`/tests/${testId}/questions`);\r\n    }\r\n    \r\n    /**\r\n     * Submit a completed test\r\n     * \r\n     * @param {number} testId - The ID of the test being submitted\r\n     * @param {Object} answers - Object containing question IDs as keys and selected answers as values\r\n     * @param {number} timeTaken - Time taken to complete the test in seconds\r\n     * @returns {Promise} - Promise that resolves with test results\r\n     */\r\n    submitTest(testId, answers, timeTaken) {\r\n        return restService.post(`/tests/${testId}/submit`, {\r\n            answers: answers,\r\n            time_taken: timeTaken\r\n        });\r\n    }\r\n    \r\n    /**\r\n     * Get all available tests\r\n     * \r\n     * @param {Object} filters - Optional filters for tests (e.g., category, difficulty)\r\n     * @returns {Promise} - Promise that resolves with list of available tests\r\n     */\r\n    getAvailableTests(filters = {}) {\r\n        return restService.get('/tests', filters);\r\n    }\r\n    \r\n    /**\r\n     * Get test results history for current user\r\n     * \r\n     * @param {number} limit - Optional limit for number of results to return\r\n     * @param {number} page - Optional page number for pagination\r\n     * @returns {Promise} - Promise that resolves with test results history\r\n     */\r\n    getTestResultsHistory(limit = 10, page = 1) {\r\n        return restService.get('/results', {\r\n            limit: limit,\r\n            page: page\r\n        });\r\n    }\r\n    \r\n    /**\r\n     * Get detailed test result by result ID\r\n     * \r\n     * @param {number} resultId - The ID of the test result to fetch\r\n     * @returns {Promise} - Promise that resolves with detailed test result\r\n     */\r\n    getTestResult(resultId) {\r\n        return restService.get(`/results/${resultId}`);\r\n    }\r\n    \r\n    /**\r\n     * Save user progress on a test (for resuming later)\r\n     * \r\n     * @param {number} testId - The ID of the test\r\n     * @param {Object} progress - Progress data including answers and current position\r\n     * @returns {Promise} - Promise that resolves with save status\r\n     */\r\n    saveTestProgress(testId, progress) {\r\n        return restService.post(`/tests/${testId}/progress`, {\r\n            progress: progress,\r\n            current_question: progress.currentQuestion || 0\r\n        });\r\n    }\r\n    \r\n    /**\r\n     * Load saved test progress\r\n     * \r\n     * @param {number} testId - The ID of the test to load progress for\r\n     * @returns {Promise} - Promise that resolves with saved progress data\r\n     */\r\n    loadTestProgress(testId) {\r\n        return restService.get(`/tests/${testId}/progress`);\r\n    }\r\n    \r\n    /**\r\n     * Get test statistics for a specific test\r\n     * \r\n     * @param {number} testId - The ID of the test\r\n     * @returns {Promise} - Promise that resolves with test statistics\r\n     */\r\n    getTestStatistics(testId) {\r\n        return restService.get(`/tests/${testId}/statistics`);\r\n    }\r\n    \r\n    /**\r\n     * Get section details by section ID\r\n     * \r\n     * @param {number} sectionId - The ID of the section to fetch\r\n     * @returns {Promise} - Promise that resolves with section details\r\n     */\r\n    getSectionDetails(sectionId) {\r\n        // This method can be implemented when needed\r\n        return Promise.resolve({});\r\n    }\r\n}\r\n\r\n// Create and export a singleton instance\r\nconst testService = new TestService();\r\nexport default testService;\r\n\r\n// Also export the class for potential extension\r\nexport { TestService };\r\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "/**\r\n * TOEIC Practice Plugin - Frontend Main Entry Point\r\n * \r\n * This file serves as the main entry point for the frontend JavaScript\r\n * functionality of the TOEIC Practice plugin.\r\n */\r\n\r\n// Import components\r\nimport Sidebar from './components/Sidebar';\r\nimport Loader from './components/Loader';\r\nimport ajaxService, { AjaxService } from './services/AjaxService';\r\nimport testService, { TestService } from './services/TestService';\r\nimport TestDetailPage from './pages/test-detail-page';\r\nimport { PronunciationRecorder } from './pages/pronunciation-page';\r\n\r\n// Initialize the application when DOM is fully loaded\r\ndocument.addEventListener('DOMContentLoaded', () => {\r\n    // Global loader instance for full-screen loading\r\n    window.toeicLoader = new Loader({\r\n        fullScreen: true\r\n    });\r\n    \r\n    // Make services available globally\r\n    window.toeicAjax = ajaxService;\r\n    window.toeicTest = testService;\r\n\r\n    // check if record button exists\r\n    const recordBtn = document.getElementById('record-btn');\r\n    if (recordBtn) {\r\n        window.toeicPronunciation = new PronunciationRecorder({paragraphText: recordBtn.dataset.paragraphText});\r\n    }\r\n\r\n    // Initialize sidebar\r\n    const sidebar = document.querySelector('.toeic-sidebar');\r\n    if (sidebar) {\r\n        const sidebar = new Sidebar(sidebar);\r\n        sidebar.init();\r\n    }\r\n});\r\n\r\n// Export components for potential reuse\r\nexport {\r\n    Sidebar,\r\n    Loader,\r\n    ajaxService,\r\n    AjaxService,\r\n    testService,\r\n    TestService,\r\n    TestDetailPage,\r\n};\r\n"], "names": ["Loader", "options", "arguments", "length", "undefined", "_classCallCheck", "containerId", "text", "size", "fullScreen", "container", "loader", "isVisible", "init", "_createClass", "key", "value", "document", "getElementById", "createElement", "id", "className", "classList", "add", "body", "append<PERSON><PERSON><PERSON>", "createLoader", "concat", "spinner", "textElement", "textContent", "hide", "show", "updateText", "style", "display", "querySelector", "newTextElement", "toggle", "showFor", "duration", "_this", "Promise", "resolve", "setTimeout", "forElement", "element", "console", "error", "uniqueId", "Math", "random", "toString", "substr", "computedStyle", "window", "getComputedStyle", "position", "_objectSpread", "Sidebar", "menuItems", "querySelectorAll", "sidebarToggle", "setupEventListeners", "highlightCurrentPage", "for<PERSON>ach", "item", "addEventListener", "handleMenuItemClick", "bind", "e", "stopPropagation", "handleSidebarToggle", "handleDocumentClick", "remove", "event", "currentTarget", "currentPath", "location", "pathname", "link", "getAttribute", "<PERSON><PERSON><PERSON><PERSON>", "Matching<PERSON><PERSON><PERSON>", "_Question<PERSON><PERSON><PERSON>", "_callSuper", "_inherits", "render", "clearContainer", "items", "question", "JSON", "parse", "matchingC<PERSON>r", "userAnswers", "getUserAnswer", "leftColumn", "rightColumn", "leftHeader", "<PERSON><PERSON><PERSON><PERSON>", "selected<PERSON><PERSON>t", "selectedRight", "totalPairs", "pairedCount", "index", "leftItem", "dataset", "itemId", "wordContainer", "wordText", "prompt", "audio_link", "audioButton", "innerHTML", "audio", "Audio", "play", "isReviewMode", "toggleLeftSelection", "checkForMatch", "rightItem", "response", "log", "toggleRightSelection", "submitButton", "disabled", "onSubmit", "addStyles", "showCorrectMatches", "showExistingAnswers", "styleEl", "head", "contains", "leftId", "rightId", "answer", "setUserAnswer", "checkAllPaired", "userAnswer", "leftCards", "rightCards", "leftCard", "Array", "from", "find", "card", "rightCard", "wrongRightCard", "shuffleResponses", "responses", "map", "i", "j", "floor", "_ref", "MultipleChoiceR<PERSON>er", "option", "optionElement", "createOptionElement", "_this2", "inputId", "isChecked", "correct_answer", "input", "onAnswerSelected", "constructor", "Error", "isAnswered", "createWrapper", "wrapper", "questionId", "questionType", "type", "TextInputR<PERSON>er", "QuestionRendererFactory", "<PERSON><PERSON><PERSON><PERSON>", "question_type", "toLowerCase", "hasRendererForType", "supportedTypes", "includes", "inputContainer", "placeholder", "reviewInfo", "isCorrect", "target", "t", "r", "Symbol", "n", "iterator", "o", "toStringTag", "c", "prototype", "Generator", "u", "Object", "create", "_regeneratorDefine2", "f", "p", "y", "G", "v", "a", "d", "l", "TypeError", "call", "done", "GeneratorFunction", "GeneratorFunctionPrototype", "getPrototypeOf", "setPrototypeOf", "__proto__", "displayName", "_regenerator", "w", "m", "defineProperty", "_regeneratorDefine", "enumerable", "configurable", "writable", "_invoke", "asyncGeneratorStep", "then", "_asyncToGenerator", "apply", "_next", "_throw", "_defineProperties", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_toPrimitive", "_typeof", "toPrimitive", "String", "Number", "PronunciationService", "PronunciationRecorder", "paragraphText", "isRecording", "mediaRecorder", "audioChunks", "startTime", "timerInterval", "pronunciationService", "recordBtn", "recordingStatus", "recordingDuration", "durationText", "suggestionBtns", "warn", "hideRecordingElements", "toggleRecording", "button", "handleSuggestion", "checkMicrophonePermissions", "_checkMicrophonePermissions", "_callee", "stream", "_t", "_context", "navigator", "mediaDevices", "getUserMedia", "getTracks", "track", "stop", "showError", "_toggleRecording", "_callee2", "_context2", "stopRecording", "startRecording", "_startRecording", "_callee3", "mimeType", "_t2", "_context3", "echoCancellation", "noiseSuppression", "sampleRate", "channelCount", "MediaRecorder", "isTypeSupported", "actualMimeType", "ondataavailable", "data", "push", "onstop", "processRecording", "start", "Date", "now", "showRecordingElements", "updateButtonState", "startTimer", "stopTimer", "_this3", "audioBlob", "Blob", "convertToWAV", "wavBlob", "submitForGrading", "_convertToWAV", "_callee5", "_this4", "_context5", "reject", "audioContext", "AudioContext", "webkitAudioContext", "fileReader", "FileReader", "onload", "_ref2", "_callee4", "arrayBuffer", "audioBuffer", "<PERSON><PERSON><PERSON><PERSON>", "_t3", "_context4", "result", "decodeAudioData", "trimSilence", "audioBufferToWav", "_x2", "onerror", "readAsA<PERSON>y<PERSON><PERSON>er", "_x", "silenceT<PERSON><PERSON>old", "marginMs", "numberOfChannels", "marginSamples", "audioData", "getChannelData", "startIndex", "abs", "max", "endIndex", "min", "<PERSON><PERSON><PERSON><PERSON>", "minimalBuffer", "createBuffer", "<PERSON><PERSON><PERSON><PERSON>", "channel", "originalData", "trimmedData", "originalDuration", "toFixed", "trimmedDuration", "trimmedStart", "trimmedEnd", "buffer", "targetSampleRate", "targetChannels", "bitsPerSample", "resampled<PERSON><PERSON><PERSON>", "resampleAndConvertToMono", "bytesPerSample", "blockAlign", "byteRate", "dataSize", "bufferSize", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "view", "DataView", "writeString", "offset", "string", "setUint8", "charCodeAt", "setUint32", "setUint16", "sample", "intSample", "setInt16", "originalSampleRate", "original<PERSON>ength", "resampleRatio", "resampledData", "Float32Array", "monoData", "sum", "originalIndex", "fraction", "_submitForGrading", "_callee6", "params", "_t4", "_context6", "showLoadingState", "topic_id", "topicItemData", "item_type", "gradeRecording", "displayResults", "message", "hideLoadingState", "_x3", "analysis", "scores", "itemData", "item_data", "overallScore", "round", "overall_score", "accuracy", "fluency", "completeness", "pronunciation", "expectedText", "expected_text", "word", "recognizedText", "recognized_text", "feedback", "detectedIssues", "detected_issues", "suggestions", "issuesHtml", "issue", "join", "suggestionsHtml", "suggestion", "practiceResult", "getScoreClass", "score", "recordText", "_this5", "setInterval", "elapsed", "minutes", "seconds", "timeString", "padStart", "clearInterval", "resetRecording", "getNonce", "toeic_ajax_nonce", "_this6", "subject", "getSuggestion", "showSuggestion", "_ref3", "title", "template", "suggestionsResult", "_getSuggestion", "_callee7", "_ref4", "_t5", "_context7", "getSuggestions", "_x4", "testService", "TestDetailPage", "testData", "toeicTestData", "questions", "sections", "currentQuestionIndex", "isTestActive", "timer<PERSON><PERSON>", "timerDisplay", "timerMinutes", "timerSeconds", "<PERSON><PERSON><PERSON><PERSON>", "sectionInfo", "sectionTitle", "sectionInstructions", "questionNumber", "questionText", "questionOptions", "prevButton", "nextButton", "questionIndicators", "currentQuestionDisplay", "totalQuestionsDisplay", "resultsContainer", "resultsContent", "reviewButton", "timeLimit", "time_limit", "timeRemaining", "loadQuestions", "getTestQuestions", "renderQuestionIndicators", "startTest", "alert", "goToPreviousQuestion", "goToNextQuestion", "confirmSubmitTest", "startReviewMode", "returnValue", "showCurrentQuestion", "updateTimerDisplay", "elapsedSeconds", "submitTest", "percentRemaining", "width", "backgroundColor", "_", "indicator", "goToQuestion", "updateQuestionIndicators", "indicators", "_this5$questions$inde", "section", "s", "section_id", "instructions", "content", "renderQuestionOptions", "updateNavigationButtons", "renderer", "saveAnswer", "renderFallbackQuestion", "fallbackElement", "isLastQuestion", "answeredCount", "keys", "unansweredCount", "confirm", "test_id", "time_taken", "answers", "showResults", "results", "navigationElement", "totalPoints", "total_points", "percentage", "resultClass", "renderSectionScores", "section_scores", "sectionScores", "total", "testDetailElement", "testDetailPage", "AjaxService", "globalData", "toeicPracticeAjax", "ajaxUrl", "nonce", "nonceParam", "onError", "pendingRequests", "Map", "get", "action", "requestParams", "_defineProperty", "queryString", "URLSearchParams", "url", "_makeRequest", "method", "post", "formData", "FormData", "append", "entries", "_slicedToArray", "name", "stringify", "post<PERSON><PERSON>", "requestData", "headers", "requestId", "substring", "controller", "AbortController", "signal", "timeout", "timeoutId", "abort", "set", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "loaderText", "fetch", "credentials", "clearTimeout", "ok", "status", "statusText", "contentType", "json", "success", "otherRequestsWithLoader", "values", "some", "req", "cancelAll", "request", "clear", "updateNonce", "ajaxService", "_gradeRecording", "_args", "_getTopics", "getTopics", "_getTopicItems", "topicId", "getTopicItems", "_saveResult", "resultData", "result_data", "saveResult", "_getHistory", "filters", "_args5", "limit", "getHistory", "_getSuggestions", "_t6", "RestService", "toeicPracticeRest", "restUrl", "restNonce", "endpoint", "restService", "TestService", "getTestDetails", "testId", "timeTaken", "getAvailableTests", "getTestResultsHistory", "page", "getTestResult", "resultId", "saveTestProgress", "progress", "current_question", "currentQuestion", "loadTestProgress", "getTestStatistics", "getSectionDetails", "sectionId", "toeicAjax", "toeicTest", "toeicPronunciation", "sidebar"], "sourceRoot": ""}