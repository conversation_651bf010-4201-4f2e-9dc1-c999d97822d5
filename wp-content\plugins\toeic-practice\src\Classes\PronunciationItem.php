<?php

namespace ToeicPractice\Classes;

use \ToeicPractice\Database\DatabaseManager as DB;

class PronunciationItem {
    public $topic_id;
    public $item;
    public $item_type;
    public $item_data; // Related item data

    public function __construct($id) {
        $model = DB::getInstance()->getPronunciationTopics();
        $this->item = $model->getTopicItem($id);
        $this->item_type = $this->item['item_type'];
        $this->item_data = $this->getItemData();
    }

    // Get item attribute by object property
    public function __get($name) {
        return isset($this->item[$name]) ? $this->item[$name] : null;
    }

    // Get item data
    public function getItemData() {
        $item_type = $this->item_type;
        if ($item_type == 'vocabulary') {
            $item_data = DB::getInstance()->getVocabulary($this->item_id);
        } elseif ($item_type == 'question') {
            $item_data = DB::getInstance()->getQuestion($this->item_id);
        }
        return $item_data;
    }

    // Get item content
    public function getContent() {
        $item_type = $this->item_type;
        if ($item_type == 'vocabulary') {
            return $this->item_data['word'];
        } elseif ($item_type == 'question') {
            return $this->item_data['content'];
        }
    }

    public function getAudio() {
        $item_type = $this->item_type;
        if ($item_type == 'vocabulary') {
            return $this->item_data['audio_link'];
        } elseif ($item_type == 'question') {
            return $this->item_data['media_url'];
        }
    }

    public function getItemType() {
        return $this->item_type;
    }
}